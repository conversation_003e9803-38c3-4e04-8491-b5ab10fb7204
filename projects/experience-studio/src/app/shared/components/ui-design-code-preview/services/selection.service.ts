import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { NodeManagementService, ScreenNode } from './node-management.service';
import { CanvasService } from './canvas.service';

// Selection rectangle interface
export interface SelectionRect {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Selection state interface
export interface SelectionState {
  isSelecting: boolean;
  selectionRect: SelectionRect | null;
  selectedNodeIds: string[];
  isDraggingSelection: boolean;
  dragStartPosition: { x: number; y: number } | null;
}

// Bulk operation interface
export interface BulkOperation {
  type: 'move' | 'resize' | 'delete' | 'duplicate';
  nodeIds: string[];
  data?: any;
}

@Injectable({
  providedIn: 'root'
})
export class SelectionService {
  // Default selection state
  private readonly DEFAULT_STATE: SelectionState = {
    isSelecting: false,
    selectionRect: null,
    selectedNodeIds: [],
    isDraggingSelection: false,
    dragStartPosition: null
  };

  // State subjects
  private selectionStateSubject = new BehaviorSubject<SelectionState>(this.DEFAULT_STATE);
  private lastClickTimeSubject = new BehaviorSubject<number>(0);
  private doubleClickThresholdSubject = new BehaviorSubject<number>(300); // ms

  // Public observables
  selectionState$: Observable<SelectionState> = this.selectionStateSubject.asObservable();
  lastClickTime$: Observable<number> = this.lastClickTimeSubject.asObservable();

  constructor(
    private nodeManagementService: NodeManagementService,
    private canvasService: CanvasService
  ) {}

  /**
   * Get current selection state
   */
  getSelectionState(): SelectionState {
    return this.selectionStateSubject.getValue();
  }

  /**
   * Update selection state
   */
  private updateSelectionState(updates: Partial<SelectionState>): void {
    const current = this.getSelectionState();
    this.selectionStateSubject.next({
      ...current,
      ...updates
    });
  }

  /**
   * Start selection rectangle
   */
  startSelection(startX: number, startY: number): void {
    this.updateSelectionState({
      isSelecting: true,
      selectionRect: {
        x: startX,
        y: startY,
        width: 0,
        height: 0
      }
    });
  }

  /**
   * Update selection rectangle
   */
  updateSelection(currentX: number, currentY: number): void {
    const state = this.getSelectionState();
    if (!state.isSelecting || !state.selectionRect) return;

    const rect = state.selectionRect;
    const newRect: SelectionRect = {
      x: Math.min(rect.x, currentX),
      y: Math.min(rect.y, currentY),
      width: Math.abs(currentX - rect.x),
      height: Math.abs(currentY - rect.y)
    };

    this.updateSelectionState({
      selectionRect: newRect
    });

    // Update node selection based on rectangle
    this.selectNodesInRectangle(newRect);
  }

  /**
   * End selection rectangle
   */
  endSelection(): void {
    this.updateSelectionState({
      isSelecting: false,
      selectionRect: null
    });
  }

  /**
   * Select nodes within rectangle
   */
  private selectNodesInRectangle(rect: SelectionRect): void {
    const nodes = this.nodeManagementService.getNodes();
    const selectedIds: string[] = [];

    nodes.forEach(node => {
      if (this.isNodeInRectangle(node, rect)) {
        selectedIds.push(node.id);
      }
    });

    this.updateSelectionState({
      selectedNodeIds: selectedIds
    });

    // Update node selection states
    this.updateNodeSelectionStates(selectedIds);
  }

  /**
   * Check if node intersects with selection rectangle
   */
  private isNodeInRectangle(node: ScreenNode, rect: SelectionRect): boolean {
    const nodeLeft = node.position.x;
    const nodeTop = node.position.y;
    const nodeRight = node.position.x + node.data.width;
    const nodeBottom = node.position.y + node.data.height;

    const rectLeft = rect.x;
    const rectTop = rect.y;
    const rectRight = rect.x + rect.width;
    const rectBottom = rect.y + rect.height;

    return !(nodeRight < rectLeft || 
             nodeLeft > rectRight || 
             nodeBottom < rectTop || 
             nodeTop > rectBottom);
  }

  /**
   * Select single node
   */
  selectNode(nodeId: string, multiSelect: boolean = false): void {
    const state = this.getSelectionState();
    let selectedIds: string[];

    if (multiSelect) {
      selectedIds = state.selectedNodeIds.includes(nodeId)
        ? state.selectedNodeIds.filter(id => id !== nodeId)
        : [...state.selectedNodeIds, nodeId];
    } else {
      selectedIds = [nodeId];
    }

    this.updateSelectionState({
      selectedNodeIds: selectedIds
    });

    this.updateNodeSelectionStates(selectedIds);
  }

  /**
   * Deselect node
   */
  deselectNode(nodeId: string): void {
    const state = this.getSelectionState();
    const selectedIds = state.selectedNodeIds.filter(id => id !== nodeId);

    this.updateSelectionState({
      selectedNodeIds: selectedIds
    });

    this.updateNodeSelectionStates(selectedIds);
  }

  /**
   * Clear all selections
   */
  clearSelection(): void {
    this.updateSelectionState({
      selectedNodeIds: [],
      selectionRect: null,
      isSelecting: false
    });

    this.updateNodeSelectionStates([]);
  }

  /**
   * Select all nodes
   */
  selectAll(): void {
    const nodes = this.nodeManagementService.getNodes();
    const allIds = nodes.map(node => node.id);

    this.updateSelectionState({
      selectedNodeIds: allIds
    });

    this.updateNodeSelectionStates(allIds);
  }

  /**
   * Update node selection states in node management service
   */
  private updateNodeSelectionStates(selectedIds: string[]): void {
    const nodes = this.nodeManagementService.getNodes();
    nodes.forEach(node => {
      this.nodeManagementService.updateNode(node.id, {
        selected: selectedIds.includes(node.id)
      });
    });
  }

  /**
   * Get selected nodes
   */
  getSelectedNodes(): ScreenNode[] {
    const state = this.getSelectionState();
    const nodes = this.nodeManagementService.getNodes();
    return nodes.filter(node => state.selectedNodeIds.includes(node.id));
  }

  /**
   * Get selection count
   */
  getSelectionCount(): number {
    return this.getSelectionState().selectedNodeIds.length;
  }

  /**
   * Check if node is selected
   */
  isNodeSelected(nodeId: string): boolean {
    return this.getSelectionState().selectedNodeIds.includes(nodeId);
  }

  /**
   * Handle node click with double-click detection
   */
  handleNodeClick(nodeId: string, event: MouseEvent): 'single' | 'double' {
    const currentTime = Date.now();
    const lastClickTime = this.lastClickTimeSubject.getValue();
    const threshold = this.doubleClickThresholdSubject.getValue();

    this.lastClickTimeSubject.next(currentTime);

    if (currentTime - lastClickTime < threshold) {
      // Double click
      return 'double';
    } else {
      // Single click
      const multiSelect = event.ctrlKey || event.metaKey || event.shiftKey;
      this.selectNode(nodeId, multiSelect);
      return 'single';
    }
  }

  /**
   * Start dragging selection
   */
  startDraggingSelection(startX: number, startY: number): void {
    this.updateSelectionState({
      isDraggingSelection: true,
      dragStartPosition: { x: startX, y: startY }
    });
  }

  /**
   * Update dragging selection
   */
  updateDraggingSelection(currentX: number, currentY: number): void {
    const state = this.getSelectionState();
    if (!state.isDraggingSelection || !state.dragStartPosition) return;

    const deltaX = currentX - state.dragStartPosition.x;
    const deltaY = currentY - state.dragStartPosition.y;

    // Move all selected nodes
    const selectedNodes = this.getSelectedNodes();
    selectedNodes.forEach(node => {
      const newPosition = {
        x: node.position.x + deltaX,
        y: node.position.y + deltaY
      };
      this.nodeManagementService.updateNodePosition(node.id, newPosition);
    });

    // Update drag start position for next delta calculation
    this.updateSelectionState({
      dragStartPosition: { x: currentX, y: currentY }
    });
  }

  /**
   * End dragging selection
   */
  endDraggingSelection(): void {
    this.updateSelectionState({
      isDraggingSelection: false,
      dragStartPosition: null
    });
  }

  /**
   * Perform bulk operation on selected nodes
   */
  performBulkOperation(operation: BulkOperation): void {
    const selectedNodes = this.getSelectedNodes();

    switch (operation.type) {
      case 'delete':
        this.deleteSelectedNodes();
        break;
      case 'duplicate':
        this.duplicateSelectedNodes();
        break;
      case 'move':
        this.moveSelectedNodes(operation.data);
        break;
      case 'resize':
        this.resizeSelectedNodes(operation.data);
        break;
    }
  }

  /**
   * Delete selected nodes
   */
  private deleteSelectedNodes(): void {
    const selectedIds = this.getSelectionState().selectedNodeIds;
    selectedIds.forEach(nodeId => {
      this.nodeManagementService.removeNode(nodeId);
    });
    this.clearSelection();
  }

  /**
   * Duplicate selected nodes
   */
  private duplicateSelectedNodes(): void {
    const selectedNodes = this.getSelectedNodes();
    const newNodeIds: string[] = [];

    selectedNodes.forEach(node => {
      const newNode = {
        ...node,
        id: this.nodeManagementService.generateNodeId(),
        position: {
          x: node.position.x + 50,
          y: node.position.y + 50
        }
      };

      this.nodeManagementService.addNode(newNode);
      newNodeIds.push(newNode.id);
    });

    // Select the duplicated nodes
    this.updateSelectionState({
      selectedNodeIds: newNodeIds
    });
    this.updateNodeSelectionStates(newNodeIds);
  }

  /**
   * Move selected nodes
   */
  private moveSelectedNodes(delta: { x: number; y: number }): void {
    const selectedNodes = this.getSelectedNodes();
    selectedNodes.forEach(node => {
      const newPosition = {
        x: node.position.x + delta.x,
        y: node.position.y + delta.y
      };
      this.nodeManagementService.updateNodePosition(node.id, newPosition);
    });
  }

  /**
   * Resize selected nodes
   */
  private resizeSelectedNodes(scale: { width: number; height: number }): void {
    const selectedNodes = this.getSelectedNodes();
    selectedNodes.forEach(node => {
      const newSize = {
        width: node.data.width * scale.width,
        height: node.data.height * scale.height
      };
      this.nodeManagementService.updateNodeSize(node.id, newSize);
    });
  }

  /**
   * Get selection bounds
   */
  getSelectionBounds(): { minX: number; minY: number; maxX: number; maxY: number } | null {
    const selectedNodes = this.getSelectedNodes();
    
    if (selectedNodes.length === 0) return null;

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    selectedNodes.forEach(node => {
      minX = Math.min(minX, node.position.x);
      minY = Math.min(minY, node.position.y);
      maxX = Math.max(maxX, node.position.x + node.data.width);
      maxY = Math.max(maxY, node.position.y + node.data.height);
    });

    return { minX, minY, maxX, maxY };
  }

  /**
   * Set double click threshold
   */
  setDoubleClickThreshold(threshold: number): void {
    this.doubleClickThresholdSubject.next(threshold);
  }
}
