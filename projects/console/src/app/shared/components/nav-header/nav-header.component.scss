::ng-deep .outer-box.light {
  background-color: transparent !important;
  box-shadow: none !important;
}

::ng-deep .container {
  background-color: transparent !important;
}

// Navigation menu styles
.nav-menu {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
  z-index: 950;
}

.nav-items {
  display: flex;
  align-items: center;
  gap: 12px;
  height: 100%;
}

.nav-item-wrapper {
  position: relative;
  z-index: 950;
}

.nav-item-container {
  position: relative;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  color: var(--nav-text, #666D99);
  cursor: pointer;
  transition: all 0.3s ease;

  // &:hover {
  //   background-color: var(--nav-hover, rgba(255, 255, 255, 0.1));
  // }

  &.selected {
    background-color: var(--nav-active, #fff);
    color: var(--nav-active-text, #000);
    box-shadow: 0 2px 4px var(--card-shadow, rgba(0, 0, 0, 0.1));
  }

  .item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: currentColor;
  }

  .dropdown-arrow {
    margin-left: 4px;
    transition: transform 0.2s ease;

    &.open {
      transform: rotate(180deg);
    }
  }
}

// Dropdown menu styles
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 280px;
  background-color: var(--dropdown-bg);
  border-radius: 8px;
  box-shadow: 0 4px 16px var(--dropdown-shadow);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 1000;
  margin-top: 8px;
  padding: 8px;

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--dropdown-hover-bg);
  }

  .dropdown-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--nav-item-color);
    flex-shrink: 0;
  }

  .dropdown-item-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .dropdown-item-title {
    font-weight: 500;
    font-size: 16px;
    color: var(--dropdown-text);
  }

  .dropdown-item-desc {
    font-size: 14px;
    color: var(--text-secondary);
  }
}

// Theme toggle icon and user profile styling
.header-right-content {
  img {
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }
}

@media (max-width: 400px) {
  ::ng-deep .outer-box .center-content-wrapper {
    display: none !important;
  }
}
