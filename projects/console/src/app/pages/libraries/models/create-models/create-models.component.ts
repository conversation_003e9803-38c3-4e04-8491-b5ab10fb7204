import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormControl } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CardComponent } from '../../../../shared/components/card/card.component';
import { FormFieldComponent } from '../../../../shared/components/form-field/form-field.component';

@Component({
  selector: 'app-create-models',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardComponent,
    FormFieldComponent
  ],
  templateUrl: './create-models.component.html',
  styleUrls: ['./create-models.component.scss']
})
export class CreateModelsComponent implements OnInit {
  modelId: string | null = null;
  isEditMode: boolean = false;
  modelForm: FormGroup;
  
  // Available AI engines and models
  aiEngines = [
    { value: 'google', label: 'Google AI' },
    { value: 'anthropic', label: 'Anthropic' },
    { value: 'azure', label: 'Azure' },
    { value: 'amazon', label: 'Amazon' }
  ];
  
  modelNames = [
    { value: 'gemini-1.5-pro-001', label: 'Gemini-1.5-pro-001' },
    { value: 'gemini-1.0-pro', label: 'Gemini-1.0-pro' },
    { value: 'claude-3-opus', label: 'Claude-3-opus' },
    { value: 'claude-3-sonnet', label: 'Claude-3-sonnet' }
  ];
  
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.modelForm = this.fb.group({
      // Model details
      name: [''],
      description: [''],
      
      // Filters
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],
      
      // Model configuration
      modelType: ['Generative'],
      gcpProjectId: [''],
      gcpLocation: [''],
      
      // New dropdown selections
      aiEngine: ['google'],
      modelName: ['gemini-1.5-pro-001']
    });
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.modelId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.modelId;
    
    if (this.isEditMode && this.modelId) {
      // In a real app, you would fetch the model data by ID
      console.log(`Editing model with ID: ${this.modelId}`);
      // this.loadModelData(this.modelId);
    }
    
    // Setup change listeners for the dropdowns
    this.modelForm.get('aiEngine')?.valueChanges.subscribe(engine => {
      console.log('Selected AI Engine:', engine);
      this.updateModelNameOptions(engine);
    });
    
    this.modelForm.get('modelName')?.valueChanges.subscribe(model => {
      console.log('Selected Model Name:', model);
      // You can prefill fields based on model selection
      this.updateModelDetails(model);
    });
  }

  // Update available model names based on selected engine
  updateModelNameOptions(engine: string): void {
    // In a real app, you would filter models based on the selected engine
    console.log(`Updating models for engine: ${engine}`);
  }
  
  // Update form fields based on selected model
  updateModelDetails(modelName: string): void {
    // In a real app, you would set default values based on the selected model
    console.log(`Updating details for model: ${modelName}`);
    
    // Example: Set a default model name in the form
    if (modelName) {
      this.modelForm.get('name')?.setValue(`My ${modelName} Model`);
    }
  }

  onSave(): void {
    console.log('Form data:', this.modelForm.value);
    
    if (this.isEditMode) {
      console.log('Updating existing model');
      // this.modelService.updateModel(this.modelId, this.modelForm.value);
    } else {
      console.log('Creating new model');
      // this.modelService.createModel(this.modelForm.value);
    }
    
    this.router.navigate(['/libraries/models']);
  }

  onExit(): void {
    this.router.navigate(['/libraries/models']);
  }
  
  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.modelForm.get(name) as FormControl;
  }
} 