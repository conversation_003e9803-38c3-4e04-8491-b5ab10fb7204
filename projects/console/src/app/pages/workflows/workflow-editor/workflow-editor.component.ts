import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Child, ElementRef, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormControl } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { FormFieldComponent } from '../../../shared/components/form-field/form-field.component';
import { WorkflowGraphService, WorkflowNode, WorkflowEdge } from './services/workflow-graph.service';
import { ReactFlowService } from './services/react-flow.service';
import { AgentNodeComponent } from './components/agent-node/agent-node.component';
import { Subscription } from 'rxjs';
import { Agent } from './models/agent.model';
import { DragDropModule, CdkDragDrop } from '@angular/cdk/drag-drop';
import { CardComponent } from '../../../shared/components/card/card.component'; 

interface TempConnection {
  isActive: boolean;
  sourceNodeId?: string;
  targetNodeId?: string;
  sourceHandleType?: 'source' | 'target';
  sourceX?: number;
  sourceY?: number;
  targetX?: number;
  targetY?: number;
}

interface CanvasViewport {
  zoom: number;
  x: number;
  y: number;
  isDragging: boolean;
  lastMouseX: number;
  lastMouseY: number;
}

interface NodePosition {
  x: number;
  y: number;
}

interface NodeConnectionPoints {
  [nodeId: string]: {
    left: { x: number; y: number };
    right: { x: number; y: number };
  };
}

@Component({
  selector: 'app-workflow-editor',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    FormFieldComponent,
    AgentNodeComponent,
    DragDropModule,
    CardComponent
  ],
  templateUrl: './workflow-editor.component.html',
  styleUrls: ['./workflow-editor.component.scss']
})
export class WorkflowEditorComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('reactFlowCanvas') reactFlowCanvas!: ElementRef;
  
  workflowId: string | null = null;
  isEditMode: boolean = false;
  workflowForm: FormGroup;
  
  // Agent library
  agents: Agent[] = [
    { 
      id: 'agent1', 
      name: 'Test Agent Agent Agent Agent Agent Agent',
      description: 'Translating user needs into actionable development tasks.',
      type: 'Individual',
      capabilities: ['Code Generation', 'Translation'] 
    },
    { 
      id: 'agent2', 
      name: 'Test Agent 2',
      description: 'Processing data and generating insights.',
      type: 'Individual',
      capabilities: ['Data Analysis', 'Visualization']
    },
    { 
      id: 'agent3', 
      name: 'Test Agent 3',
      description: 'Handling complex natural language processing tasks.',
      type: 'Collaborative',
      capabilities: ['NLP', 'Summarization', 'Translation']
    }
  ];
  
  // Filtered agents for search
  filteredAgents: Agent[] = [];
  
  // Available agents (filtered and not used yet)
  availableAgents: Agent[] = [];
  
  // Workflow nodes and edges
  nodes: WorkflowNode[] = [];
  edges: WorkflowEdge[] = [];
  
  // Flow initialized flag - always false since we're using our custom implementation
  flowInitialized = false;
  
  // Selected node
  selectedNodeId: string | null = null;
  
  // Temp connection for drawing
  tempConnection: TempConnection = {
    isActive: false
  };
  
  // Node connection points cache
  nodeConnectionPoints: NodeConnectionPoints = {};
  
  // Canvas viewport state
  viewport: CanvasViewport = {
    zoom: 1,
    x: 0,
    y: 0,
    isDragging: false,
    lastMouseX: 0,
    lastMouseY: 0
  };
  
  // Subscriptions
  private subscriptions: Subscription[] = [];
  
  // Track used agent IDs
  usedAgentIds: Set<string> = new Set();
  
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private workflowGraphService: WorkflowGraphService,
    private reactFlowService: ReactFlowService,
    private cdr: ChangeDetectorRef
  ) {
    this.workflowForm = this.fb.group({
      // Workflow details
      name: [''],
      description: [''],
      
      // Enable Manager LLM
      enableManagerLLM: [false],
      
      // LLM Configuration settings
      temperature: [0.3],
      topP: [0.95],
      maxRPM: [0],
      maxToken: [4000],
      maxIteration: [1],
      maxExecutionTime: [30],
      
      // Search filter
      agentFilter: ['']
    });
    
    // Initialize filtered agents
    this.filteredAgents = [...this.agents];
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.workflowId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.workflowId;
    
    if (this.isEditMode && this.workflowId) {
      // In a real app, you would fetch the workflow data by ID
      console.log(`Editing workflow with ID: ${this.workflowId}`);
      // this.loadWorkflowData(this.workflowId);
    }
    
    // Subscribe to nodes to track used agents
    this.subscriptions.push(
      this.workflowGraphService.nodes$.subscribe(nodes => {
        this.nodes = nodes;
        
        // Update used agent IDs
        this.updateUsedAgentIds();
        
        // Update node connection points cache after a short delay
        if (this.reactFlowCanvas) {
          setTimeout(() => this.updateNodeConnectionPoints(), 50);
        }
      })
    );
    
    this.subscriptions.push(
      this.workflowGraphService.edges$.subscribe(edges => {
        this.edges = edges;
      })
    );
    
    // Subscribe to the search filter changes
    this.subscriptions.push(
      this.getControl('agentFilter').valueChanges.subscribe(filterValue => {
        this.filterAgents(filterValue);
      })
    );
  }
  
  ngAfterViewInit(): void {
    // Always use our custom implementation
    setTimeout(() => {
      this.updateNodeConnectionPoints();
    }, 500);
    
    // Add event listeners for canvas navigation
    this.setupCanvasNavigation();
  }
  
  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
  
// Setup canvas navigation event handlers
private setupCanvasNavigation(): void {
  // Add key event listeners for keyboard shortcuts
  const element = this.reactFlowCanvas?.nativeElement;
  if (element) {
    // Add keyboard listener for reset viewport (e.g., spacebar)
    document.addEventListener('keydown', (event) => {
      if (event.key === ' ') { // Spacebar
        this.resetViewport();
        event.preventDefault();
      }
    });
      
    // When zooming or panning completes, update nodes
    element.addEventListener('mouseup', () => {
      if (this.viewport.isDragging) {
        // Update connection points after panning
        this.updateNodeConnectionPoints();
      }
    });
  }
}
  
  // Reset viewport to default
  private resetViewport(): void {
    this.viewport = {
      zoom: 1,
      x: 0,
      y: 0,
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0
    };
    
    // Update connections after viewport change
    this.updateNodeConnectionPoints();
    this.cdr.detectChanges();
  }
  
  filterAgents(filterValue: string): void {
    // First filter by search term
    if (!filterValue || filterValue.trim() === '') {
      this.filteredAgents = [...this.agents];
    } else {
      filterValue = filterValue.toLowerCase().trim();
      this.filteredAgents = this.agents.filter(agent => 
        agent.name.toLowerCase().includes(filterValue) || 
        agent.description.toLowerCase().includes(filterValue) ||
        (agent.type && agent.type.toLowerCase().includes(filterValue)) ||
        (agent.capabilities && agent.capabilities.some(cap => cap.toLowerCase().includes(filterValue)))
      );
    }
    
    // Then filter out agents that are already used
    this.updateAvailableAgents();
  }
  
  updateAvailableAgents(): void {
    this.availableAgents = this.filteredAgents.filter(agent => 
      !this.usedAgentIds.has(agent.id)
    );
  }
  
  onNodesChange(changes: any[]): void {
    // No longer needed, handled by our custom implementation
  }
  
  onEdgesChange(changes: any[]): void {
    // No longer needed, handled by our custom implementation
  }
  
  onConnect(connection: any): void {
    // No longer needed, handled by our custom implementation
  }
  
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  }
  
  onDrop(event: DragEvent): void {
    event.preventDefault();
    
    if (event.dataTransfer) {
      const agentData = event.dataTransfer.getData('application/reactflow');
      
      if (agentData) {
        try {
          const agent = JSON.parse(agentData);
          // Get drop position relative to the canvas
          const reactFlowBounds = this.reactFlowCanvas.nativeElement.getBoundingClientRect();
          
          // Calculate the position in untransformed coordinates
          const position = {
            x: (event.clientX - reactFlowBounds.left - this.viewport.x) / this.viewport.zoom,
            y: (event.clientY - reactFlowBounds.top - this.viewport.y) / this.viewport.zoom
          };
          
          // Ensure position is within canvas bounds and not negative
          const safePosition = {
            x: Math.max(0, position.x),
            y: Math.max(0, position.y)
          };
          
          // Create a new node for the agent
          const newNode: WorkflowNode = {
            id: this.workflowGraphService.generateNodeId(),
            type: 'agent',
            data: {
              label: agent.name,
              agentId: agent.id,
              agentName: agent.name,
              description: agent.description,
              capabilities: agent.capabilities,
              width: 200 // Default width
            },
            position: safePosition
          };
          
          this.workflowGraphService.addNode(newNode);
          
          // Update connection points immediately
          this.updateNodeConnectionPoints();
        } catch (error) {
          console.error('Error adding node:', error);
        }
      }
    }
  }

  onDragStart(event: DragEvent, agent: Agent): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/reactflow', JSON.stringify(agent));
      event.dataTransfer.effectAllowed = 'move';
    }
  }
  
  onDeleteNode(nodeId: string): void {
    this.workflowGraphService.removeNode(nodeId);
    
    // If the deleted node was selected, clear selection
    if (this.selectedNodeId === nodeId) {
      this.selectedNodeId = null;
    }
  }
  
  onNodeSelected(nodeId: string): void {
    this.selectedNodeId = nodeId;
  }
  
  onMoveNode(data: {nodeId: string, position: NodePosition}): void {
    // Find the node index
    const nodeIndex = this.nodes.findIndex(node => node.id === data.nodeId);
    if (nodeIndex === -1) return;
    
    // Create a new array with the updated node
    const updatedNodes = [...this.nodes];
    updatedNodes[nodeIndex] = {
      ...this.nodes[nodeIndex],
      position: data.position
    };
    
    // Update the node positions through service
    this.workflowGraphService.updateNodePositions(updatedNodes);
    
    // Force a change detection cycle
    this.cdr.detectChanges();
    
    // Update connection points immediately - don't wait for timeout
    this.updateNodeConnectionPoints();
  }
  
  onStartConnection(data: {nodeId: string, handleType: 'source' | 'target', event: MouseEvent}): void {
    // Find the node in our nodes array
    const node = this.nodes.find(n => n.id === data.nodeId);
    if (!node) return;
    
    // Find the position of the handle in untransformed coordinates
    // This is based on the node's current position
    let handleX, handleY;
    
    // Get node position and width
    const nodePos = node.position;
    const nodeWidth = node.data.width || 200;
    
    // Calculate handle position based on the node position
    // (these are in the untransformed coordinate system)
    if (data.handleType === 'source') {
      // Right handle
      handleX = nodePos.x + nodeWidth;
      handleY = nodePos.y + 30; // Approximately middle of the node
    } else {
      // Left handle
      handleX = nodePos.x;
      handleY = nodePos.y + 30; // Approximately middle of the node
    }
    
    // Get canvas rect
    const canvasRect = this.reactFlowCanvas.nativeElement.getBoundingClientRect();
    
    // Convert mouse position to untransformed coordinates
    const targetX = (data.event.clientX - canvasRect.left - this.viewport.x) / this.viewport.zoom;
    const targetY = (data.event.clientY - canvasRect.top - this.viewport.y) / this.viewport.zoom;
    
    // Set up the temporary connection
    this.tempConnection = {
      isActive: true,
      sourceNodeId: data.nodeId,
      sourceHandleType: data.handleType,
      sourceX: handleX,
      sourceY: handleY,
      targetX,
      targetY
    };
  }
  
  onResizeNode(data: {nodeId: string, width: number}): void {
    // Find the node
    const nodeIndex = this.nodes.findIndex(node => node.id === data.nodeId);
    if (nodeIndex === -1) return;
    
    // Create a new array with the updated node
    const updatedNodes = [...this.nodes];
    updatedNodes[nodeIndex] = {
      ...this.nodes[nodeIndex],
      data: {
        ...this.nodes[nodeIndex].data,
        width: data.width
      }
    };
    
    // Update nodes
    this.workflowGraphService.updateNodePositions(updatedNodes);
    
    // Update connection points
    requestAnimationFrame(() => {
      this.updateNodeConnectionPoints();
    });
  }
  
  updateNodePosition(data: {nodeId: string, position: {x: number, y: number}}): void {
    // Update the connection points for this node
    this.updateNodeConnectionPoints();
  }
  
  onCanvasMouseMove(event: MouseEvent): void {
    // Handle canvas panning
    if (this.viewport.isDragging) {
      const deltaX = event.clientX - this.viewport.lastMouseX;
      const deltaY = event.clientY - this.viewport.lastMouseY;
      
      this.viewport.x += deltaX;
      this.viewport.y += deltaY;
      
      this.viewport.lastMouseX = event.clientX;
      this.viewport.lastMouseY = event.clientY;
      
      // Force change detection to update the canvas transform
      this.cdr.detectChanges();
      return;
    }
    
    if (!this.tempConnection.isActive) return;
    
    // Update the temporary connection endpoint
    const canvasRect = this.reactFlowCanvas.nativeElement.getBoundingClientRect();
    
    // Calculate position in untransformed coordinates 
    // (since we'll transform the entire SVG)
    const targetX = (event.clientX - canvasRect.left - this.viewport.x) / this.viewport.zoom;
    const targetY = (event.clientY - canvasRect.top - this.viewport.y) / this.viewport.zoom;
    
    this.tempConnection = {
      ...this.tempConnection,
      targetX,
      targetY
    };
    
    // Force change detection to update the temporary connection path
    this.cdr.detectChanges();
  }
  
  onCanvasWheel(event: WheelEvent): void {
    event.preventDefault();
    
    // Calculate zoom change
    const delta = -event.deltaY;
    const zoomSpeed = 0.001;
    const newZoom = Math.max(0.1, Math.min(2, this.viewport.zoom + delta * zoomSpeed));
    
    if (newZoom !== this.viewport.zoom) {
      // Get mouse position relative to the canvas
      const rect = this.reactFlowCanvas.nativeElement.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;
      
      // Calculate new position to zoom towards mouse cursor
      const zoomRatio = newZoom / this.viewport.zoom;
      const newX = mouseX - (mouseX - this.viewport.x) * zoomRatio;
      const newY = mouseY - (mouseY - this.viewport.y) * zoomRatio;
      
      // Update viewport
      this.viewport.zoom = newZoom;
      this.viewport.x = newX;
      this.viewport.y = newY;
      
      // Update connection points to reflect the new zoom level
      this.updateNodeConnectionPoints();
      
      // Force change detection to update the canvas transform
      this.cdr.detectChanges();
    }
  }
  
  onCanvasMouseDown(event: MouseEvent): void {
    // Start canvas dragging when middle mouse button is pressed or Alt+left click
    if (event.button === 1 || (event.button === 0 && event.altKey)) {
      this.viewport.isDragging = true;
      this.viewport.lastMouseX = event.clientX;
      this.viewport.lastMouseY = event.clientY;
      event.preventDefault();
    }
  }
  
  onCanvasMouseUp(event: MouseEvent): void {
    // End canvas dragging
    this.viewport.isDragging = false;
    
    if (!this.tempConnection.isActive) return;
    
    // Check if we're over a node handle
    const target = event.target as HTMLElement;
    const handleElement = target.closest('.handle');
    
    if (handleElement && this.tempConnection.sourceNodeId) {
      const sourceNodeId = this.tempConnection.sourceNodeId;
      const sourceHandleType = this.tempConnection.sourceHandleType;
      
      // Get the node from the handle element
      const nodeElement = handleElement.closest('.agent-node');
      if (nodeElement) {
        const targetNodeId = nodeElement.getAttribute('data-node-id');
        const isLeftHandle = handleElement.classList.contains('handle-left');
        const targetHandleType = isLeftHandle ? 'target' : 'source';
        
        // Make sure we're connecting source to target (right to left)
        if (targetNodeId && sourceNodeId !== targetNodeId &&
            ((sourceHandleType === 'source' && targetHandleType === 'target') ||
             (sourceHandleType === 'target' && targetHandleType === 'source'))) {
          
          // Determine which node is the source and which is the target
          let source = sourceNodeId;
          let target = targetNodeId;
          
          // If the connection started from a target handle, swap source and target
          if (sourceHandleType === 'target') {
            source = targetNodeId;
            target = sourceNodeId;
          }
          
          // Create a new edge
          const newEdge: WorkflowEdge = {
            id: this.workflowGraphService.generateEdgeId(source, target),
            source,
            target,
            animated: true
          };
          
          // Add the edge
          this.workflowGraphService.addEdge(newEdge);
        }
      }
    }
    
    // Reset the temporary connection
    this.tempConnection = { isActive: false };
  }
  
  updateNodeConnectionPoints(): void {
    // Clear the cache
    this.nodeConnectionPoints = {};
    
    // Calculate connection points based on node positions and dimensions
    // This uses the untransformed coordinate system
    for (const node of this.nodes) {
      const nodePos = node.position;
      const nodeWidth = node.data.width || 200;
      
      // Calculate connection points
      const leftPoint = {
        x: nodePos.x,
        y: nodePos.y + 30 // Approximately middle of the node
      };
      
      const rightPoint = {
        x: nodePos.x + nodeWidth,
        y: nodePos.y + 30 // Approximately middle of the node
      };
      
      // Store the points
      this.nodeConnectionPoints[node.id] = {
        left: leftPoint,
        right: rightPoint
      };
    }
    
    // Force update to ensure edge paths are redrawn
    this.cdr.detectChanges();
  }
  
  getEdgePath(edge: WorkflowEdge): string {
    // Make sure connection points are up-to-date
    if (Object.keys(this.nodeConnectionPoints).length === 0) {
      this.updateNodeConnectionPoints();
    }
    
    const sourcePoints = this.nodeConnectionPoints[edge.source];
    const targetPoints = this.nodeConnectionPoints[edge.target];
    
    if (!sourcePoints || !targetPoints) {
      return '';
    }
    
    const sourceX = sourcePoints.right.x;
    const sourceY = sourcePoints.right.y;
    const targetX = targetPoints.left.x;
    const targetY = targetPoints.left.y;
    
    // Calculate distance between points
    const dx = Math.abs(targetX - sourceX);
    
    // Adjust the control points based on distance
    const offset = Math.min(80, dx * 0.75);
    
    // Create a bezier curve
    const controlPointX1 = sourceX + offset;
    const controlPointX2 = targetX - offset;
    
    return `M ${sourceX} ${sourceY} C ${controlPointX1} ${sourceY}, ${controlPointX2} ${targetY}, ${targetX} ${targetY}`;
  }
  
  getTempConnectionPath(): string {
    if (!this.tempConnection.isActive || 
        this.tempConnection.sourceX === undefined || 
        this.tempConnection.sourceY === undefined ||
        this.tempConnection.targetX === undefined ||
        this.tempConnection.targetY === undefined) {
      return '';
    }
    
    // Source and target positions are already in untransformed coordinates
    const sourceX = this.tempConnection.sourceX;
    const sourceY = this.tempConnection.sourceY;
    const targetX = this.tempConnection.targetX;
    const targetY = this.tempConnection.targetY;
    
    // Calculate distance between points
    const dx = Math.abs(targetX - sourceX);
    
    // Adjust the control points based on distance
    const offset = Math.min(80, dx * 0.75);
    
    // Create a bezier curve
    const controlPointX1 = sourceX + offset;
    const controlPointX2 = targetX - offset;
    
    return `M ${sourceX} ${sourceY} C ${controlPointX1} ${sourceY}, ${controlPointX2} ${targetY}, ${targetX} ${targetY}`;
  }
  
  onSave(): void {
    const workflowData = {
      ...this.workflowForm.value,
      nodes: this.nodes,
      edges: this.edges
    };
    
    console.log('Saving workflow:', workflowData);
    
    if (this.isEditMode) {
      console.log('Updating existing workflow');
      // this.workflowService.updateWorkflow(this.workflowId, workflowData);
    } else {
      console.log('Creating new workflow');
      // this.workflowService.createWorkflow(workflowData);
    }
    
    this.router.navigate(['/launch/workflows']);
  }

  onExit(): void {
    this.router.navigate(['/launch/workflows']);
  }
  
  onReset(): void {
    this.workflowGraphService.clearWorkflow();
    this.selectedNodeId = null;
    this.tempConnection = { isActive: false };
  }
  
  onExecute(): void {
    console.log('Executing workflow');
    // Navigate to workflow execution page
    if (this.workflowId) {
      this.router.navigate(['/launch/workflows/execute', this.workflowId]);
    } else {
      // For new workflows, save first then navigate
      alert('Please save the workflow before executing it.');
    }
  }
  
  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.workflowForm.get(name) as FormControl;
  }

  /**
   * Check if an agent is already used in the workflow
   * @param agentId ID of the agent to check
   */
  isAgentUsed(agentId: string): boolean {
    return this.usedAgentIds.has(agentId);
  }

  /**
   * Update the set of used agent IDs
   */
  updateUsedAgentIds(): void {
    this.usedAgentIds.clear();
    this.nodes.forEach(node => {
      if (node.data && node.data.agentId) {
        this.usedAgentIds.add(node.data.agentId);
      }
    });
    
    // Update available agents whenever used agents change
    this.updateAvailableAgents();
  }
} 