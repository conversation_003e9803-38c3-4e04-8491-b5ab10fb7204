#main-content-container {
  // Experience Cards Styles (from elder-wand)
  .exp-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    margin: 0 auto;
    padding: 20px;
    max-width: 1000px;

    .exp-card {
      border-radius: 16px;
      overflow: hidden;
      cursor: pointer;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: 1px solid var(--code-viewer-border) !important;
      background-color: var(--code-viewer-bg) !important;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      height: 280px;
      position: relative;
      // Border is now applied inline for theme support

      // Styles for disabled cards
      &.disabled-card {
        cursor: not-allowed;
        opacity: 0.7;
        filter: grayscale(50%);

        &:hover {
          transform: none;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 16px;
        padding: 2px;
        background: linear-gradient(90deg, #8C65F7 0%, #E84393 100%);
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        z-index: 1;
      }

      &:hover {
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);

        &::before {
          opacity: 1;
        }

        .exp-arrow-button {
          background: linear-gradient(90deg, #8C65F7 0%, #E84393 100%);
          img {
            transform: translateX(4px);
          }
        }
      }

      .card-content {
        display: flex;
        justify-content: space-between;
        padding: 24px;
        height: 100%;

        .text-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          padding-right: 24px;

          h2 {
            color: #1D1D1D;
            font-family: Mulish, sans-serif;
            font-size: 28px;
            font-style: normal;
            font-weight: 700;
            line-height: 120%;
            margin-bottom: 16px;
          }

          .description {
            color: #595959;
            font-family: Mulish, sans-serif;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 150%;
            margin-bottom: 24px;
          }

          .exp-arrow-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            img {
              width: 20px;
              height: 20px;
              transition: transform 0.3s ease;
            }
          }
        }

        .image-container {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            max-width: 100%;
            height: auto;
            object-fit: contain;
          }
        }
      }
    }
  }

  /*
   * PREVIOUS CARD DESIGN: These are the styles for the previous card designs used in the landing page
   * Keeping them commented for reference in case we need to revert back
   */
  /*
  .flip-card {
    height: 266px;
    perspective: 1000px;
    &.flipped .flip-card-inner {
      transform: rotateY(180deg);
    }
    .flip-card-inner {
      height: 100%;
      transition: transform 1s;
      transform-style: preserve-3d;
      .flip-card-front,
      .flip-card-back {
        width: 100%;
        position: absolute;
        backface-visibility: hidden;
      }
      .flip-card-back {
        transform: rotateY(180deg);
        .back-description {
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }
      }
    }
  }
  */
}
.container-fluid {
  margin-top:7%;
}

#divider-section-container {
  margin-top: 2rem;
  margin-bottom: 1rem;

  .divider-image {
    width: 80%;
    max-width: 800px;
    height: auto;
  }
}

#recent-projects-section {
  width: 100%;
  padding: 1rem 0;
}
/*
 * PREVIOUS CARD DESIGN: Additional styles for the previous card designs
 * Keeping them commented for reference in case we need to revert back
 */
/*
.card-back {
  border-radius: 6px;
  background: var(
    --Elevation-Dark-Fill-Dark-60,
    linear-gradient(102deg, rgba(20, 27, 31, 0.24) 2.05%, rgba(20, 27, 31, 0.24) 100%)
  );
  box-shadow: 0px -25px 125px -50px rgba(164, 7, 78, 0.5) inset;
  backdrop-filter: blur(7.5px);
}

:host ::ng-deep awe-cards {
  width: 100% !important;
}

:host ::ng-deep .awe-card--medium {
  padding: none !important;
  border-radius: 8px !important;
  background-color: var(--feature-card-bg);
}

:host ::ng-deep .awe-card--image-right .awe-card__media {
  width: 66%;
}

:host ::ng-deep .awe-card--info {
  text-align: start;
  padding-right: 10px !important;
}

::ng-deep button.primary {
  background: var(--feature-card-btn);
  border-radius: 6px;
  width: 81px;
}

::ng-deep awe-cards {
  width: 370px !important;
  height: 266px !important;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
}

:host ::ng-deep .awe-card--light {
  background: var(--feature-card-bg);
  color: var(--feature-card-text-color);
  border: 1.5px solid var(--feature-card-border-front);
  backdrop-filter: blur(7.5px);
}

awe-cards p {
  font-size: 14px;
  width: 80%;
}
*/

// border-radius: 8px;
// border: 2px solid var(--Pink-P-400, #F63B8F);
// box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.10);

/* Tablet */
@media (max-width: 1024px) {
  #main-content-container {
    .exp-grid {
      grid-template-columns: repeat(2, 1fr);
      padding: 15px;

      .exp-card {
        height: 260px;

        .card-content {
          .text-content {
            h2 {
              font-size: 24px;
            }

            .description {
              font-size: 14px;
            }
          }
        }
      }
    }

    .flip-card {
      width: 320px;
      height: 240px;
    }
  }
}

/* Mobile */
@media (max-width: 767px) {
  #main-content-container {
    .exp-grid {
      grid-template-columns: 1fr;
      padding: 10px;

      .exp-card {
        height: 240px;

        .card-content {
          .text-content {
            h2 {
              font-size: 22px;
              margin-bottom: 10px;
            }

            .description {
              font-size: 14px;
              margin-bottom: 16px;
            }
          }
        }
      }
    }

    .flip-card {
      width: 90%;
      height: 220px;
    }
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  #main-content-container {
    .exp-grid {
      gap: 16px;

      .exp-card {
        height: 220px;

        .card-content {
          padding: 16px;

          .text-content {
            h2 {
              font-size: 20px;
            }

            .description {
              font-size: 12px;
            }

            .exp-arrow-button {
              width: 32px;
              height: 32px;
            }
          }
        }
      }
    }

    .flip-card {
      width: 100%;
      height: 200px;
    }
  }
}

@media (min-width: 1420px) {
  .col-xl-6 {
    max-width: 25%;
  }

  #main-content-container {
    .exp-grid {
      max-width: 1200px;
    }
  }
}
