import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import SearchBar from '../../../shared/components/search-bar/search-bar.component';
import { CreateCardComponent } from '../../../shared/components/create-card/create-card.component';
import { DataCardComponent } from '../../../shared/components/data-card/data-card.component';
import { CardData } from '../../../shared/models/card.model';
import { MOCK_MODELS } from '../../../shared/mock-data/model-mock-data';
import { FilterBarComponent } from '../../../shared/components/filter-bar/filter-bar.component';
import { FilterService } from '../../../shared/services/filter.service';
import { FilterConfig } from '../../../shared/models/filter.model';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';

@Component({
  selector: 'app-models',
  standalone: true,
  imports: [
    CommonModule, 
    SearchBar, 
    CreateCardComponent, 
    DataCardComponent, 
    FilterBarComponent,
    PageFooterComponent
  ],
  templateUrl: './models.component.html',
  styleUrl: './models.component.scss'
})
export class ModelsComponent implements OnInit {
  // Get models from mock data
  allModels: CardData[] = MOCK_MODELS;
  filteredModels: CardData[] = [];
  displayedModels: CardData[] = [];
  modelFilterConfig!: FilterConfig;
  
  // Track filter bar visibility
  isFilterBarVisible: boolean = false;
  
  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 12; // Show 12 cards per page
  totalPages: number = 1;
  
  // Map filter IDs to CardData properties
  private filterPropertyMap: {[key: string]: string} = {
    'modelType': 'userType',
    'provider': 'client',
    'category': 'department',
    'capability': 'role',
    'series': 'project',
    'license': 'category'
  };

  constructor(
    private filterService: FilterService,
    private paginationService: PaginationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Get the filter configuration for models
    this.modelFilterConfig = this.filterService.getFilterConfig('models');
    this.filteredModels = [...this.allModels];
    this.updateDisplayedModels();
  }

  updateDisplayedModels(): void {
    // Use the pagination service to get displayed items and total pages
    const result = this.paginationService.getPaginatedItems(
      this.filteredModels,
      this.currentPage,
      this.itemsPerPage
    );
    
    this.displayedModels = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreateModel(): void {
    console.log('Create Model clicked');
    // Navigate to model creation page
    this.router.navigate(['/libraries/models/create']);
  }

  onCardClicked(modelId: string): void {
    console.log(`Model card clicked: ${modelId}`);
    // Navigate to edit model page
    this.router.navigate([`/libraries/models/edit/${modelId}`]);
  }

  onActionClicked(event: {action: string, cardId: string}): void {
    console.log(`Action ${event.action} clicked for model: ${event.cardId}`);
    // Implement your action click logic here
  }

  toggleFilterBar(): void {
    this.isFilterBarVisible = !this.isFilterBarVisible;
    console.log('Filter bar visibility:', this.isFilterBarVisible);
  }

  onFilterChange(filters: {[key: string]: string}): void {
    // Apply filters to models
    if (Object.keys(filters).length === 0) {
      this.filteredModels = [...this.allModels];
    } else {
      this.filteredModels = this.filterService.filterData(
        this.allModels, 
        filters, 
        this.filterPropertyMap
      );
    }
    
    // Reset to first page when filters change
    this.currentPage = 1;
    this.updateDisplayedModels();
    
    console.log('Applied filters:', filters);
    console.log('Filtered models count:', this.filteredModels.length);
  }
  
  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedModels();
  }
  
  // Helper to check if we should show the create card (only on first page)
  get showCreateCard(): boolean {
    return this.currentPage === 1;
  }
}
