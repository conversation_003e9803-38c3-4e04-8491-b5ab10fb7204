import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { createLogger } from '../utils/logger';
import { FileModel } from '../components/code-viewer/code-viewer.component';

export interface FileVersion {
  id: string;
  timestamp: number;
  source: 'baseline' | 'user-edit' | 'ai-regenerated';
  content: string;
  metadata: {
    fileSize: number;
    lastModified: number;
    isCurrentVersion: boolean;
    parentVersionId?: string;
    changeDescription?: string;
  };
}

export interface FileVersionHistory {
  filePath: string;
  versions: FileVersion[];
  currentVersionId: string;
  lastUserEditId?: string;
  lastAiGeneratedId?: string;
}

export interface FileTreeSnapshot {
  id: string;
  timestamp: number;
  files: FileModel[];
  metadata: {
    totalFiles: number;
    totalSize: number;
    lastModified: number;
    version: number;
  };
}

export interface FileComparisonResult {
  updated: FileModel[];
  added: FileModel[];
  unchanged: FileModel[];
  totalProcessed: number;
}

export interface VersionManagementResult {
  fileVersions: Map<string, FileVersionHistory>;
  conflictingFiles: string[];
  preservedUserEdits: string[];
  overwrittenFiles: string[];
}

@Injectable({
  providedIn: 'root'
})
export class FileTreePersistenceService {
  private logger = createLogger('FileTreePersistenceService');

  // Current baseline file tree state
  private baselineSnapshot$ = new BehaviorSubject<FileTreeSnapshot | null>(null);

  // Current working file tree state
  private currentSnapshot$ = new BehaviorSubject<FileTreeSnapshot | null>(null);

  // Version management for individual files
  private fileVersionHistories$ = new BehaviorSubject<Map<string, FileVersionHistory>>(new Map());

  // User edit tracking
  private userEditedFiles$ = new BehaviorSubject<Set<string>>(new Set());

  // Version counter for tracking changes
  private versionCounter = 1;

  // Storage keys
  private readonly BASELINE_STORAGE_KEY = 'file_tree_baseline';
  private readonly CURRENT_STORAGE_KEY = 'file_tree_current';
  private readonly VERSION_STORAGE_KEY = 'file_tree_version';
  private readonly VERSION_HISTORIES_KEY = 'file_version_histories';
  private readonly USER_EDITED_FILES_KEY = 'user_edited_files';

  constructor() {
    this.loadPersistedState();
  }

  /**
   * Public observables for components to subscribe to
   */
  get baselineSnapshot(): Observable<FileTreeSnapshot | null> {
    return this.baselineSnapshot$.asObservable();
  }

  get currentSnapshot(): Observable<FileTreeSnapshot | null> {
    return this.currentSnapshot$.asObservable();
  }

  get fileVersionHistories(): Observable<Map<string, FileVersionHistory>> {
    return this.fileVersionHistories$.asObservable();
  }

  get userEditedFiles(): Observable<Set<string>> {
    return this.userEditedFiles$.asObservable();
  }

  /**
   * Initialize baseline file tree from initial code generation
   * This should be called when code is first generated
   */
  initializeBaseline(files: FileModel[]): FileTreeSnapshot {
    this.logger.info('🏗️ Initializing baseline file tree');

    const snapshot = this.createSnapshot(files, 'baseline');
    this.baselineSnapshot$.next(snapshot);
    this.currentSnapshot$.next({ ...snapshot });

    this.persistState();

    this.logger.info(`✅ Baseline initialized with ${files.length} files (version ${snapshot.metadata.version})`);
    return snapshot;
  }

  /**
   * Update current file tree state after edit/regenerate operations
   * This maintains the baseline while updating the current state
   */
  updateCurrentState(files: FileModel[]): FileTreeSnapshot {
    this.logger.info('🔄 Updating current file tree state');

    const snapshot = this.createSnapshot(files, 'current');
    this.currentSnapshot$.next(snapshot);

    this.persistState();

    this.logger.info(`✅ Current state updated with ${files.length} files (version ${snapshot.metadata.version})`);
    return snapshot;
  }

  /**
   * Compare incoming edit/regenerate files against current baseline
   * Returns detailed comparison results
   */
  compareAgainstBaseline(incomingFiles: { fileName: string; content: string }[]): FileComparisonResult {
    const currentSnapshot = this.currentSnapshot$.value;

    if (!currentSnapshot) {
      this.logger.warn('⚠️ No baseline available for comparison, treating all files as new');
      return {
        updated: [],
        added: incomingFiles.map(f => this.convertToFileModel(f)),
        unchanged: [],
        totalProcessed: incomingFiles.length
      };
    }

    return this.performDetailedComparison(currentSnapshot.files, incomingFiles);
  }

  /**
   * Get current file tree for Monaco editor
   */
  getCurrentFiles(): FileModel[] {
    const snapshot = this.currentSnapshot$.value;
    return snapshot ? [...snapshot.files] : [];
  }

  /**
   * Get baseline file tree
   */
  getBaselineFiles(): FileModel[] {
    const snapshot = this.baselineSnapshot$.value;
    return snapshot ? [...snapshot.files] : [];
  }

  /**
   * Reset to baseline state (useful for reverting changes)
   */
  resetToBaseline(): FileTreeSnapshot | null {
    const baseline = this.baselineSnapshot$.value;
    if (baseline) {
      const resetSnapshot = this.createSnapshot(baseline.files, 'reset');
      this.currentSnapshot$.next(resetSnapshot);
      this.persistState();
      this.logger.info('🔄 Reset current state to baseline');
      return resetSnapshot;
    }
    return null;
  }

  /**
   * Promote current state to new baseline
   * This should be called after successful edit/regenerate operations
   */
  promoteToBaseline(): void {
    const current = this.currentSnapshot$.value;
    if (current) {
      const newBaseline = this.createSnapshot(current.files, 'promoted');
      this.baselineSnapshot$.next(newBaseline);
      this.persistState();
      this.logger.info('⬆️ Promoted current state to new baseline');
    }
  }

  /**
   * Record user edit for a specific file
   * This creates a new version in the file's history
   */
  recordUserEdit(filePath: string, content: string, changeDescription?: string): FileVersion {
    this.logger.info(`✏️ Recording user edit for: ${filePath}`);

    const normalizedPath = this.normalizeFilePath(filePath);
    const versionHistories = this.fileVersionHistories$.value;
    const userEditedFiles = this.userEditedFiles$.value;

    // Create new version
    const newVersion: FileVersion = {
      id: `user_edit_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      timestamp: Date.now(),
      source: 'user-edit',
      content,
      metadata: {
        fileSize: content.length,
        lastModified: Date.now(),
        isCurrentVersion: true,
        changeDescription: changeDescription || 'User edit in Monaco Editor'
      }
    };

    // Get or create version history for this file
    let fileHistory = versionHistories.get(normalizedPath);
    if (!fileHistory) {
      fileHistory = {
        filePath: normalizedPath,
        versions: [],
        currentVersionId: newVersion.id,
        lastUserEditId: newVersion.id
      };
    } else {
      // Mark previous version as not current
      const currentVersion = fileHistory.versions.find(v => v.metadata.isCurrentVersion);
      if (currentVersion) {
        currentVersion.metadata.isCurrentVersion = false;
        newVersion.metadata.parentVersionId = currentVersion.id;
      }

      fileHistory.currentVersionId = newVersion.id;
      fileHistory.lastUserEditId = newVersion.id;
    }

    // Add new version to history
    fileHistory.versions.push(newVersion);

    // Update maps
    versionHistories.set(normalizedPath, fileHistory);
    userEditedFiles.add(normalizedPath);

    // Emit updates
    this.fileVersionHistories$.next(versionHistories);
    this.userEditedFiles$.next(userEditedFiles);

    // Persist state
    this.persistVersionState();

    this.logger.info(`✅ User edit recorded for ${filePath} (version: ${newVersion.id})`);
    return newVersion;
  }

  /**
   * Process AI regeneration response with version management
   * This handles the complex logic of preserving user edits vs accepting AI changes
   */
  processAiRegeneration(
    incomingFiles: { fileName: string; content: string }[],
    preserveUserEdits: boolean = true
  ): VersionManagementResult {
    this.logger.info(`🤖 Processing AI regeneration for ${incomingFiles.length} files (preserve user edits: ${preserveUserEdits})`);

    const versionHistories = this.fileVersionHistories$.value;
    const userEditedFiles = this.userEditedFiles$.value;
    const result: VersionManagementResult = {
      fileVersions: new Map(versionHistories),
      conflictingFiles: [],
      preservedUserEdits: [],
      overwrittenFiles: []
    };

    for (const incomingFile of incomingFiles) {
      const normalizedPath = this.normalizeFilePath(incomingFile.fileName);
      const hasUserEdits = userEditedFiles.has(normalizedPath);
      const fileHistory = versionHistories.get(normalizedPath);

      if (hasUserEdits && preserveUserEdits && fileHistory) {
        // User has made edits - preserve them and record AI version as alternative
        this.logger.info(`🔄 Preserving user edits for: ${normalizedPath}`);

        const aiVersion: FileVersion = {
          id: `ai_regen_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          timestamp: Date.now(),
          source: 'ai-regenerated',
          content: incomingFile.content,
          metadata: {
            fileSize: incomingFile.content.length,
            lastModified: Date.now(),
            isCurrentVersion: false, // Don't make it current - preserve user edit
            changeDescription: 'AI regeneration (not applied due to user edits)'
          }
        };

        fileHistory.versions.push(aiVersion);
        fileHistory.lastAiGeneratedId = aiVersion.id;
        result.fileVersions.set(normalizedPath, fileHistory);
        result.preservedUserEdits.push(normalizedPath);
        result.conflictingFiles.push(normalizedPath);

      } else {
        // No user edits or user chose to overwrite - apply AI changes
        this.logger.info(`🤖 Applying AI regeneration for: ${normalizedPath}`);

        const aiVersion: FileVersion = {
          id: `ai_regen_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          timestamp: Date.now(),
          source: 'ai-regenerated',
          content: incomingFile.content,
          metadata: {
            fileSize: incomingFile.content.length,
            lastModified: Date.now(),
            isCurrentVersion: true,
            changeDescription: 'AI regeneration applied'
          }
        };

        let updatedHistory = fileHistory;
        if (!updatedHistory) {
          updatedHistory = {
            filePath: normalizedPath,
            versions: [],
            currentVersionId: aiVersion.id,
            lastAiGeneratedId: aiVersion.id
          };
        } else {
          // Mark previous version as not current
          const currentVersion = updatedHistory.versions.find(v => v.metadata.isCurrentVersion);
          if (currentVersion) {
            currentVersion.metadata.isCurrentVersion = false;
            aiVersion.metadata.parentVersionId = currentVersion.id;
          }

          updatedHistory.currentVersionId = aiVersion.id;
          updatedHistory.lastAiGeneratedId = aiVersion.id;
        }

        updatedHistory.versions.push(aiVersion);
        result.fileVersions.set(normalizedPath, updatedHistory);
        result.overwrittenFiles.push(normalizedPath);

        // Remove from user edited files if AI version is applied
        userEditedFiles.delete(normalizedPath);
      }
    }

    // Update state
    this.fileVersionHistories$.next(result.fileVersions);
    this.userEditedFiles$.next(userEditedFiles);
    this.persistVersionState();

    this.logger.info(`✅ AI regeneration processed: ${result.preservedUserEdits.length} preserved, ${result.overwrittenFiles.length} overwritten, ${result.conflictingFiles.length} conflicts`);
    return result;
  }

  /**
   * Get current content for a file (from current version)
   */
  getCurrentFileContent(filePath: string): string | null {
    const normalizedPath = this.normalizeFilePath(filePath);
    const versionHistories = this.fileVersionHistories$.value;
    const fileHistory = versionHistories.get(normalizedPath);

    if (!fileHistory) {
      return null;
    }

    const currentVersion = fileHistory.versions.find(v => v.metadata.isCurrentVersion);
    return currentVersion ? currentVersion.content : null;
  }

  /**
   * Get version history for a specific file
   */
  getFileVersionHistory(filePath: string): FileVersionHistory | null {
    const normalizedPath = this.normalizeFilePath(filePath);
    return this.fileVersionHistories$.value.get(normalizedPath) || null;
  }

  /**
   * Check if a file has user edits
   */
  hasUserEdits(filePath: string): boolean {
    const normalizedPath = this.normalizeFilePath(filePath);
    return this.userEditedFiles$.value.has(normalizedPath);
  }

  /**
   * Clear all persisted state
   */
  clearPersistedState(): void {
    this.logger.info('🗑️ Clearing all persisted file tree state');

    sessionStorage.removeItem(this.BASELINE_STORAGE_KEY);
    sessionStorage.removeItem(this.CURRENT_STORAGE_KEY);
    sessionStorage.removeItem(this.VERSION_STORAGE_KEY);
    sessionStorage.removeItem(this.VERSION_HISTORIES_KEY);
    sessionStorage.removeItem(this.USER_EDITED_FILES_KEY);

    this.baselineSnapshot$.next(null);
    this.currentSnapshot$.next(null);
    this.fileVersionHistories$.next(new Map());
    this.userEditedFiles$.next(new Set());
    this.versionCounter = 1;
  }

  /**
   * Create a snapshot from file array
   */
  private createSnapshot(files: FileModel[], type: string): FileTreeSnapshot {
    const now = Date.now();
    const totalSize = files.reduce((size, file) => size + (file.content?.length || 0), 0);

    return {
      id: `${type}_${now}_${this.versionCounter}`,
      timestamp: now,
      files: this.deepCloneFiles(files),
      metadata: {
        totalFiles: files.length,
        totalSize,
        lastModified: now,
        version: this.versionCounter++
      }
    };
  }

  /**
   * Perform detailed file comparison
   */
  private performDetailedComparison(
    currentFiles: FileModel[],
    incomingFiles: { fileName: string; content: string }[]
  ): FileComparisonResult {
    this.logger.info('🔍 Performing detailed file comparison');
    this.logger.info(`📊 Current: ${currentFiles.length} files, Incoming: ${incomingFiles.length} files`);

    const result: FileComparisonResult = {
      updated: [],
      added: [],
      unchanged: [],
      totalProcessed: incomingFiles.length
    };

    // Create a map of current files for efficient lookup
    const currentFileMap = new Map<string, FileModel>();
    currentFiles.forEach(file => {
      const normalizedPath = this.normalizeFilePath(file.name || file.fileName || '');
      currentFileMap.set(normalizedPath, file);
    });

    // Process each incoming file
    for (const incomingFile of incomingFiles) {
      const normalizedPath = this.normalizeFilePath(incomingFile.fileName);
      const existingFile = currentFileMap.get(normalizedPath);

      if (existingFile) {
        // File exists - check if content changed
        if (existingFile.content !== incomingFile.content) {
          const updatedFile: FileModel = {
            ...existingFile,
            content: incomingFile.content,
            fileName: incomingFile.fileName
          };
          result.updated.push(updatedFile);
          this.logger.info(`🔄 File updated: ${incomingFile.fileName}`);
        } else {
          result.unchanged.push(existingFile);
          this.logger.info(`✅ File unchanged: ${incomingFile.fileName}`);
        }
      } else {
        // New file
        const newFile = this.convertToFileModel(incomingFile);
        result.added.push(newFile);
        this.logger.info(`➕ File added: ${incomingFile.fileName}`);
      }
    }

    this.logger.info(`📋 Comparison complete: ${result.updated.length} updated, ${result.added.length} added, ${result.unchanged.length} unchanged`);
    return result;
  }

  /**
   * Convert incoming file format to FileModel
   */
  private convertToFileModel(file: { fileName: string; content: string }): FileModel {
    return {
      name: file.fileName,
      type: 'file',
      content: file.content,
      fileName: file.fileName
    };
  }

  /**
   * Normalize file path for comparison
   */
  private normalizeFilePath(filePath: string): string {
    if (!filePath) return '';

    return filePath
      .replace(/\\/g, '/')
      .replace(/^\/+/, '')
      .replace(/\/+$/, '')
      .toLowerCase();
  }

  /**
   * Deep clone files array to prevent mutations
   */
  private deepCloneFiles(files: FileModel[]): FileModel[] {
    return JSON.parse(JSON.stringify(files));
  }

  /**
   * Persist current state to sessionStorage
   */
  private persistState(): void {
    try {
      const baseline = this.baselineSnapshot$.value;
      const current = this.currentSnapshot$.value;

      if (baseline) {
        sessionStorage.setItem(this.BASELINE_STORAGE_KEY, JSON.stringify(baseline));
      }

      if (current) {
        sessionStorage.setItem(this.CURRENT_STORAGE_KEY, JSON.stringify(current));
      }

      sessionStorage.setItem(this.VERSION_STORAGE_KEY, this.versionCounter.toString());

      this.logger.info('💾 File tree state persisted to sessionStorage');
    } catch (error) {
      this.logger.error('❌ Failed to persist file tree state:', error);
    }
  }

  /**
   * Persist version state to sessionStorage
   */
  private persistVersionState(): void {
    try {
      const versionHistories = this.fileVersionHistories$.value;
      const userEditedFiles = this.userEditedFiles$.value;

      // Convert Map to object for serialization
      const versionHistoriesObj = Object.fromEntries(versionHistories);
      sessionStorage.setItem(this.VERSION_HISTORIES_KEY, JSON.stringify(versionHistoriesObj));

      // Convert Set to array for serialization
      const userEditedFilesArray = Array.from(userEditedFiles);
      sessionStorage.setItem(this.USER_EDITED_FILES_KEY, JSON.stringify(userEditedFilesArray));

      this.logger.info('💾 Version state persisted to sessionStorage');
    } catch (error) {
      this.logger.error('❌ Failed to persist version state:', error);
    }
  }

  /**
   * Load persisted state from sessionStorage
   */
  private loadPersistedState(): void {
    try {
      const baselineData = sessionStorage.getItem(this.BASELINE_STORAGE_KEY);
      const currentData = sessionStorage.getItem(this.CURRENT_STORAGE_KEY);
      const versionData = sessionStorage.getItem(this.VERSION_STORAGE_KEY);
      const versionHistoriesData = sessionStorage.getItem(this.VERSION_HISTORIES_KEY);
      const userEditedFilesData = sessionStorage.getItem(this.USER_EDITED_FILES_KEY);

      if (baselineData) {
        const baseline = JSON.parse(baselineData) as FileTreeSnapshot;
        this.baselineSnapshot$.next(baseline);
        this.logger.info('📂 Loaded baseline state from sessionStorage');
      }

      if (currentData) {
        const current = JSON.parse(currentData) as FileTreeSnapshot;
        this.currentSnapshot$.next(current);
        this.logger.info('📂 Loaded current state from sessionStorage');
      }

      if (versionData) {
        this.versionCounter = parseInt(versionData, 10) || 1;
      }

      if (versionHistoriesData) {
        const versionHistoriesObj = JSON.parse(versionHistoriesData);
        const versionHistoriesMap = new Map<string, FileVersionHistory>(Object.entries(versionHistoriesObj));
        this.fileVersionHistories$.next(versionHistoriesMap);
        this.logger.info(`📂 Loaded ${versionHistoriesMap.size} file version histories from sessionStorage`);
      }

      if (userEditedFilesData) {
        const userEditedFilesArray = JSON.parse(userEditedFilesData) as string[];
        const userEditedFilesSet = new Set(userEditedFilesArray);
        this.userEditedFiles$.next(userEditedFilesSet);
        this.logger.info(`📂 Loaded ${userEditedFilesSet.size} user-edited files from sessionStorage`);
      }

      this.logger.info('✅ File tree persistence service with version management initialized');
    } catch (error) {
      this.logger.error('❌ Failed to load persisted file tree state:', error);
    }
  }
}
