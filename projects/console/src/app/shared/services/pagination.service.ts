import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class PaginationService {
  /**
   * Gets paginated items while accounting for a Create Card on the first page
   * 
   * @param allItems All data items
   * @param currentPage Current page number (1-based)
   * @param itemsPerPage Number of items per page (including the Create Card on first page)
   * @returns Object containing displayedItems and totalPages
   */
  getPaginatedItems<T>(allItems: T[], currentPage: number, itemsPerPage: number = 12): { 
    displayedItems: T[], 
    totalPages: number 
  } {
    // Validate inputs
    if (currentPage < 1) currentPage = 1;
    if (itemsPerPage < 1) itemsPerPage = 12;
    
    // Calculate total pages accounting for the Create Card on first page
    const adjustedTotalItems = allItems.length + 1; // +1 for Create Card
    const totalPages = Math.ceil(adjustedTotalItems / itemsPerPage);
    
    let startIndex, endIndex;
    
    if (currentPage === 1) {
      // First page: leave room for 1 Create Card + (itemsPerPage-1) data cards
      startIndex = 0;
      endIndex = itemsPerPage - 1; // One less to account for Create Card
    } else {
      // Other pages: account for first page's adjusted count
      startIndex = (currentPage - 1) * itemsPerPage - 1; // -1 because first page has one less data item
      endIndex = startIndex + itemsPerPage;
    }
    
    // Ensure we don't go beyond array bounds
    startIndex = Math.max(0, startIndex);
    endIndex = Math.min(allItems.length, endIndex);
    
    // Get the slice of items for the current page
    const displayedItems = allItems.slice(startIndex, endIndex);
    
    return {
      displayedItems,
      totalPages
    };
  }
} 