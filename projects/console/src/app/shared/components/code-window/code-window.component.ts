import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChatWindowComponent, ChatMessage } from '../chat-window/chat-window.component';
import { CodeViewerComponent } from '../code-viewer/code-viewer.component';

interface CodeFile {
  name: string;
  language: string;
  content: string;
  path: string;
}

@Component({
  selector: 'app-code-window',
  standalone: true,
  imports: [CommonModule, ChatWindowComponent, CodeViewerComponent],
  templateUrl: './code-window.component.html',
  styleUrls: ['./code-window.component.scss']
})
export class CodeWindowComponent implements OnInit, OnDestroy {
  // UI state
  isResizing = false;
  currentView: 'editor' | 'preview' | 'loading' = 'loading';
  isPreviewLoading = true;
  currentTheme: 'dark' | 'light' = 'light';
  isPanelCollapsed = false;
  isLoading = true;
  isCodeActive = false;
  isPreviewActive = true;
  isLeftPanelCollapsed = false;
  lastProgressDescription = '';
  
  // Default values for hardcoded implementation
  files: CodeFile[] = [
    {
      name: 'index.html',
      language: 'html',
      path: 'src/index.html',
      content: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sample Project</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <h1>Welcome to My Project</h1>
    <p>This is a sample project to demonstrate the code window component.</p>
    <button id="myButton">Click Me</button>
  </div>
  <script src="app.js"></script>
</body>
</html>`
    },
    {
      name: 'styles.css',
      language: 'css',
      path: 'src/styles.css',
      content: `.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  color: #2c3e50;
}

button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #2980b9;
}`
    },
    {
      name: 'app.js',
      language: 'javascript',
      path: 'src/app.js',
      content: `document.addEventListener('DOMContentLoaded', () => {
  const button = document.getElementById('myButton');
  
  button.addEventListener('click', () => {
    alert('Button clicked!');
    console.log('Button click event handled');
  });
  
  console.log('Application initialized');
});`
    }
  ];
  
  // Chat window properties
  lightPrompt = '';
  darkPrompt = '';
  
  lightMessages: ChatMessage[] = [
    {
      from: 'ai',
      text: 'Hello! I can help you with coding tasks. What would you like to work on today?'
    },
    {
      from: 'user',
      text: 'Create a simple web app with HTML, CSS, and JavaScript'
    },
    {
      from: 'ai',
      text: 'I\'ve created a simple web application for you with HTML, CSS, and JavaScript. You can see the files in the code editor tab. The app includes a clean layout with a heading, paragraph, and an interactive button that shows an alert when clicked.'
    }
  ];
  
  rightIcons = [
    { name: 'send', tooltip: 'Send message' }
  ];
  
  isCodeGenerationComplete = true;
  
  // Constructor
  constructor() {}
  
  ngOnInit() {
    // Simulate loading completed
    setTimeout(() => {
      this.isLoading = false;
      this.currentView = 'preview';
    }, 1000);
  }
  
  ngOnDestroy() {
    // Cleanup 
  }
  
  // UI interaction methods
  onPanelToggled(isCollapsed: boolean) {
    this.isPanelCollapsed = isCollapsed;
  }
  
  toggleLeftPanel() {
    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;
  }
  
  // Tab switching
  toggleCodeView() {
    this.isCodeActive = true;
    this.isPreviewActive = false;
    this.currentView = 'editor';
  }
  
  togglePreviewView() {
    this.isCodeActive = false;
    this.isPreviewActive = true;
    this.currentView = 'preview';
  }
  
  // Handle tab clicks
  onTabClick(tab: 'code' | 'preview') {
    if (tab === 'code') {
      this.toggleCodeView();
    } else if (tab === 'preview') {
      this.togglePreviewView();
    }
  }
  
  // Chat interactions
  handleIconClick(event: { name: string, index: number }) {
    if (event.name === 'send') {
      this.handleSendMessage();
    }
  }
  
  handleSendMessage() {
    if (!this.lightPrompt.trim()) return;
    
    // Add user message
    this.lightMessages.push({
      from: 'user',
      text: this.lightPrompt
    });
    
    const userQuery = this.lightPrompt;
    this.lightPrompt = '';
    
    // Simulate AI response
    setTimeout(() => {
      this.lightMessages.push({
        from: 'ai',
        text: `I've processed your request: "${userQuery}". Let me know if you need any modifications to the code.`
      });
    }, 1000);
  }
  
  handleEnhancedSendLight() {
    this.handleSendMessage();
  }
  
  // Helper for file operations
  getFileBySuffix(suffix: string): CodeFile | undefined {
    return this.files.find(file => file.name.endsWith(suffix));
  }
}