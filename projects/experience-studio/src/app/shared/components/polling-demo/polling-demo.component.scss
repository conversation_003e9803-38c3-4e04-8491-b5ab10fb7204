.polling-demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;

  h2 {
    color: #2c3e50;
    margin-bottom: 10px;
  }

  p {
    color: #7f8c8d;
    font-size: 16px;
  }
}

.demo-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.control-group {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;

  h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #ecf0f1;
    padding-bottom: 8px;
  }

  .demo-btn {
    width: 100%;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.demo-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &.primary {
    background-color: #3498db;
    color: white;

    &:hover {
      background-color: #2980b9;
    }
  }

  &.secondary {
    background-color: #95a5a6;
    color: white;

    &:hover {
      background-color: #7f8c8d;
    }
  }

  &.tertiary {
    background-color: #f39c12;
    color: white;

    &:hover {
      background-color: #e67e22;
    }
  }

  &.warning {
    background-color: #ffc107;
    color: #212529;

    &:hover {
      background-color: #e0a800;
    }
  }

  &.info {
    background-color: #17a2b8;
    color: white;

    &:hover {
      background-color: #138496;
    }
  }

  &.danger {
    background-color: #e74c3c;
    color: white;

    &:hover {
      background-color: #c0392b;
    }
  }
}

.demo-status {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.status-section {
  margin-bottom: 20px;

  h3 {
    margin-bottom: 15px;
    color: #2c3e50;
  }
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 5px;

  label {
    font-weight: 600;
    color: #34495e;
    font-size: 14px;
  }

  .status-value {
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 13px;

    &.IN_PROGRESS {
      background-color: #fff3cd;
      border-color: #ffeaa7;
      color: #856404;
    }

    &.COMPLETED {
      background-color: #d4edda;
      border-color: #c3e6cb;
      color: #155724;
    }

    &.FAILED {
      background-color: #f8d7da;
      border-color: #f5c6cb;
      color: #721c24;
    }
  }
}

.logs-section {
  h3 {
    margin-bottom: 10px;
    color: #2c3e50;
  }
}

.log-content {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  min-height: 60px;
  white-space: pre-wrap;
}

.demo-data-preview {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;

  h3 {
    margin-bottom: 20px;
    color: #2c3e50;
  }
}

.data-sections {
  display: grid;
  gap: 20px;
}

.data-section {
  h4 {
    margin-bottom: 10px;
    color: #34495e;
    font-size: 16px;
  }
}

.data-content {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.file-list {
  display: grid;
  gap: 8px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #dee2e6;

  .file-path {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 13px;
    color: #2c3e50;
  }

  .file-size {
    font-size: 12px;
    color: #7f8c8d;
  }
}

.preview-link {
  display: inline-block;
  padding: 8px 12px;
  background: #3498db;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;

  &:hover {
    background: #2980b9;
  }
}

.simple-file-list {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    padding: 6px 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 13px;
  }
}

.demo-explanation {
  background: #e8f4fd;
  border-radius: 8px;
  padding: 20px;

  h3 {
    margin-bottom: 15px;
    color: #2c3e50;
  }

  .explanation-content p {
    margin-bottom: 20px;
    color: #34495e;
    line-height: 1.6;
  }
}

.flow-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 15px;

  .step-number {
    width: 30px;
    height: 30px;
    background: #3498db;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    flex-shrink: 0;
  }

  .step-content {
    h4 {
      margin-bottom: 5px;
      color: #2c3e50;
      font-size: 16px;
    }

    p {
      color: #7f8c8d;
      font-size: 14px;
      line-height: 1.4;
      margin: 0;
    }
  }
}

@media (max-width: 768px) {
  .demo-controls {
    flex-direction: column;
    align-items: center;
  }

  .demo-btn {
    width: 100%;
    max-width: 300px;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .flow-steps {
    grid-template-columns: 1fr;
  }
}
