<div class="code-viewer-container" [ngClass]="theme">
  <div *ngIf="showFileExplorer" class="file-explorer">
    <div class="file-explorer-header">
      <span>Files</span>
    </div>
    <div class="file-list">
      <div *ngFor="let file of files" 
           class="file-item" 
           [class.selected]="selectedFile === file"
           (click)="selectFile(file)">
        <span class="file-icon">{{ getFileIcon(file.name) }}</span>
        <span class="file-name">{{ file.name }}</span>
      </div>
    </div>
  </div>
  
  <div class="code-content">
    <div class="code-tabs">
      <div *ngFor="let file of files" 
           class="code-tab" 
           [class.active]="selectedFile === file"
           (click)="selectFile(file)">
        {{ file.name }}
      </div>
    </div>
    
    <div class="code-display" *ngIf="selectedFile">
      <pre><code [class]="getLanguageClass(selectedFile.language)">{{ selectedFile.content }}</code></pre>
    </div>
    
    <div class="no-file-selected" *ngIf="!selectedFile">
      <div class="no-file-message">No file selected</div>
    </div>
  </div>
</div>