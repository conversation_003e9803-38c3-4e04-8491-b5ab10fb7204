export const salesAgent = [
  {
    id: 1,
    title: 'Ruby to Springboot',
    type: 'flow',
    description:
      'Convert Ruby applications to Spring Boot with automated code migration and best practices implementation',
    rating: 4.8,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '#FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 156,
  },
  {
    id: 2,
    title: 'Brainstorm Product Idea',
    type: 'studio',
    description:
      'Collaborative ideation workspace for product innovation with AI-driven insights and market analysis',
    rating: 4.7,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 234,
  },
  {
    id: 3,
    title: 'Create UI Design',
    type: 'studio',
    description:
      'Design modern user interfaces with component libraries and design system integration',
    rating: 4.9,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 189,
  },
  {
    id: 4,
    title: 'Document to Code',
    type: 'flow',
    description:
      'Transform design documents and specifications into production-ready code with automated code generation',
    rating: 4.6,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '#FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 145,
  },
  {
    id: 5,
    title: 'Upgrade Angular',
    type: 'flow',
    description:
      'Seamlessly upgrade Angular applications to the latest version with automated dependency updates',
    rating: 4.7,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 178,
  },
  {
    id: 6,
    title: 'Insights Ascender',
    type: 'flow',
    description:
      'Extract actionable insights from complex data sets with advanced analytics and visualization',
    rating: 4.8,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 167,
  },
  {
    id: 7,
    title: 'Generate Test Case',
    type: 'flow',
    description:
      'Automatically generate comprehensive test cases with intelligent test coverage analysis',
    rating: 4.5,
    studio: {
      name: 'Quality Studio',
      type: 'Quality Studio',
      backgroundColor: '#FBEDEC',
      textColor: '#C9372C',
    },
    users: 143,
  },
  {
    id: 8,
    title: 'SWOT Analysis',
    type: 'flow',
    description:
      'Conduct detailed SWOT analysis with market insights and competitive intelligence',
    rating: 4.6,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 198,
  },
];
