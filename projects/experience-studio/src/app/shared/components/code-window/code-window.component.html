<awe-splitscreen
  class="container smooth-split-screen"
  [isResizable]="true"
  [minWidth]="(minWidth | async) || '300'"
  defaultLeftPanelWidth="35%"
  defaultRightPanelWidth="65%">
  <awe-leftpanel [hasHeader]="true" awe-leftpanel>
    <div awe-leftpanel-header>
      <!-- Custom left panel header instead of awe-header -->
      <div class="custom-header" [ngClass]="(currentTheme | async) + '-theme'">
        <div class="header-left">
          <awe-icons
            iconName="awe_home"
            iconColor="neutralIcon"
            class="cursor-pointer"
            (click)="navigateToHome()"
            title="Navigate to home page"></awe-icons>
          <awe-icons
            (click)="toggleLeftPanel()"
            iconName="awe_dock_to_right"
            iconColor="neutralIcon"></awe-icons>
        </div>
        <div class="header-center">
          <div
            class="project-name"
            [class.shimmer]="isProjectNameLoading"
            [class.hidden]="shouldHideProjectName | async"
            *ngIf="!(shouldHideProjectName | async)">
            {{ projectName }}
          </div>
        </div>
        <div class="header-right">
          <!-- Hide History tab in UI Design mode -->
          <!-- <div
            *ngIf="!(isUIDesignMode | async)"
            class="custom-button"
            [class.active]="isHistoryActive | async"
            (click)="toggleHistoryView()">
            History
          </div> -->
        </div>
      </div>
    </div>
    <div awe-leftpanel-content class="adjust-height">
      <div *ngIf="!(isHistoryActive | async)" class="adjust-height">
        <!--Injected chat-window on left content-->
        <app-chat-window
          #chatWindow
          [theme]="(currentTheme | async) || 'light'"
          [defaultText]="getPromptBarPlaceholder()"
          [rightIcons]="rightIcons"
          [(textValue)]="lightPrompt"
          [chatMessages]="lightMessages"
          [showStepper]="(isUIDesignMode | async) ? false : isPolling"
          [progress]="currentProgressState"
          [progressDescription]="lastProgressDescription"
          [status]="pollingStatus"
          [selectedImageDataUri]="selectedImageDataUri"
          [isCodeGenerationComplete]="getPromptBarEnabledState()"
          [isUIDesignLoading]="shouldShowUIDesignLoadingIndicator()"
          [projectId]="projectId || ''"
          [jobId]="jobId || ''"
          [useApi]="true"
          (iconClicked)="handleIconClick($event)"
          (enterPressed)="handleUIDesignPromptSubmission()"
          (retryStep)="onRetryClick()">
        </app-chat-window>
      </div>
      <div class="border history-container" *ngIf="isHistoryActive | async">
        <div class="history-content">
          <div class="history-header">
            <awe-icons iconName="awe_arrow_back_left" (click)="toggleHistoryView()"></awe-icons>
            <h4>Project History</h4>
          </div>

          <div class="history-cards-container"></div>
        </div>
      </div>
    </div>
  </awe-leftpanel>

  <awe-rightpanel [hasHeader]="true" awe-rightpanel>
    <div awe-rightpanel-header>
      <!-- Custom right panel header with tabs and organized icons -->
      <div class="custom-header" [ngClass]="(currentTheme | async) + '-theme'">
        <div class="header-left">
          <!-- Home icon that only shows when left panel is collapsed -->
          <awe-icons
            *ngIf="isLeftPanelCollapsed | async"
            iconName="awe_home"
            iconColor="neutralIcon"
            class="cursor-pointer"
            (click)="navigateToHome()"
            title="Navigate to home page"></awe-icons>
          <awe-icons
            class="dockToRight"
            *ngIf="isLeftPanelCollapsed | async"
            (click)="toggleLeftPanel()"
            iconName="awe_dock_to_right"
            iconColor="neutralIcon"
            title="Toggle left panel"></awe-icons>

          <!-- Tabs instead of toggle switch -->
          <div class="tabs-container">
            <!-- UI Design Mode - Overview and Preview Tabs -->
            <ng-container *ngIf="isUIDesignMode | async">
              <!-- Overview tab - shown when UI design response is received -->
              <div
                *ngIf="showUIDesignOverviewTab | async"
                class="custom-button"
                [class.active]="(currentView | async) === 'overview'"
                (click)="onTabClick('overview')"
                [title]="'View UI Design in Mobile Frame'">
                <span>Overview</span>
              </div>
              <!-- Preview tab - canvas view -->
              <div
                class="custom-button"
                [class.active]="(currentView | async) === 'preview' || !(showUIDesignOverviewTab | async)"
                (click)="onTabClick('preview')"
                [title]="'View UI Design Canvas'">
                <span>Preview</span>
              </div>
            </ng-container>

            <!-- Standard Mode - All Tabs -->
            <ng-container *ngIf="!(isUIDesignMode | async)">
              <!-- Preview tab - STRICT: only shown when DEPLOY + COMPLETED with ref_code URL or FAILED status -->
              <div
                *ngIf="isNewPreviewTabEnabled"
                class="custom-button"
                [class.active]="(currentView | async) === 'preview' && !(isLogsActive | async)"
                [class.error-tab]="isInFailedState"
                (click)="onTabClick('preview')"
                [title]="
                  isInFailedState ? 'View error details' : 'View deployed application preview'
                ">
                <span>{{ (previewTabName | async) || 'Preview' }}</span>
              </div>
              <!-- Code tab - STRICT: only shown when BUILD + IN_PROGRESS -->
              <div
                *ngIf="isNewCodeTabEnabled"
                class="custom-button"
                [class.active]="(currentView | async) === 'editor' && !(isLogsActive | async)"
                (click)="onTabClick('code')"
                [title]="'View generated code'">
                <span>Code</span>
              </div>
              <div
                class="custom-button"
                [class.active]="isLogsActive | async"
                [class.disabled]="!hasLogs"
                (click)="hasLogs && onTabClick('logs')"
                [title]="
                  hasLogs
                    ? 'View application logs and debugging information'
                    : 'Logs will be available once generated'
                ">
                <span>Logs</span>
                <i *ngIf="isStreamingLogs" class="bi bi-arrow-repeat tab-status spinning"></i>
              </div>

              <!-- Artifacts tab - always shown but disabled when no artifacts are available -->
              <div
                class="custom-button"
                [class.active]="isArtifactsActive | async"
                [class.disabled]="!isArtifactsTabEnabled"
                (click)="isArtifactsTabEnabled && onTabClick('artifacts')"
                [title]="
                  isArtifactsTabEnabled
                    ? 'View project artifacts'
                    : 'Artifacts will be available when generated'
                ">
                <span>Artifacts</span>
              </div>
              <!-- Export tab - shown like other tabs -->
            </ng-container>
          </div>
        </div>

        <div class="header-right">
          <div class="icon-group">
            <div
              *ngIf="
                isCodeGenerationComplete &&
                !(previewError | async) &&
                (currentView | async) === 'editor'
              "
              class="custom-button"
              [class.active]="isExperienceStudioModalOpen | async"
              (click)="toggleExportModal()"
              title="Export project">
              <span>Export</span>
            </div>
            <!-- Export icon removed as it's now in the tabs -->
            <awe-icons
              *ngIf="
                (currentView | async) === 'preview' &&
                isCodeGenerationComplete &&
                !(previewError | async)
              "
              iconName="awe_edit"
              iconColor="neutralIcon"
              title="Select element to edit"
              [class.active]="isElementSelectionMode"
              (click)="onEditButtonClick()"
              [disabled]="true"></awe-icons>
            <awe-icons
              *ngIf="
                (currentView | async) === 'preview' &&
                isCodeGenerationComplete &&
                !(previewError | async) &&
                (deployedUrl | async)
              "
              iconName="awe_external_link"
              iconColor="neutralIcon"
              title="Open preview in new tab"
              (click)="openPreviewInNewTab()"></awe-icons>
          </div>
        </div>
      </div>
    </div>

    <div awe-rightpanel-content>
      <!--Injected Code editor on right content-->
      <!-- <app-loading-animation
        *ngIf="currentView === 'loading' || isPreviewLoading"
        [messages]="loadingMessages"></app-loading-animation> -->
      <!-- Editor View - only show when editor is active AND logs are not active AND artifacts are not active -->
      <div
        *ngIf="
          (currentView | async) === 'editor' &&
          !(isLogsActive | async) &&
          !(isArtifactsActive | async)
        "
        class="editor-view">
        <!-- Always show loading animation when code generation is not complete -->
        <app-loading-animation
          *ngIf="!isCodeGenerationComplete"
          [messages]="loadingMessages"
          [theme]="(currentTheme | async) || 'light'"></app-loading-animation>

        <!-- Show code viewer when code generation is complete -->
        <app-code-viewer
          *ngIf="isCodeGenerationComplete"
          [theme]="(currentTheme | async) || 'light'"
          [files]="(files | async) || []"
          [showFileExplorer]="true"></app-code-viewer>
      </div>

      <!-- Overview View - only show when overview is active AND logs are not active AND artifacts are not active -->
      <div
        *ngIf="
          (currentView | async) === 'overview' &&
          !(isLogsActive | async) &&
          !(isArtifactsActive | async) &&
          (isUIDesignMode | async)
        "
        class="overview-view">
        <app-mobile-frame
          [pages]="(uiDesignPages | async) || []"
          [currentPageIndex]="(currentUIDesignPageIndex | async) || 0"
          [theme]="(currentTheme | async) || 'light'"
          (pageChange)="onUIDesignPageChange($event)"
          (fullscreenRequest)="onUIDesignFullscreenRequest($event)">
        </app-mobile-frame>
      </div>

      <!-- Preview View - only show when preview is active AND logs are not active AND artifacts are not active -->
      <div
        *ngIf="
          (currentView | async) === 'preview' &&
          !(isLogsActive | async) &&
          !(isArtifactsActive | async)
        "
        class="preview-view">

        <!-- UI Design Canvas Mode -->
        <div *ngIf="isUIDesignMode | async" class="ui-design-canvas-container" #uiDesignCanvas>
          <!-- Modern Loading Screen with Blurred Canvas Background -->
          <div
            *ngIf="shouldShowUIDesignCanvasLoading()"
            class="ui-design-loading-overlay"
            [ngClass]="(currentTheme | async) + '-theme'">
            <div class="loading-backdrop"></div>
            <div class="loading-content">
              <div class="loading-spinner-container">
                <div class="modern-loading-spinner">
                  <div class="spinner-ring"></div>
                  <div class="spinner-core"></div>
                </div>
              </div>
              <div class="loading-text-container">
                <h3 class="loading-title">Generating UI Design</h3>
                <p class="loading-message">{{ getCurrentUIDesignLoadingMessage() }}</p>
                <div class="loading-progress">
                  <div class="progress-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Viewport Debug Panel -->
          <!-- <app-viewport-debug></app-viewport-debug> -->

          <!-- Canvas Info Panel -->
          <app-canvas-info></app-canvas-info>

          <!-- Canvas Selection Tooltip -->
          <div
            *ngIf="showCanvasTooltip | async"
            class="canvas-selection-tooltip"
            [ngClass]="(currentTheme | async) + '-theme'">
            <div class="tooltip-content">
              <i class="bi bi-cursor-fill"></i>
              <span>Single click to select a page for editing</span>
            </div>
          </div>

          <!-- Canvas Controls -->
          <div class="canvas-controls">
            <div class="zoom-controls">
              <button
                class="canvas-control-btn"
                (click)="zoomOutCanvas()"
                [disabled]="isCanvasAtMinZoom()"
                title="Zoom Out">
                <!-- Custom Zoom Out SVG Icon -->
                <svg class="zoom-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="6.5" cy="6.5" r="5" stroke-width="1.5"/>
                  <path d="M10.5 10.5l4 4" stroke-width="1.5"/>
                  <path d="M4 6.5h5" stroke-width="1.2"/>
                </svg>
              </button>
              <span class="zoom-display">{{ getCanvasZoomPercentage() }}%</span>
              <button
                class="canvas-control-btn"
                (click)="zoomInCanvas()"
                [disabled]="isCanvasAtMaxZoom()"
                title="Zoom In">
                <!-- Custom Zoom In SVG Icon -->
                <svg class="zoom-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="6.5" cy="6.5" r="5" stroke-width="1.5"/>
                  <path d="M10.5 10.5l4 4" stroke-width="1.5"/>
                  <path d="M6.5 4v5M4 6.5h5" stroke-width="1.2"/>
                </svg>
              </button>
            </div>
            <div class="view-controls">
              <button
                class="canvas-control-btn"
                (click)="resetCanvasView()"
                title="Reset View">
                <i class="bi bi-arrow-clockwise"></i>
                Reset
              </button>
              <button
                class="canvas-control-btn"
                (click)="fitCanvasToView()"
                title="Fit to View">
                <i class="bi bi-arrows-fullscreen"></i>
                Fit
              </button>
            </div>
            <!-- Multi-Selection Controls - Only visible after wireframe generation success -->
            <div
              class="selection-controls"
              *ngIf="(wireframeGenerationStateService.canShowSelectionControls$ | async) && (uiDesignNodes | async) && (uiDesignNodes | async)!.length > 0">
              <button
                class="canvas-control-btn selection-btn"
                (click)="selectAllNodes()"
                title="Select All Pages"
                [attr.aria-label]="'Select all ' + (uiDesignNodes | async)!.length + ' pages'">
                <i class="bi bi-check-square"></i>
                Select All
              </button>
              <button
                class="canvas-control-btn selection-btn"
                (click)="clearAllSelection()"
                [disabled]="getSelectedNodesCount() === 0"
                title="Clear Selection"
                [attr.aria-label]="'Clear selection of ' + getSelectedNodesCount() + ' pages'">
                <i class="bi bi-square"></i>
                Clear
              </button>
              <div class="selection-info" *ngIf="getSelectedNodesCount() > 0">
                <span class="selection-count">{{ getSelectedNodesCount() }} selected</span>
              </div>
            </div>
          </div>

          <!-- Canvas Content -->
          <div
            class="canvas-content"
            [style.transform]="getCanvasTransformStyle()"
            (mousedown)="onCanvasMouseDown($event)"
            (mousemove)="onCanvasMouseMove($event)"
            (mouseup)="onCanvasMouseUp($event)"
            (wheel)="onCanvasWheel($event)">

            <!-- UI Design Nodes -->
            <div
              *ngFor="let node of (uiDesignNodes | async); trackBy: trackByUIDesignNode"
              class="ui-design-node"
              [style.left.px]="node.position.x"
              [style.top.px]="node.position.y"
              [style.width.px]="node.data.width"
              [style.height.px]="node.data.height"
              [style.z-index]="node.data.zIndex || 100"
              [ngClass]="uiDesignVisualFeedbackService.getEnhancedNodeClasses(node.id)"
              [class.dragging]="node.dragging"
              [class.loading]="node.data.isLoading"
              [attr.data-page-name]="node.data.displayTitle || node.data.title"
              [attr.aria-selected]="isNodeSelectedForEditing(node.id)"
              (click)="onUIDesignNodeSelect(node, $event)"
              (dblclick)="onUIDesignNodeDoubleClick(node)">

              <div class="node-header">
                <h4
                  [class.highlighted-title]="uiDesignVisualFeedbackService.isNodeSelected(node.id)"
                  [attr.aria-label]="node.data.displayTitle || node.data.title">
                  {{ node.data.displayTitle || node.data.title }}
                </h4>
              </div>

              <div class="node-content">
                <!-- Show loader when isLoading is true -->
                <div *ngIf="node.data.isLoading" class="node-loader">
                  <div class="loader-spinner"></div>
                  <div class="loader-text">Generating wireframe...</div>
                </div>

                <!-- Show iframe content when not loading -->
                <iframe
                  *ngIf="!node.data.isLoading"
                  [safeSrcdoc]="node.data.rawContent"
                  class="preview-iframe"
                  frameborder="0"
                  sandbox="allow-same-origin allow-scripts allow-top-navigation-by-user-activation">
                </iframe>
              </div>

              <!-- Only show overlay when not loading -->
              <div *ngIf="!node.data.isLoading" class="node-overlay">
                <i class="bi bi-arrows-fullscreen"></i>
                <span>Double-click to expand</span>
              </div>
            </div>

            <!-- Loading Nodes for Regeneration -->
            <div
              *ngFor="let loadingNode of (uiDesignLoadingNodes | async); trackBy: trackByUIDesignNode"
              class="ui-design-node loading-node"
              [style.left.px]="loadingNode.position.x"
              [style.top.px]="loadingNode.position.y"
              [style.width.px]="loadingNode.data.width"
              [style.height.px]="loadingNode.data.height"
              [style.z-index]="1000"
              [attr.data-page-name]="loadingNode.data.displayTitle || loadingNode.data.title"
              [attr.data-loading-for]="loadingNode.data.originalNodeId">

              <div class="node-header">
                <h4 [attr.aria-label]="loadingNode.data.displayTitle || loadingNode.data.title">
                  {{ loadingNode.data.displayTitle || loadingNode.data.title }}
                </h4>
              </div>

              <div class="node-content">
                <!-- Always show loader for loading nodes -->
                <div class="node-loader regeneration-loader">
                  <div class="loader-spinner"></div>
                  <div class="loader-text">{{ loadingNode.data.loadingMessage || 'Editing...' }}</div>
                </div>
              </div>

              <!-- Loading indicator overlay -->
              <div class="loading-overlay">
                <div class="loading-pulse"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Standard Preview Mode -->
        <ng-container *ngIf="!(isUIDesignMode | async)">
          <!-- Different states of the preview view - only one will be shown at a time -->

        <!-- Enhanced iframe loading with URL validation and availability checking -->
        <!-- Show URL validation loading state (but not during regeneration) -->
        <div
          *ngIf="
            isCodeGenerationComplete &&
            !(previewError | async) &&
            urlSafe &&
            !(isIframeReady | async) &&
            !(regenerationInProgress$ | async)
          "
          class="url-validation-container">
          <app-loading-animation
            [theme]="(currentTheme | async) || 'light'"
            [messages]="[
              'Validating deployment URL...',
              'Checking URL accessibility...',
              'Preparing iframe for loading...',
              'Almost ready to display your app...',
            ]">
          </app-loading-animation>

          <!-- Show validation error if any -->
          <div *ngIf="urlValidationError | async" class="url-validation-error">
            <div class="error-message">
              <i class="bi bi-exclamation-triangle"></i>
              <span>{{ urlValidationError | async }}</span>
            </div>
          </div>
        </div>

        <!-- Show iframe only when URL is validated, available, iframe is ready, and NOT during regeneration -->
        <div
          *ngIf="
            isCodeGenerationComplete &&
            !(previewError | async) &&
            urlSafe &&
            (isIframeReady | async) &&
            (isUrlValidated | async) &&
            (isUrlAvailable | async) &&
            !(regenerationInProgress$ | async)
          "
          class="iframe-container">
          <iframe
            [src]="urlSafe"
            class="preview-frame"
            frameborder="0"
            (load)="onIframeLoad($event)"
            (error)="onIframeError($event)"
            sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-modals allow-top-navigation-by-user-activation"
            referrerpolicy="no-referrer"
            allow="accelerometer; camera; encrypted-media; geolocation; gyroscope; microphone; midi">
          </iframe>
        </div>

        <!-- Show deployment loading state when code is complete but no URL yet OR during regeneration -->
        <div
          *ngIf="isCodeGenerationComplete && !(previewError | async) && (!urlSafe || (regenerationInProgress$ | async))"
          class="deployment-loading-container">
          <app-loading-animation
            [theme]="(currentTheme | async) || 'light'"
            [messages]="(regenerationInProgress$ | async) ? [
              'Processing your code changes...',
              'Applying edits to your application...',
              'Rebuilding with your modifications...',
              'Updating application structure...',
              'Deploying regenerated code...',
              'Almost ready - finalizing regeneration...',
              'Your updated preview will be available shortly...'
            ] : [
              'Preparing your preview deployment...',
              'Setting up hosting environment...',
              'Configuring deployment pipeline...',
              'Building application for preview...',
              'Deploying to preview server...',
              'Almost ready - finalizing deployment...',
              'Your preview will be available shortly...'
            ]">
          </app-loading-animation>
        </div>

        <!-- Initial loading state - show if code generation is not complete and either:
             1. No progress state exists, or
             2. We're in a state that's not LAYOUT_ANALYZED or PAGES_GENERATED, or
             3. We're in LAYOUT_ANALYZED or PAGES_GENERATED but have no valid layout data -->
        <ng-container
          *ngIf="
            !isCodeGenerationComplete &&
            (!currentProgressState ||
              (!currentProgressState.includes('LAYOUT_ANALYZED') &&
                !currentProgressState.includes('PAGES_GENERATED')) ||
              ((currentProgressState.includes('LAYOUT_ANALYZED') ||
                currentProgressState.includes('PAGES_GENERATED')) &&
                (!layoutData || layoutData.length === 0 || !layoutMapping[layoutData[0]])))
          ">
          <div
            class="loading-state-container"
            [style.display]="isCodeGenerationComplete || (previewError | async) ? 'none' : 'block'">
            <app-loading-animation
              [messages]="loadingMessages"
              [theme]="(currentTheme | async) || 'light'"></app-loading-animation>
          </div>
        </ng-container>

        <!-- Layout analyzed state - only show if code generation is not complete -->
        <ng-container
          *ngIf="
            !isCodeGenerationComplete &&
            currentProgressState &&
            currentProgressState.includes('LAYOUT_ANALYZED') &&
            !currentProgressState.includes('PAGES_GENERATED') &&
            layoutData &&
            layoutData.length > 0 &&
            layoutMapping[layoutData[0]]
          ">
          <div
            class="layout-examples-container"
            [style.display]="isCodeGenerationComplete || (previewError | async) ? 'none' : 'block'">
            <div class="layout-examples-header">
              <p *ngIf="layoutData.length > 1">
                {{ layoutData.length }} layouts have been identified for your application
              </p>
            </div>

            <!-- Show actual layout data - no shimmer/skeleton loading -->
            <div
              class="layout-examples-grid"
              [class.layout-examples-grid-fullscreen]="layoutData.length === 1"
              [class.layout-count-1]="layoutData.length === 1"
              [class.layout-count-2]="layoutData.length === 2"
              [class.layout-count-3]="layoutData.length === 3"
              [class.layout-count-4]="layoutData.length === 4">
              <!-- If we have layout data, use it and show all identified layouts -->
              <ng-container *ngIf="layoutData && layoutData.length > 0">
                <div
                  *ngFor="let layout of layoutData; trackBy: trackByLayoutId"
                  class="layout-example-item"
                  [class.layout-example-item-fullscreen]="layoutData.length === 1">
                  <div class="layout-example-card">
                    <div
                      class="layout-example-image"
                      [class.layout-example-image-fullscreen]="layoutData.length === 1">
                      <!-- Directly use the layout key to get the image with layout- prefix -->
                      <img
                        [src]="'assets/images/layout-' + layout + '.png'"
                        [alt]="layoutMapping[layout] || 'Identified Layout'"
                        loading="lazy"
                        decoding="async"
                        onerror="this.src='assets/images/01.png'" />
                    </div>
                    <div class="layout-example-title">
                      {{ layoutMapping[layout] || 'Identified Layout' }}
                    </div>
                  </div>
                </div>
              </ng-container>

              <!-- Fallback to default layout if none detected -->
              <ng-container *ngIf="!layoutData || layoutData.length === 0">
                <div class="layout-example-item layout-example-item-fullscreen">
                  <div class="layout-example-card">
                    <div class="layout-example-image layout-example-image-fullscreen">
                      <img
                        [src]="'assets/images/01.png'"
                        [alt]="'Default Layout'"
                        loading="lazy"
                        decoding="async" />
                    </div>
                    <div class="layout-example-title">Default Layout Structure</div>
                    <div class="layout-example-description">
                      A standard layout structure has been selected for your application
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </ng-container>

        <!-- Pages generated state - only show if code generation is not complete -->
        <ng-container
          *ngIf="
            !isCodeGenerationComplete &&
            currentProgressState &&
            currentProgressState.includes('PAGES_GENERATED') &&
            layoutData &&
            layoutData.length > 0 &&
            layoutMapping[layoutData[0]]
          ">
          <div
            class="pages-generated-container"
            [style.display]="isCodeGenerationComplete || (previewError | async) ? 'none' : 'block'">
            <div class="pages-generated-header">
              <h3>Page Inventory</h3>
              <p *ngIf="layoutData.length > 1">
                Using {{ layoutData.length }} different layout types for your pages
              </p>
            </div>

            <!-- Show actual page data when loaded -->
            <div
              class="pages-examples-grid"
              [class.layout-count-1]="layoutData.length === 1"
              [class.layout-count-2]="layoutData.length === 2"
              [class.layout-count-3]="layoutData.length === 3"
              [class.layout-count-4]="layoutData.length === 4">
              <!-- If we have layout data, use it - only show the specific key -->
              <ng-container *ngIf="layoutData && layoutData.length > 0">
                <!-- Create page examples using all available layouts -->
                <div
                  *ngFor="let i of [0, 1, 2, 3, 4, 5, 6, 7]; trackBy: trackByPageIndex"
                  class="page-example-item">
                  <div class="page-example-card">
                    <div class="page-example-image">
                      <!-- Use the appropriate layout based on index -->
                      <img
                        [src]="'assets/images/layout-' + getLayoutForPageIndex(i) + '.png'"
                        [alt]="getPageTitle(i, getLayoutForPageIndex(i))"
                        loading="lazy"
                        decoding="async"
                        onerror="this.src='assets/images/0' + (i + 1 > 8 ? '1' : (i + 1)) + '.png'"
                        [attr.data-index]="i + 1" />
                    </div>
                    <div class="page-example-title">
                      {{ getPageTitle(i, getLayoutForPageIndex(i)) }}
                    </div>
                  </div>
                </div>
              </ng-container>

              <!-- Fallback to default pages if none detected -->
              <ng-container *ngIf="!layoutData || layoutData.length === 0">
                <div
                  *ngFor="
                    let image of layoutExampleImages.slice(0, 8);
                    let i = index;
                    trackBy: trackByPageIndex
                  "
                  class="page-example-item">
                  <div class="page-example-card">
                    <div class="page-example-image">
                      <img
                        [src]="image"
                        [alt]="getPageTitle(i, '')"
                        loading="lazy"
                        decoding="async" />
                    </div>
                    <div class="page-example-title">
                      {{ getPageTitle(i, '') }}
                    </div>
                    <!-- <div class="page-example-description">
                      Standard page structure for your website
                    </div> -->
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </ng-container>

          <!-- Show error page if preview failed -->
          <app-error-page
            *ngIf="previewError | async"
            [theme]="(currentTheme | async) || 'light'"
            [errorDescription]="(errorDescription | async) || 'An error occurred'"
            [terminalOutput]="(errorTerminalOutput | async) || ''"
            [progressState]="pollingStatus"
            (retry)="onRetryClick()"
            (goHome)="navigateToHome()"
            (showDetails)="toggleLogsView()">
          </app-error-page>
        </ng-container>
      </div>

      <!-- Logs View - only show when logs are active -->
      <div
        *ngIf="
          (currentView | async) === 'logs' && (isLogsActive | async) && !(isArtifactsActive | async)
        "
        class="logs-view">
        <!-- Show loading animation when no logs are available -->
        <app-loading-animation
          *ngIf="!hasLogs"
          [theme]="(currentTheme | async) || 'light'"
          [messages]="[
            'Initializing your workspace…',
            'Loading creative modules…',
            'Setting up your dashboard…',
            'Almost there — preparing magic!',
            'Initializing build process and preparing environment...',
            'Setting up compilation pipeline for your application...',
            'Configuring build tools and dependencies...',
            'Preparing to generate application logs...',
            'Logs will appear here as they become available...',
            'Monitoring build process for status updates...',
          ]">
        </app-loading-animation>

        <!-- Show logs when they are available -->
        <div *ngIf="hasLogs || isCodeGenerationComplete" class="logs-container">
          <div class="logs-header">
            <div class="logs-header-row">
              <div class="logs-header-left">
                <div class="logs-header-title">
                  <h4>Application Logs</h4>
                </div>
              </div>

              <div class="logs-header-center">
                <div class="log-filter">
                  <div
                    class="filter-option"
                    [class.active]="true"
                    [class.disabled]="!isCodeGenerationComplete">
                    <span class="dot info-dot"></span>
                    <span>Info</span>
                  </div>
                  <div
                    class="filter-option"
                    [class.active]="true"
                    [class.disabled]="!isCodeGenerationComplete">
                    <span class="dot debug-dot"></span>
                    <span>Debug</span>
                  </div>
                  <div
                    class="filter-option"
                    [class.active]="true"
                    [class.disabled]="!isCodeGenerationComplete">
                    <span class="dot warning-dot"></span>
                    <span>Warning</span>
                  </div>
                  <div
                    class="filter-option"
                    [class.active]="true"
                    [class.disabled]="!isCodeGenerationComplete">
                    <span class="dot error-dot"></span>
                    <span>Error</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="logs-content">
            <!-- Info message removed as requested -->

            <!-- New formatted logs display with letter-by-letter typing effect -->
            <div class="formatted-logs-container">
              <!-- Regular log entries with trackBy for more efficient rendering -->
              <div
                *ngFor="let log of formattedLogMessages; let i = index; trackBy: trackByLogIndex"
                class="log-entry"
                [class]="getLogClass(log)"
                [class.new-log]="i >= formattedLogMessages.length - 5">
                <!-- Timestamp -->
                <span class="log-timestamp">{{ log.timestamp }}</span>

                <!-- Code block with capsule-like container -->
                <div
                  *ngIf="log.type === 'code'"
                  class="code-capsule"
                  [class.expanded]="isCodeExpanded(log.id)"
                  [class.dark-theme]="(currentTheme | async) === 'dark'">
                  <!-- Code header with file path and toggle button -->
                  <div class="code-header" (click)="toggleCodeExpansion(log)">
                    <span class="file-path">{{ log.path || 'unknown.file' }}</span>
                    <div class="code-header-actions">
                      <span class="toggle-icon">
                        {{ isCodeExpanded(log.id) ? '▼' : '▶' }}
                      </span>
                    </div>
                  </div>

                  <!-- Code content with typing effect - only shown when expanded -->
                  <div
                    class="code-content-wrapper"
                    [class.expanded]="isCodeExpanded(log.id)"
                    [style.--max-content-height.px]="log.maxHeight || 500">
                    <pre class="code-content">
                      <code
                        [innerHTML]="formatCodeForDisplay(log.visibleContent || '')"
                        [class.typing]="isTypingLog && i === currentLogIndex"></code>
                      <!-- Typewriter cursor removed as requested -->
                    </pre>
                  </div>
                </div>

                <!-- Regular log content with typing effect - only show if content is not empty -->
                <span
                  *ngIf="log.type !== 'code'"
                  class="log-content"
                  [class.typing]="isTypingLog && i === currentLogIndex">
                  <span class="log-text">{{ log.visibleContent || '' }}</span>
                  <!-- Typewriter cursor removed as requested -->
                </span>
              </div>

              <!-- Show a message when no logs are available -->
              <div *ngIf="formattedLogMessages.length === 0" class="no-logs-message">
                <span
                  >No logs available yet. Logs will appear here when the application generates
                  them.</span
                >
              </div>
            </div>

            <!-- Keep the original logs display for compatibility, but hide it -->
            <pre style="display: none">
              <code *ngFor="let log of logMessages; let i = index" [class]="getLogClass(log)">{{ log }}</code>
            </pre>
          </div>
        </div>
      </div>

      <!-- Artifacts View - only show when artifacts tab is active and enabled -->
      <div
        *ngIf="
          (currentView | async) === 'artifacts' &&
          (isArtifactsActive | async) &&
          isArtifactsTabEnabled
        "
        class="artifacts-view">
        <!-- Show loading animation when artifacts are not available -->
        <app-loading-animation
          *ngIf="!isArtifactsTabEnabled || artifactsData.length === 0"
          [theme]="(currentTheme | async) || 'light'"
          [messages]="[
            'Loading project artifacts...',
            'Preparing file viewer...',
            'Organizing project resources...',
            'Collecting design assets...',
            'Analyzing project structure...',
            'Gathering documentation...',
          ]">
        </app-loading-animation>

        <!-- Show artifacts when they are available -->
        <div *ngIf="isArtifactsTabEnabled && artifactsData.length > 0" class="artifacts-container">
          <!-- File viewer with split view -->
          <div class="artifacts-content">
            <!-- Left sidebar with file tree -->
            <div class="file-explorer">
              <div class="file-explorer-header">
                <span>Files</span>
              </div>
              <div class="file-list">
                <div
                  *ngFor="let file of artifactsData"
                  class="file-item"
                  [class.selected]="selectedArtifactFile === file"
                  (click)="selectArtifactFile(file)">
                  <span class="file-icon" [ngClass]="getFileIconClass(file.type)"></span>
                  <span class="file-name">{{ file.name }}</span>
                </div>
              </div>
            </div>

            <!-- Right content area -->
            <div class="file-content">
              <!-- Show message when no file is selected -->
              <div *ngIf="!selectedArtifactFile" class="no-file-selected">
                <span>Select a file from the list to view its content</span>
              </div>

              <!-- Show content based on file type -->
              <ng-container *ngIf="selectedArtifactFile">
                <!-- Markdown content -->
                <div *ngIf="selectedArtifactFile.type === 'markdown'" class="markdown-content">
                  <!-- Use typewriter content for Project Overview, regular content for others -->
                  <markdown
                    [data]="selectedArtifactFile.name === 'Project Overview' ?
                            getArtifactVisibleContent(selectedArtifactFile.name) :
                            selectedArtifactFile.content">
                  </markdown>
                </div>

                <!-- Image content -->
                <div *ngIf="selectedArtifactFile.type === 'image'" class="image-content">
                  <!-- Layout Analyzed content -->
                  <div
                    *ngIf="selectedArtifactFile.name === 'Layout Analyzed'"
                    class="layout-examples-container">
                    <div class="layout-examples-header">
                      <p *ngIf="layoutAnalyzedData && layoutAnalyzedData.length > 1">
                        {{ layoutAnalyzedData.length }} layouts have been identified for your
                        application
                      </p>
                      <p *ngIf="layoutAnalyzedData && layoutAnalyzedData.length === 1">
                        A layout has been identified for your application
                      </p>
                    </div>

                    <!-- Show actual layout data -->
                    <div
                      class="layout-examples-grid"
                      [class.layout-examples-grid-fullscreen]="layoutAnalyzedData.length === 1"
                      [class.layout-count-1]="layoutAnalyzedData.length === 1"
                      [class.layout-count-2]="layoutAnalyzedData.length === 2"
                      [class.layout-count-3]="layoutAnalyzedData.length === 3"
                      [class.layout-count-4]="layoutAnalyzedData.length === 4">
                      <!-- If we have layout data, use it and show all identified layouts -->
                      <ng-container *ngIf="layoutAnalyzedData && layoutAnalyzedData.length > 0">
                        <div
                          *ngFor="let layout of layoutAnalyzedData; trackBy: trackByLayoutId"
                          class="layout-example-item"
                          [class.layout-example-item-fullscreen]="layoutAnalyzedData.length === 1">
                          <div class="layout-example-card">
                            <div
                              class="layout-example-animation"
                              [class.layout-example-animation-fullscreen]="
                                layoutAnalyzedData.length === 1
                              ">
                              <!-- Use the new layout animation component -->
                              <app-layout-identified-animation
                                [layoutKey]="layout.key"
                                [theme]="(currentTheme | async) || 'light'">
                              </app-layout-identified-animation>
                            </div>
                            <div
                              class="layout-example-description"
                              *ngIf="layoutAnalyzedData.length === 1">
                              <p>A layout has been identified for your application</p>
                            </div>
                          </div>
                        </div>
                      </ng-container>

                      <!-- Fallback to default layout if none detected -->
                      <ng-container *ngIf="!layoutAnalyzedData || layoutAnalyzedData.length === 0">
                        <div class="layout-example-item layout-example-item-fullscreen">
                          <div class="layout-example-card">
                            <div
                              class="layout-example-animation layout-example-animation-fullscreen">
                              <!-- Use default HB layout animation as fallback -->
                              <app-layout-identified-animation
                                [layoutKey]="'HB'"
                                [theme]="(currentTheme | async) || 'light'">
                              </app-layout-identified-animation>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </div>
                  </div>

                  <!-- Regular image content for other image types -->
                  <img
                    *ngIf="selectedArtifactFile.name !== 'Layout Analyzed'"
                    [src]="selectedArtifactFile.content"
                    [alt]="selectedArtifactFile.name"
                    loading="lazy"
                    decoding="async" />
                </div>

                <!-- SVG content -->
                <div *ngIf="selectedArtifactFile.type === 'svg'" class="svg-content">
                  <img
                    [src]="selectedArtifactFile.content"
                    [alt]="selectedArtifactFile.name"
                    loading="lazy"
                    decoding="async" />
                </div>

                <!-- Text content -->
                <div *ngIf="selectedArtifactFile.type === 'text'" class="text-content">
                  <pre>{{ selectedArtifactFile.content }}</pre>
                </div>

                <!-- Component content (for design system) -->
                <div *ngIf="selectedArtifactFile.type === 'component'" class="component-content">
                  <!-- Design System Component -->
                  <div
                    *ngIf="selectedArtifactFile.name === 'Design System'"
                    class="design-system-container">
                    <!-- Design System Header -->
                    <div class="design-system-header">
                      <h2>Design System</h2>
                    </div>

                    <!-- Colors Section (Moved to Top) -->
                    <div class="design-section">
                      <h3>Colours</h3>
                      <div class="color-swatches">
                        <div
                          *ngFor="let token of getTokensByCategory('Colors')"
                          class="color-swatch">
                          <div class="color-box" [style.background-color]="token.value"></div>
                          <div class="color-details">
                            <div class="color-name">{{ token.name }}</div>
                            <div class="color-value">
                              <input
                                type="text"
                                class="token-input"
                                [value]="token.value"
                                (change)="onTokenValueChange($event, token.id)"
                                [attr.title]="'Edit ' + token.name + ' color value'" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Combined Font Style and Buttons Section (With Single Coming Soon Overlay) -->
                    <div class="design-section coming-soon-section combined-sections">
                      <div class="coming-soon-overlay">
                        <div class="coming-soon-content">
                          <div class="coming-soon-icon">🎨</div>
                          <div class="coming-soon-text">Coming Soon</div>
                          <div class="coming-soon-subtitle">
                            Font and button customization will be available soon
                          </div>
                        </div>
                      </div>

                      <!-- Font Style Section -->
                      <h3>Font Style Desktop</h3>
                      <div class="font-styles">
                        <div
                          *ngFor="let token of getTokensByCategory('Font Style Desktop')"
                          class="font-style-item">
                          <h2 *ngIf="token.name === 'Headline 1'" class="headline-1">Headline 1</h2>
                          <h3 *ngIf="token.name === 'Headline 2'" class="headline-2">Headline 2</h3>
                          <h4 *ngIf="token.name === 'Headline 3'" class="headline-3">Headline 3</h4>
                          <h5 *ngIf="token.name === 'Headline 4'" class="headline-4">Headline 4</h5>
                          <div class="font-details">
                            <span>{{ token.value }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- Buttons Section -->
                      <h3>Buttons</h3>
                      <div class="button-samples">
                        <div
                          *ngFor="let token of getTokensByCategory('Buttons')"
                          class="button-sample">
                          <button [class]="'sample-button ' + token.value">
                            {{ token.name }}
                            <span *ngIf="token.value.includes('icon')" class="button-icon">✨</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Placeholder for other component types -->
                  <div
                    *ngIf="selectedArtifactFile.name !== 'Design System'"
                    class="shimmer-container">
                    <div class="shimmer-block"></div>
                    <div class="shimmer-block"></div>
                    <div class="shimmer-block"></div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </awe-rightpanel>
</awe-splitscreen>

<!-- Custom Modal Implementation -->
<div
  class="custom-modal-overlay"
  *ngIf="isExperienceStudioModalOpen | async"
  (click)="toggleExportModal()">
  <div class="custom-modal-content" (click)="$event.stopPropagation()">
    <div class="custom-modal-header">
      <h3>Export this project</h3>
      <button class="custom-close-button" (click)="toggleExportModal()">×</button>
    </div>
    <div class="custom-modal-body">
      <div class="sharable-link-section">
        <p class="section-label">Sharable link</p>
        <div class="link-input-container">
          <input
            class="link-input"
            type="text"
            [value]="
              (deployedUrl | async) || 'Deployment URL will be available after deployment completes'
            "
            readonly
            #sharableLink />
          <button class="copy-button" (click)="copyToClipboard(sharableLink)">Copy</button>
        </div>
      </div>

      <div class="export-options">
        <div class="export-option-row">
          <div class="export-option-item" (click)="downloadProject()">
            <div class="option-icon-container download">
              <img
                src="assets/icons/awe_download.svg"
                [ngClass]="{ 'dark-icon': (currentTheme | async) === 'dark' }"
                alt="Download"
                width="32"
                height="32"
                loading="lazy"
                decoding="async" />
            </div>
            <span class="option-label">Download</span>
          </div>

          <div class="export-option-item" (click)="exportToAzure()">
            <div class="option-icon-container">
              <img
                src="assets/icons/awe_azure.svg"
                width="32"
                height="32"
                loading="lazy"
                decoding="async" />
            </div>
            <span class="option-label">Azure</span>
          </div>

          <div class="export-option-item" (click)="exportToVSCode()">
            <div class="option-icon-container">
              <img
                src="assets/icons/awe_vscode.svg"
                width="32"
                height="32"
                loading="lazy"
                decoding="async" />
            </div>
            <span class="option-label">VSCode</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Simple Full Screen Image Preview Overlay -->
<div class="image-overlay" *ngIf="showPreview && previewImage">
  <button
    class="close-button"
    (click)="closeImagePreview()"
    role="button"
    tabindex="0"
    aria-label="Close preview">
    ×
  </button>
  <img
    [src]="previewImage.url"
    [alt]="previewImage.name"
    loading="eager"
    decoding="async"
    fetchpriority="high" />
</div>

<!-- UI Design Full-Screen Modal -->
<div
  class="ui-design-fullscreen-overlay"
  *ngIf="isUIDesignFullScreenOpen | async"
  [class.modal-fullscreen]="isUIDesignModalFullScreen | async"
  (click)="closeUIDesignFullScreen()">
  <div class="ui-design-fullscreen-modal"
       [class.modal-fullscreen]="isUIDesignModalFullScreen | async"
       (click)="$event.stopPropagation()">
    <!-- Modal Header -->
    <div class="ui-design-modal-header">
      <div class="modal-title">
        <h3>{{ (selectedUIDesignNode | async)?.data?.displayTitle || (selectedUIDesignNode | async)?.data?.title || 'UI Design Preview' }}</h3>
      </div>
      <div class="modal-controls">
        <!-- View Mode Toggle -->
        <div class="view-mode-toggle">
          <button
            class="view-mode-btn"
            [class.active]="(uiDesignViewMode | async) === 'mobile'"
            (click)="switchUIDesignViewMode('mobile')"
            title="Mobile View">
            <i class="bi bi-phone"></i>
            Mobile
          </button>
          <button
            class="view-mode-btn"
            [class.active]="(uiDesignViewMode | async) === 'web'"
            (click)="switchUIDesignViewMode('web')"
            title="Web View">
            <i class="bi bi-laptop"></i>
            Web
          </button>
        </div>

        <!-- Control Buttons -->
        <div class="modal-control-buttons">
          <!-- Open in New Tab Button -->
          <button
            class="control-btn new-tab-btn"
            (click)="openUIDesignInNewTab()"
            title="Open in New Tab">
            <!-- Custom External Link SVG Icon -->
            <svg class="new-tab-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 2h4v4M14 2l-6 6M8 2H3a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>

          <!-- Fullscreen Toggle Button -->
          <button
            class="control-btn fullscreen-btn"
            (click)="toggleUIDesignModalFullScreen()"
            [title]="(isUIDesignModalFullScreen | async) ? 'Exit Fullscreen' : 'Enter Fullscreen'">
            <!-- Custom Fullscreen SVG Icon -->
            <svg class="fullscreen-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g *ngIf="!(isUIDesignModalFullScreen | async)">
                <!-- Expand to fullscreen icon -->
                <path d="M1.5 1.5h4M1.5 1.5v4M1.5 1.5l4 4M14.5 1.5h-4M14.5 1.5v4M14.5 1.5l-4 4M1.5 14.5h4M1.5 14.5v-4M1.5 14.5l4-4M14.5 14.5h-4M14.5 14.5v-4M14.5 14.5l-4-4" stroke-width="1.5" stroke-linecap="round"/>
              </g>
              <g *ngIf="isUIDesignModalFullScreen | async">
                <!-- Exit fullscreen icon -->
                <path d="M5.5 1.5h-4M5.5 1.5v4M5.5 1.5l-4 4M10.5 1.5h4M10.5 1.5v4M10.5 1.5l4 4M5.5 14.5h-4M5.5 14.5v-4M5.5 14.5l-4-4M10.5 14.5h4M10.5 14.5v-4M10.5 14.5l4-4" stroke-width="1.5" stroke-linecap="round"/>
              </g>
            </svg>
          </button>

          <!-- Close Button -->
          <button
            class="control-btn close-btn"
            (click)="closeUIDesignFullScreen()"
            title="Close Preview">
            <!-- Custom Close SVG Icon -->
            <svg class="close-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 4L4 12M4 4l8 8" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Modal Content -->
    <div class="ui-design-modal-content">
      <div
        class="ui-design-preview-container"
        [class.mobile-view]="(uiDesignViewMode | async) === 'mobile'"
        [class.web-view]="(uiDesignViewMode | async) === 'web'">
        <iframe
          *ngIf="selectedUIDesignNode | async"
          [safeSrcdoc]="(selectedUIDesignNode | async)?.data?.rawContent || ''"
          class="ui-design-preview-iframe"
          frameborder="0"
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-downloads allow-top-navigation-by-user-activation">
        </iframe>
      </div>
    </div>
  </div>
</div>
