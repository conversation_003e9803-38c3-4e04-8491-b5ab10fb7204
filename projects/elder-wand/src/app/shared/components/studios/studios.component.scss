.studios-container {
  padding: 40px 20px;

  .studios-header {
    margin-bottom: 40px;
    text-align: center;

    .title-wrapper {
      display: inline-flex;
      align-items: center;
      gap: 12px;

      .sparkle {
        font-size: 28px;
      }

      h1 {
        color: #14161F;
        text-align: center;
        font-family: Mulish;
        font-size: 48px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: -0.912px;

        .gradient-text {
          background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 110.55%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }
    }
  }

  .studios-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    margin: 0 auto;

    .studio-card {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      cursor: pointer;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);

        .arrow-button {
          background: linear-gradient(180deg, #C2C3FF 0%, #7D7FD9 100%);

          .arrow {
            color: white;
            /* Change icon color to white or any other color you prefer */
            transform: translateX(4px);
          }
        }

      }

      .card-content {
        display: flex;
        justify-content: space-between;
        padding: 16px;
        height: 100%;

        .text-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          padding: 32px;

          .text-top {
            display: flex;
            flex-direction: column;
            gap: 16px;

            h2 {
              color: #1D1D1D;
              font-family: Mulish;
              font-size: 40px;
              font-style: normal;
              font-weight: 700;
              line-height: 120%;
              margin: 0;
            }

            .description {
              color: #595959;
              font-family: Mulish;
              font-size: 20px;
              font-style: normal;
              font-weight: 500;
              line-height: 150%;
              margin: 0;
              width: 344px;
              text-align: left;
            }
          }

          .arrow-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            awe-icons svg {
              margin-top: 9px;
            }

            .arrow {
              font-size: 20px;
              color: #595959;
              /* Default icon color */
              transition: transform 0.3s ease, color 0.3s ease;
            }
          }
        }

        .image-container {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 280px;
          width: 460px;

          img {
            max-width: 100%;
            height: auto;
            object-fit: contain;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .studios-grid {
    grid-template-columns: 1fr !important;
  }
}