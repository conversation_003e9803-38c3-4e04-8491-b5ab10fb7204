import { Component, OnInit } from '@angular/core';
import { CommonModule } from "@angular/common";
import { Router, ActivatedRoute } from '@angular/router';
import SearchBar from '../../../shared/components/search-bar/search-bar.component';
import { CreateCardComponent } from '../../../shared/components/create-card/create-card.component';
import { DataCardComponent } from '../../../shared/components/data-card/data-card.component';
import { CardData } from '../../../shared/models/card.model';
import { MOCK_GUARDRAILS } from '../../../shared/mock-data/guardrail-mock-data';
import { FilterBarComponent } from '../../../shared/components/filter-bar/filter-bar.component';
import { FilterService } from '../../../shared/services/filter.service';
import { FilterConfig } from '../../../shared/models/filter.model';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';

@Component({
  selector: 'app-guardrails',
  standalone: true,
  imports: [
    CommonModule, 
    SearchBar, 
    CreateCardComponent, 
    DataCardComponent, 
    FilterBarComponent,
    PageFooterComponent
  ],
  templateUrl: './guardrails.component.html',
  styleUrl: './guardrails.component.scss'
})
export class GuardrailsComponent implements OnInit {
  // Get guardrails from mock data
  allGuardrails: CardData[] = MOCK_GUARDRAILS;
  filteredGuardrails: CardData[] = [];
  displayedGuardrails: CardData[] = [];
  guardrailFilterConfig!: FilterConfig;
  
  // Track filter bar visibility
  isFilterBarVisible: boolean = false;
  
  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 12; // Show 12 cards per page
  totalPages: number = 1;
  
  // Map filter IDs to CardData properties
  private filterPropertyMap: {[key: string]: string} = {
    'guardrailType': 'userType',
    'domain': 'client',
    'category': 'department',
    'complexity': 'role',
    'access': 'project'
  };

  constructor(
    private filterService: FilterService,
    private paginationService: PaginationService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    // Get the filter configuration for guardrails
    this.guardrailFilterConfig = this.filterService.getFilterConfig('guardrails');
    this.filteredGuardrails = [...this.allGuardrails];
    
    // Check for page parameter in the URL
    const pageParam = this.route.snapshot.queryParamMap.get('page');
    if (pageParam) {
      this.currentPage = parseInt(pageParam, 10);
    }
    
    this.updateDisplayedGuardrails();
  }

  updateDisplayedGuardrails(): void {
    // Use the pagination service to get displayed items and total pages
    const result = this.paginationService.getPaginatedItems(
      this.filteredGuardrails,
      this.currentPage,
      this.itemsPerPage
    );
    
    this.displayedGuardrails = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreateGuardrail(): void {
    console.log('Create Guardrail clicked');
    // Implement navigation to create guardrail page
    this.router.navigate(['/libraries/guardrails/create']);
  }

  onCardClicked(guardrailId: string): void {
    console.log(`Guardrail card clicked: ${guardrailId}`);
    // Navigate to edit guardrail page
    this.router.navigate(['/libraries/guardrails/edit', guardrailId], {
      queryParams: { returnPage: this.currentPage }
    });
  }

  onActionClicked(event: {action: string, cardId: string}): void {
    console.log(`Action ${event.action} clicked for guardrail: ${event.cardId}`);
    
    // Handle different actions
    if (event.action === 'execute') {
      console.log(`Executing guardrail: ${event.cardId}`);
      this.router.navigate(['/libraries/guardrails/edit', event.cardId], { 
        queryParams: { execute: 'true', returnPage: this.currentPage } 
      });
    } else if (event.action === 'delete') {
      console.log(`Deleting guardrail: ${event.cardId}`);
      // Implement delete functionality
    }
  }

  toggleFilterBar(): void {
    this.isFilterBarVisible = !this.isFilterBarVisible;
    console.log('Filter bar visibility:', this.isFilterBarVisible);
  }

  onFilterChange(filters: {[key: string]: string}): void {
    // Apply filters to guardrails
    if (Object.keys(filters).length === 0) {
      this.filteredGuardrails = [...this.allGuardrails];
    } else {
      this.filteredGuardrails = this.filterService.filterData(
        this.allGuardrails, 
        filters, 
        this.filterPropertyMap
      );
    }
    
    // Reset to first page when filters change
    this.currentPage = 1;
    this.updateDisplayedGuardrails();
    
    console.log('Applied filters:', filters);
    console.log('Filtered guardrails count:', this.filteredGuardrails.length);
  }
  
  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedGuardrails();
    console.log('Page changed to:', this.currentPage);
    
    // Update URL with page parameter
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page: this.currentPage },
      queryParamsHandling: 'merge'
    });
  }
  
  // Helper to check if we should show the create card (only on first page)
  get showCreateCard(): boolean {
    return this.currentPage === 1;
  }
}
