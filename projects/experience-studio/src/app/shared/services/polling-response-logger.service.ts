import { Injectable } from '@angular/core';
import { NewPollingResponse } from '../models/polling-response.interface';
import { createLogger } from '../utils/logger';

/**
 * Service to capture and log real-time polling responses to files
 * for analysis, debugging, and validation purposes.
 */
@Injectable({
  providedIn: 'root'
})
export class PollingResponseLoggerService {
  private logger = createLogger('PollingResponseLogger');
  private responses: any[] = [];
  private sessionId: string;
  private startTime: Date;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.startTime = new Date();
    // this.logger.info('Polling response logger initialized with session ID:', this.sessionId);
  }

  /**
   * Log a raw polling response
   */
  logResponse(response: any, responseType: 'enhanced' | 'new' | 'legacy' = 'enhanced'): void {
    const timestamp = new Date();
    const logEntry = {
      sessionId: this.sessionId,
      timestamp: timestamp.toISOString(),
      responseType,
      response: this.deepClone(response),
      metadata: {
        timeFromStart: timestamp.getTime() - this.startTime.getTime(),
        responseSize: JSON.stringify(response).length,
        hasProgress: !!response?.progress,
        hasStatus: !!response?.status,
        hasMetadata: !!response?.metadata,
        hasHistory: !!response?.history
      }
    };

    this.responses.push(logEntry);
    // this.logger.info(`Logged ${responseType} polling response:`, {
    //   timestamp: logEntry.timestamp,
    //   progress: response?.progress,
    //   status: response?.status,
    //   responseSize: logEntry.metadata.responseSize
    // });
  }

  /**
   * Log an enhanced polling response with additional context
   */
  logEnhancedResponse(response: NewPollingResponse, context?: any): void {
    const enhancedLogEntry = {
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      responseType: 'enhanced',
      response: this.deepClone(response),
      context: context ? this.deepClone(context) : null,
      analysis: {
        progress: response.progress,
        status: response.status,
        hasArtifacts: response.metadata?.some(m => m.type === 'artifact') || false,
        hasFiles: response.metadata?.some(m => m.type === 'files') || false,
        hasPreviewUrl: response.metadata?.some(m => m.type === 'ref_code') || false,
        hasFileNames: response.metadata?.some(m => m.type === 'fileNames') || false,
        historyLength: response.history?.length || 0,
        metadataCount: response.metadata?.length || 0,
        logLength: response.log?.length || 0
      }
    };

    this.responses.push(enhancedLogEntry);
    // this.logger.info('Logged enhanced polling response with analysis:', enhancedLogEntry.analysis);
  }

  /**
   * Export all logged responses to a downloadable file
   */
  exportToFile(filename?: string): void {
    const exportData = {
      sessionInfo: {
        sessionId: this.sessionId,
        startTime: this.startTime.toISOString(),
        endTime: new Date().toISOString(),
        totalResponses: this.responses.length,
        sessionDuration: new Date().getTime() - this.startTime.getTime()
      },
      responses: this.responses,
      summary: this.generateSummary()
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });

    const defaultFilename = `polling-responses-${this.sessionId}-${this.formatDateForFilename(new Date())}.json`;
    const finalFilename = filename || defaultFilename;

    this.downloadFile(blob, finalFilename);
    // this.logger.info(`Exported ${this.responses.length} polling responses to file:`, finalFilename);
  }

  /**
   * Export responses in a human-readable format
   */
  exportToReadableFile(filename?: string): void {
    let content = `# Polling Response Log\n\n`;
    content += `**Session ID:** ${this.sessionId}\n`;
    content += `**Start Time:** ${this.startTime.toISOString()}\n`;
    content += `**End Time:** ${new Date().toISOString()}\n`;
    content += `**Total Responses:** ${this.responses.length}\n\n`;

    content += `## Summary\n\n`;
    const summary = this.generateSummary();
    content += `- **Response Types:** ${Object.keys(summary.responseTypes).join(', ')}\n`;
    content += `- **Progress States:** ${summary.progressStates.join(', ')}\n`;
    content += `- **Status Values:** ${summary.statusValues.join(', ')}\n`;
    content += `- **Average Response Size:** ${summary.averageResponseSize} bytes\n\n`;

    content += `## Detailed Responses\n\n`;

    this.responses.forEach((entry, index) => {
      content += `### Response ${index + 1}\n\n`;
      content += `**Timestamp:** ${entry.timestamp}\n`;
      content += `**Type:** ${entry.responseType}\n`;
      content += `**Progress:** ${entry.response?.progress || 'N/A'}\n`;
      content += `**Status:** ${entry.response?.status || 'N/A'}\n`;

      if (entry.analysis) {
        content += `**Analysis:**\n`;
        Object.entries(entry.analysis).forEach(([key, value]) => {
          content += `  - ${key}: ${value}\n`;
        });
      }

      content += `**Raw Response:**\n`;
      content += `\`\`\`json\n${JSON.stringify(entry.response, null, 2)}\n\`\`\`\n\n`;
      content += `---\n\n`;
    });

    const blob = new Blob([content], { type: 'text/markdown' });
    const defaultFilename = `polling-responses-readable-${this.sessionId}-${this.formatDateForFilename(new Date())}.md`;
    const finalFilename = filename || defaultFilename;

    this.downloadFile(blob, finalFilename);
    // this.logger.info(`Exported readable polling responses to file:`, finalFilename);
  }

  /**
   * Get current session statistics
   */
  getSessionStats(): any {
    return {
      sessionId: this.sessionId,
      startTime: this.startTime.toISOString(),
      currentTime: new Date().toISOString(),
      totalResponses: this.responses.length,
      sessionDuration: new Date().getTime() - this.startTime.getTime(),
      summary: this.generateSummary()
    };
  }

  /**
   * Clear all logged responses
   */
  clearResponses(): void {
    const previousCount = this.responses.length;
    this.responses = [];
    this.startTime = new Date();
    this.sessionId = this.generateSessionId();
    // this.logger.info(`Cleared ${previousCount} logged responses. New session started:`, this.sessionId);
  }

  /**
   * Get all logged responses
   */
  getAllResponses(): any[] {
    return this.deepClone(this.responses);
  }

  /**
   * Get responses filtered by criteria
   */
  getFilteredResponses(filter: {
    responseType?: string;
    progress?: string;
    status?: string;
    fromTime?: Date;
    toTime?: Date;
  }): any[] {
    return this.responses.filter(entry => {
      if (filter.responseType && entry.responseType !== filter.responseType) return false;
      if (filter.progress && entry.response?.progress !== filter.progress) return false;
      if (filter.status && entry.response?.status !== filter.status) return false;
      if (filter.fromTime && new Date(entry.timestamp) < filter.fromTime) return false;
      if (filter.toTime && new Date(entry.timestamp) > filter.toTime) return false;
      return true;
    });
  }

  // Private helper methods

  private generateSessionId(): string {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private deepClone(obj: any): any {
    return JSON.parse(JSON.stringify(obj));
  }

  private generateSummary(): any {
    const responseTypes: { [key: string]: number } = {};
    const progressStates = new Set<string>();
    const statusValues = new Set<string>();
    let totalSize = 0;

    this.responses.forEach(entry => {
      responseTypes[entry.responseType] = (responseTypes[entry.responseType] || 0) + 1;

      if (entry.response?.progress) {
        progressStates.add(entry.response.progress);
      }

      if (entry.response?.status) {
        statusValues.add(entry.response.status);
      }

      if (entry.metadata?.responseSize) {
        totalSize += entry.metadata.responseSize;
      }
    });

    return {
      responseTypes,
      progressStates: Array.from(progressStates),
      statusValues: Array.from(statusValues),
      averageResponseSize: this.responses.length > 0 ? Math.round(totalSize / this.responses.length) : 0,
      totalSize
    };
  }

  private formatDateForFilename(date: Date): string {
    return date.toISOString().replace(/[:.]/g, '-').split('T')[0] + '-' +
           date.toISOString().split('T')[1].split('.')[0].replace(/:/g, '-');
  }

  private downloadFile(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}
