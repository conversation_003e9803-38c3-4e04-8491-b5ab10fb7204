.collaborative-agent-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: transparent;
  position: relative;
  overflow-y: auto; 
  overflow-x: hidden; /* Allow horizontal overflow for dropdowns */
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin: 24px 0;
  padding: 0 20px;
}

.form-layout {
  display: flex;
  flex-direction: row;
  gap: 24px;
  padding: 24px;
  height: calc(100vh - 100px);
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden; /* Allow horizontal overflow for dropdowns */
  transition: all 0.3s ease;
  
  &.three-column-layout {
    .left-column {
      width: 25%;
      transition: width 0.3s ease;
      
      @media (max-width: 1400px) {
        width: 25%;
      }
      
      @media (max-width: 1200px) {
        width: 100%;
      }
    }
    
    .middle-column {
      width: 40%;
      transition: width 0.3s ease;
      
      @media (max-width: 1400px) {
        width: 35%;
      }
      
      @media (max-width: 1200px) {
        width: 100%;
      }
    }
  }
  
  @media (max-width: 1200px) {
    gap: 16px;
    padding: 16px;
  }
  
  @media (max-width: 992px) {
    flex-direction: column;
    overflow-y: auto;
  }
  
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px;
  }

  .left-column, .middle-column, .chat-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: 100%;
    
    @media (max-width: 1200px) {
      gap: 16px;
    }
    
    @media (max-width: 992px) {
      width: 100%;
      overflow: visible;
    }
    
    @media (max-width: 576px) {
      gap: 12px;
    }
  }

  .left-column {
    width: 30%;
    flex-shrink: 0;
    overflow-y: auto;
    transition: width 0.3s ease;
    
    @media (max-width: 1200px) {
      width: 35%;
    }
    
    @media (max-width: 992px) {
      width: 100%;
    }
    
    app-card {
      &:first-child {
        flex: 0 0 auto;
      }
      
      &:last-child {
        flex: 1;
      }
    }
  }

  .middle-column {
    width: 70%;
    flex-shrink: 0;
    overflow-y: auto;
    overflow-x: visible;
    display: flex;
    flex-direction: column;
    gap: 40px;
    transition: width 0.3s ease;
    
    @media (max-width: 1200px) {
      width: 65%;
    }
    
    @media (max-width: 992px) {
      width: 100%;
    }
    
    app-card {
      &:first-child {
        flex: 0 0 auto;
      }
      
      &:last-child {
        margin-top: auto;
      }
    }
  }
}

.card-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  
  &.agent-task-container {
    gap: 16px;
    padding: 16px 24px 24px;
  }
  
  &.template-content {
    display: flex;
    flex-direction: column;
    min-height: 300px; /* Ensure template card maintains minimum height */
    /* Remove bottom padding to accommodate the form actions */
  }
  
  @media (max-width: 576px) {
    padding: 16px;
    gap: 16px;
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 0;
  color: var(--text-color);
  
  @media (max-width: 576px) {
    font-size: 16px;
  }
}

.field-label {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 4px;
  color: var(--text-secondary);
  
  @media (max-width: 576px) {
    font-size: 14px;
  }
}

/* Agent task section */
.agent-task-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.input-with-actions {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.input-analyze-row {
  display: flex;
  gap: 12px;
  width: 100%;
  
  .input-with-icon {
    flex: 1;
    position: relative;
  }
  
  .workflow-input {
    width: 100%;
  }
  
  .analyze-button {
    margin-top: 0;
    height: 40px;
    padding: 0 20px;
  }
}

/* Ensure attachment icon appears inside the input field */
.input-with-icon {
  position: relative;
  width: 100%;
  
  ::ng-deep {
    .form-field-container {
      position: relative;
      width: 100%;
    }
    
    .form-input {
      height: 40px !important;
      border-radius: 8px !important;
      padding-right: 40px !important; /* Make room for the attachment icon */
    }
  }
}

/* Force the attachment icon to be visible */
::ng-deep .attach-icon {
  position: absolute !important;
  right: 10px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 5 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  opacity: 0.7 !important;
  
  svg {
    width: 16px !important;
    height: 16px !important;
    stroke: var(--text-secondary, #666666) !important;
  }
  
  &:hover {
    background-color: var(--card-hover-shadow, rgba(0, 0, 0, 0.05)) !important;
    opacity: 1 !important;
    
    svg {
      stroke: var(--primary-start, #6566CD) !important;
    }
  }
}

/* Action buttons for the input area */
.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.clipboard-actions {
  display: flex;
  gap: 8px;
}

.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: var(--agent-action-btn-bg);
  border: 1px solid var(--agent-action-btn-border);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: var(--agent-action-btn-hover-bg);
    border-color: var(--agent-action-btn-hover-border);
    color: var(--text-color);
  }
}

.analyze-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 20px;
  height: 40px;
  border-radius: 8px;
  background: var(--gradient-primary, linear-gradient(90deg, #9f4fe5 0%, #ff3bde 100%));
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 120px;
  
  &:hover {
    opacity: var(--agent-analyze-btn-hover);
  }
  
  .sparkle-emoji {
    font-size: 16px;
    margin-right: 2px;
  }
}

/* Instructions section with template option */
.instruction-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px 0;
  width: 100%;
  flex: 1;
}

.instruction-text {
  font-size: 14px;
  color: var(--text-secondary);
  text-align: center;
  
  &.center-text {
    font-weight: 500;
    margin: 4px 0;
  }
}

.highlight {
  color: var(--primary-start);
  font-weight: 600;
}

.template-card {
  width: 100%;
  flex: 1;
  min-height: 150px;
  padding: 24px;
  border: 1px dashed var(--agent-template-card-border);
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: var(--agent-template-card-hover-border);
    background-color: var(--agent-template-card-hover-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px var(--agent-template-card-hover-shadow);
  }
}

.template-title {
  font-size: 20px;
  color: var(--text-color);
  margin: 0;
  font-weight: 500;
  position: relative;
  
  /* Add a small plus icon to indicate it's clickable */
  // &::after {
  //   content: "+";
  //   font-size: 18px;
  //   position: absolute;
  //   top: 0;
  //   right: -24px;
  //   color: var(--primary-start);
  //   font-weight: 600;
  // }
}

/* Custom styling for the card component */
::ng-deep app-card {
  width: 100%;
  display: flex;
  flex-direction: column;
  
  .card-container {
    background: var(--agent-card-bg) !important;
    border: 1px solid var(--agent-card-border) !important;
    box-shadow: 0 2px 4px var(--agent-card-shadow) !important;
    border-radius: 12px !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    overflow: visible !important; /* Ensure dropdowns can overflow outside the card */
  }
}

/* Form field customization */
::ng-deep app-form-field {
  width: 100%;
  
  .form-input, .form-textarea {
    background-color: var(--input-bg) !important;
    color: var(--input-text) !important;
    border: 1px solid var(--input-border) !important;
    border-radius: 8px !important;
    padding: 12px 15px !important;
    
    &:focus {
      border-color: var(--input-focus-border) !important;
      box-shadow: 0 0 0 2px var(--input-focus-shadow) !important;
    }
    
    &:hover:not(:focus) {
      border-color: var(--input-focus-border) !important;
    }
  }
  
  // Default textarea style - allows resizing
  .form-textarea {
    min-height: 100px !important;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical !important; // Allow vertical resizing
    overflow-y: auto !important; // Show scrollbar when needed
    
    &.textarea-with-icon {
      padding-right: 40px !important;
    }
  }
  
  .attach-icon {
    opacity: 0.7;
    top: 50% !important;
    transform: translateY(-50%) !important;
    bottom: auto !important;
    
    &:hover {
      opacity: 1;
      background-color: var(--card-hover-shadow, rgba(0, 0, 0, 0.05));
    }
  }
}

/* Form action buttons */
.form-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  gap: 12px;
  
  @media (max-width: 576px) {
    padding: 12px;
    gap: 8px;
  }
}

/* Specific styling for the actions card */
.actions-card {
  ::ng-deep .card-container {
    padding: 0 !important;
  }
}

/* Mixin for gradient borders to ensure cross-browser compatibility */
@mixin gradient-border {
  border: 1px solid transparent;
  background-image: linear-gradient(var(--agent-gradient-border-bg), var(--agent-gradient-border-bg)), linear-gradient(90deg, #6566CD 0%, #F96CAB 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  border-radius: 6px;
}

.submit-button {
  padding: 6px 12px;
  background: transparent;
  color: #6566CD;
  @include gradient-border;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 110px;
  text-align: center;
  
  &:hover {
    background-color: var(--agent-action-btn-hover-bg);
  }
  
  &:disabled {
    background: var(--disabled-bg, #e0e0e0);
    color: var(--disabled-text, #a0a0a0);
    cursor: not-allowed;
    opacity: 0.7;
    border-image: none;
    border: 1px solid var(--disabled-bg, #e0e0e0);
    background-image: none;
  }
}

.exit-button {
  padding: 6px 12px;
  background: transparent;
  color: #6566CD;
  @include gradient-border;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 110px;
  text-align: center;
  
  &:hover {
    background-color: var(--agent-action-btn-hover-bg);
  }
}

/* Chat column styles */
.chat-column {
  width: 35%;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  background-color: var(--agent-chat-column-bg);
  border-left: 1px solid var(--agent-chat-column-border);
  transition: all 0.3s ease;
  box-shadow: -2px 0 10px var(--agent-chat-column-shadow);
  
  @media (max-width: 1400px) {
    width: 35%;
  }
  
  @media (max-width: 1200px) {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--agent-chat-column-border);
    box-shadow: 0 -2px 10px var(--agent-chat-column-shadow);
  }
  
  .chat-column-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  app-card {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.playground-title {
  font-size: 14px;
  font-weight: 500;
  color: #6566CD;
  margin: 0 0 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Chat container styles */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

app-chat-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* Make sure chat card content takes full height */
.chat-column .card-content {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-button {
  padding: 6px 12px;
  background: linear-gradient(90deg, #6566CD 0%, #F96CAB 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 110px;
  text-align: center;
  
  &:hover {
    opacity: 0.9;
  }
  
  &:disabled {
    background: var(--disabled-bg, #e0e0e0);
    color: var(--disabled-text, #a0a0a0);
    cursor: not-allowed;
    opacity: 0.7;
  }
}

/* Prompt related styles */
.prompt-selection-row {
  /* Position dropdown and button at opposite ends */
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: relative;
  z-index: 10;
  margin-top: 0; /* Add space above for dropdown to open upward */
  padding-top: 15px; /* Add padding at the top to give space for dropdown */
  
  .select-container {
    flex: 0 1 auto; /* Don't grow, allow shrinking, base size on content */
    width: 60%; /* Reduce width to 60% of the container */
    max-width: 400px; /* Set a maximum width */
  }
  
  /* Button styling - push to the right edge */
  .edit-button, .enhance-button {
    margin-left: auto; /* Push button to the right */
    flex-shrink: 0; /* Prevent button from shrinking */
  }
  
  /* For small screens, ensure select doesn't get too narrow */
  @media (max-width: 768px) {
    .select-container {
      width: 70%; /* Slightly wider on smaller screens */
    }
  }
  
  @media (max-width: 576px) {
    flex-direction: row; /* Ensure it stays as row even on very small screens */
    
    .select-container {
      width: 65%; /* Adjust for smallest screens */
    }
  }
  
  /* Dropdown styling */
  ::ng-deep app-select-dropdown.dropdown-down {
    .dropdown-menu {
      top: calc(100% + 4px) !important; /* Position below instead of above */
      bottom: auto !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
      animation: fadeIn 0.2s ease !important;
    }
    
    /* Keep dropdown arrow pointing down */
    .select-icon svg {
      transform: rotate(0deg);
      
      &.open {
        transform: rotate(180deg);
      }
    }
  }
}

/* Mixin for gradient text to apply the same gradient to text */
@mixin gradient-text {
  background: linear-gradient(90deg, #6566CD 0%, #F96CAB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.edit-button, .enhance-button {
  border: 1px solid transparent;
  background: linear-gradient(var(--edit-enhance-button-bg), var(--edit-enhance-button-bg)) padding-box,
    var(--button-gradient) border-box;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: var(--edit-enhance-button-bg);
  box-shadow: 0 2px 4px var(--edit-enhance-button-shadow);
  transition: all 0.2s ease;

  span {
    background: var(--button-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  svg {
    path {
      stroke: var(--svg-stroke-url);
    }
  }

  &:hover {
    background: linear-gradient(var(--edit-enhance-button-bg-hover), var(--edit-enhance-button-bg-hover)) padding-box,
      var(--button-gradient) border-box;
    box-shadow: 0 2px 6px var(--edit-enhance-button-shadow-hover);
  }
}

.two-column-row {
  display: flex;
  gap: 24px;
  margin-top: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
  
  .column {
    flex: 1;
  }
}

.three-column-row {
  display: flex;
  gap: 24px;
  margin-top: 16px;
  
  @media (max-width: 992px) {
    flex-direction: column;
    gap: 16px;
  }
  
  @media (min-width: 769px) and (max-width: 1200px) {
    flex-wrap: wrap;
    
    .column {
      flex: 0 0 calc(50% - 12px);
      
      &:last-child {
        flex: 0 0 100%;
        margin-top: 16px;
      }
    }
  }
  
  .column {
    flex: 1;
  }
}

.field-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.field-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: var(--text-color, #333333);
}

.field-content {
  padding: 12px;
  background-color: var(--input-bg, #F8F8F9);
  border-radius: 8px;
  border: 1px solid var(--input-border, #E0E0E0);
  position: relative;
  
  p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
    color: var(--input-text, #666666);
  }
  
  /* Remove internal padding from app-form-field since the field-content already has padding */
  ::ng-deep app-form-field {
    .form-input, .form-textarea {
      padding: 8px 0 !important;
      background-color: transparent !important;
      border: none !important;
      box-shadow: none !important;
      
      &:focus {
        outline: none !important;
      }
    }
    
    .form-input {
      height: 24px !important; /* Consistent height for single-line inputs */
    }
    
    .label {
      display: none; /* Hide label since we have field-title instead */
    }
  }
}

/* Position the enhance button at the bottom of the content area */
.field-content .enhance-button {
  position: absolute;
  bottom: 5px;
  right: 12px;
  z-index: 10;
}

/* Field height consistency classes */
::ng-deep .short-field {
  .form-textarea, .form-input {
    min-height: 24px !important;
    height: 24px !important;
    max-height: 24px !important;
    overflow-y: hidden !important;
    resize: none !important;
    line-height: 1.5 !important;
  }
  
  /* Ensure consistent height across all screens */
  @media (max-width: 992px) {
    .form-textarea, .form-input {
      height: 24px !important;
    }
  }
}

::ng-deep .tall-field {
  .form-textarea {
    min-height: 140px !important;
    height: 140px !important;
    max-height: 140px !important;
    overflow-y: auto !important;
    resize: none !important;
    line-height: 1.5 !important;
    padding-right: 75px !important; /* Create space for the enhance button - horizontal */
    padding-bottom: 48px !important; /* Increased further to ensure enough space */
  }
  
  /* Ensure consistent height across all screens */
  @media (max-width: 992px) {
    .form-textarea {
      min-height: 120px !important;
      height: 120px !important;
      padding-bottom: 48px !important; /* Add this to ensure responsive consistency */
    }
  }
}

.enhance-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 5px 10px;
  background-color: var(--edit-field-bg);
  border: 1px solid transparent;
  background-image: linear-gradient(var(--edit-field-bg), var(--edit-field-bg)), 
    var(--button-gradient);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px var(--edit-enhance-button-shadow);
  z-index: 5; /* Ensure button stays on top */

  span {
    background: var(--button-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
  
  &:hover {
    background-image: linear-gradient(var(--edit-enhance-button-bg-hover), var(--edit-enhance-button-bg-hover)), 
      var(--button-gradient);
    box-shadow: 0 2px 6px var(--edit-enhance-button-shadow-hover);
  }
  
  svg {
    width: 14px;
    height: 14px;
    
    path {
      stroke: var(--svg-stroke-url);
    }
  }
}

/* Suggested prompt section specific styles */
::ng-deep app-suggested-prompt {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0 24px;
  
  // Add some margin above form actions when suggested prompt is shown
  + .form-actions {
    margin-top: 16px;
  }
  
  .suggested-prompt-container {
    margin-top: 0;
    transition: all 0.3s ease;
    flex: 1;
    
    &.ng-enter, &.ng-leave {
      transition: all 0.3s ease;
    }
    
    &.ng-enter {
      opacity: 0;
      transform: translateY(-10px);
    }
    
    &.ng-enter-active {
      opacity: 1;
      transform: translateY(0);
    }
    
    &.ng-leave {
      opacity: 1;
      transform: translateY(0);
    }
    
    &.ng-leave-active {
      opacity: 0;
      transform: translateY(-10px);
    }
  }
}

/* Model Config Specific Styles */
.model-config-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 16px;
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: visible; /* Allow horizontal overflow for potential nested dropdowns */
  z-index: 5; /* Lower than the dropdown z-index */
}

.config-row {
  display: flex;
  gap: 24px;
  width: 100%;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
}

.config-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color, #333333);
  
  .tokens-used {
    font-weight: normal;
    color: var(--text-secondary, #666666);
    font-size: 12px;
    margin-left: 8px;
  }
}

.split-size-container {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.split-size-slider {
  flex: 1;
  height: 4px;
  appearance: none;
  background-color: var(--agent-slider-bg, #e0e0e0);
  border-radius: 2px;
  outline: none;
  
  &::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background-color: var(--agent-slider-thumb-bg, #6566CD);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--agent-slider-thumb-shadow, none);
  }
  
  &::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background-color: var(--agent-slider-thumb-bg, #6566CD);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: var(--agent-slider-thumb-shadow, none);
  }
}

.split-size-input {
  width: 50px;
  padding: 6px;
  border: 1px solid var(--agent-slider-input-border, #e0e0e0);
  border-radius: 6px;
  text-align: center;
  background-color: var(--agent-slider-input-bg, #ffffff);
  color: var(--agent-slider-input-text, #333333);
}

.config-text-input {
  width: 100%;
  padding: 6px 12px;
  border: 1px solid var(--input-border, #E0E0E0);
  border-radius: 4px;
  font-size: 14px;
}

.token-usage {
  font-size: 12px;
  color: var(--text-secondary);
  text-align: right;
  margin-top: 8px;
  
  .tokens-used {
    color: var(--text-tertiary);
  }
}

/* General dropdown styling */
::ng-deep app-select-dropdown {
  .select-container {
    position: relative;
    z-index: 100; /* Ensure dropdown is above other content */
  }
  
  .dropdown-menu {
    position: absolute;
    z-index: 101; /* Higher than the container */
    max-height: 250px;
    overflow-y: auto;
    width: 100%;
    left: 0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.2s ease;
  }
}

/* Custom animation for standard dropdown */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Custom animation for upward opening dropdown */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Knowledge Base specific styles */
.knowledge-base-config-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 16px;
  padding: 24px;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: visible; /* Allow horizontal overflow for potential nested dropdowns */
  z-index: 5; /* Lower than the dropdown z-index */
}

.section {
  margin-bottom: 24px;
}

.retriever-options {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
  margin-top: 16px;
  
  @media (max-width: 576px) {
    flex-direction: column;
    gap: 12px;
  }
}

.retriever-button {
  padding: 8px 16px;
  border: 1px solid var(--agent-retriever-btn-border, #e0e0e0);
  border-radius: 10px;
  background-color: var(--agent-retriever-btn-bg, #f8f9fa);
  color: var(--agent-retriever-btn-text, #333);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: var(--agent-retriever-btn-hover-bg, #f0f0f0);
    border-color: var(--agent-retriever-btn-hover-border, #d0d0d0);
  }
  
  &.selected {
    background-color: var(--agent-retriever-btn-selected-bg, #E6E4FB);
    color: var(--agent-retriever-btn-selected-text, #6566CD);
    border-color: var(--agent-retriever-btn-selected-border, #6566CD);
  }
}

.split-section {
  margin-bottom: 16px;
}

.file-table-container {
  margin-top: 16px;
  border: 1px solid var(--agent-table-border, #E0E0E0);
  border-radius: 8px;
  overflow: hidden;
  max-height: 200px;
  overflow-y: auto;
}

.file-table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 8px 16px;
    text-align: left;
    font-size: 14px;
    border-bottom: 1px solid var(--agent-table-border, #E0E0E0);
  }
  
  th {
    background-color: var(--agent-table-header-bg, #f8f8f9);
    font-weight: 600;
    color: var(--agent-table-header-text, #333333);
    position: sticky;
    top: 0;
    z-index: 1;
  }
  
  td {
    color: var(--agent-table-cell-text, #666666);
  }
  
  tr:last-child td {
    border-bottom: none;
  }

  tr:hover {
    background-color: var(--agent-table-row-hover, #f5f5fa);
  }
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.delete-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  
  svg {
    stroke: var(--text-tertiary, #999999);
  }
  
  &:hover {
    background-color: var(--agent-edit-btn-bg, rgba(249, 108, 171, 0.1));
    
    svg {
      stroke: var(--button-end-gradient, #F96CAB);
    }
  }
}

.file-upload-area {
  margin-top: 16px;
}

.upload-info {
  margin-bottom: 12px;
}

.upload-text {
  font-size: 12px;
  color: var(--text-tertiary, #999999);
  margin: 0;
}

.drag-drop-area {
  border: 2px dashed var(--agent-retriever-btn-border, #E0E0E0);
  border-radius: 8px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--agent-slider-input-bg, rgba(248, 248, 249, 0.5));
  position: relative;
  min-height: 120px;
  
  &:hover {
    border-color: var(--agent-retriever-btn-selected-border, #6566CD);
    background-color: var(--agent-retriever-btn-hover-bg, rgba(101, 102, 205, 0.05));
  }
  
  p {
    font-size: 14px;
    color: var(--agent-retriever-btn-text, #666666);
    margin: 0 0 12px;
  }
  
  .file-input {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
  }
}

/* Tool section specific styles */
.selected-tools-section {
  margin-top: 24px;
}

.sub-section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px;
  color: var(--text-color, #333333);
}

.tools-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
  
  ::ng-deep .card-container {
    height: 100%;
    background-color: var(--tools-card-bg, #f5f5f5);
    border: 1px solid var(--tools-card-border, #e0e0e0);
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--tools-card-shadow, rgba(0, 0, 0, 0.05));
    transition: all 0.2s ease;
    padding: 16px;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px var(--tools-card-hover-shadow, rgba(0, 0, 0, 0.1));
      border-color: var(--tools-card-hover-border, #d0d0d0);
    }
  }
}

/* Custom styles for app-data-card in the tools section */
.tools-cards-container ::ng-deep app-data-card {
  .card-container {
    padding: 16px;
  }
  
  .card-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    color: var(--tools-card-title, #333);
  }
  
  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 12px;
  }
  
  .tag {
    background-color: var(--tools-card-tag-bg, #eeeeee);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    color: var(--tools-card-tag-text, #555);
  }
  
  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
  }
  
  .created-date {
    font-size: 12px;
    color: var(--tools-card-date, #777);
  }
  
  .action-buttons {
    display: flex;
    gap: 8px;
  }
}

/* Styles for data cards */
.data-card {
  overflow: hidden;
  cursor: pointer;
  height: 100%;
  background-color: var(--agent-data-card-bg);
  border: 1px solid var(--agent-data-card-border);
  border-radius: 8px;
  box-shadow: 0 2px 4px var(--agent-data-card-shadow);
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--agent-data-card-hover-shadow);
    border-color: var(--agent-data-card-hover-border);
  }
}

.data-card-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.tags-section {
  margin-top: 4px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  padding: 4px 8px;
  background-color: var(--agent-tag-bg);
  border-radius: 16px;
  font-size: 12px;
  color: var(--agent-tag-text);
  white-space: nowrap;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  padding-top: 8px;
}

.created-date {
  font-size: 12px;
  color: var(--agent-data-card-date);
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  color: var(--text-secondary, #666);
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--tools-card-action-hover-bg, #f5f5f5);
    color: var(--tools-card-action-hover-color, #6566CD);
  }
  
  &.delete-icon:hover {
    color: var(--tools-card-delete-hover-color, #FF4757);
  }
}

/* Multi-select dropdown styles */
::ng-deep .multi-select {
  .selected-value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

::ng-deep .checkbox-container {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

::ng-deep .checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid var(--input-border, #E0E0E0);
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.checked {
    background-color: #6566CD;
    border-color: #6566CD;
  }
}

::ng-deep .option-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.empty-tools-state {
  margin-top: 24px;
  text-align: center;
  color: var(--text-secondary, #666666);
  font-size: 14px;
  padding: 32px 16px;
  background-color: var(--agent-tools-empty-bg);
  border-radius: 8px;
  border: 1px dashed var(--agent-tools-empty-border);
}
