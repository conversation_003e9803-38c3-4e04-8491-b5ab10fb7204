.create-knowledge-base-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: transparent;
}

form {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.form-layout {
  display: flex;
  flex-direction: row;
  gap: 20px;
  padding: 20px;
  flex: 1;
  overflow: hidden;
  align-items: stretch;
  height: 100%;
  overflow-y: auto;
  
  @media (max-width: 1400px) {
    gap: 20px;
  }
  
  @media (max-width: 1200px) {
    gap: 16px;
    padding: 16px;
  }
  
  @media (max-width: 992px) {
    flex-direction: column;
  }
  
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px;
  }

  .left-column, .right-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: calc(100vh - 180px);
    max-height: 800px;
    
    @media (max-width: 1200px) {
      gap: 16px;
      height: calc(100vh - 160px);
    }
    
    @media (max-width: 992px) {
      width: 100%;
      height: auto;
      max-height: none;
    }
    
    @media (max-width: 576px) {
      gap: 12px;
    }
  }

  .left-column {
    width: 35%;
    flex-shrink: 0;
    overflow-y: auto;
    
    @media (max-width: 1400px) {
      width: 40%;
    }
    
    @media (max-width: 1200px) {
      width: 40%;
    }
    
    app-card {
      flex-shrink: 0;
    }
    
    app-card:first-of-type {
      flex: 0 0 auto;
    }
    
    app-card:last-of-type {
      flex: 1;
    }
  }

  .right-column {
    width: 65%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    
    @media (max-width: 1400px) {
      width: 60%;
    }
    
    @media (max-width: 1200px) {
      width: 60%;
    }
  }
}

.card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  
  @media (max-width: 576px) {
    padding: 12px;
    gap: 12px;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
  color: var(--text-color);
  
  @media (max-width: 576px) {
    font-size: 14px;
    margin-bottom: 6px;
  }
}

// Section styling
.section {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.split-section {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

// Retriever selection styling
.retriever-options {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
}

.retriever-button {
  padding: 8px 16px;
  border: 1px solid var(--agent-retriever-btn-border);
  border-radius: 10px;
  background-color: var(--agent-retriever-btn-bg);
  color: var(--agent-retriever-btn-text);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: var(--agent-retriever-btn-hover-bg);
    border-color: var(--agent-retriever-btn-hover-border);
  }
  
  &.selected {
    background-color: var(--agent-retriever-btn-selected-bg);
    color: var(--agent-retriever-btn-selected-text);
    border-color: var(--agent-retriever-btn-selected-border);
  }
}

// Split size slider styling
.split-size-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.split-size-slider {
  flex: 1;
  height: 4px;
  appearance: none;
  background-color: var(--agent-slider-bg);
  border-radius: 2px;
  outline: none;
  
  &::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background-color: var(--agent-slider-thumb-bg);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--agent-slider-thumb-shadow);
  }
  
  &::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background-color: var(--agent-slider-thumb-bg);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: var(--agent-slider-thumb-shadow);
  }
}

.split-size-input {
  width: 50px;
  padding: 6px;
  border: 1px solid var(--agent-slider-input-border);
  border-radius: 6px;
  text-align: center;
  background-color: var(--agent-slider-input-bg);
  color: var(--agent-slider-input-text);
}

// Model selection section styling
.model-selection-section {
  width: 30%;
  margin-top: 24px;
  padding-top: 16px;
}

// File table styling
.file-table-container {
  margin-bottom: 20px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--agent-table-border);
  border-radius: 8px;
}

.file-table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--agent-table-border);
  }
  
  th {
    background-color: var(--agent-table-header-bg);
    font-weight: 600;
    color: var(--agent-table-header-text);
  }
  
  tr:last-child td {
    border-bottom: none;
  }

  tr:hover {
    background-color: var(--agent-table-row-hover);
  }

  td {
    color: var(--agent-table-cell-text);
  }
}

.file-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

// Delete button in file table
.delete-button {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  
  &:hover {
    color: var(--error-color);
    background-color: var(--error-bg);
  }
}

// File upload styling
.file-upload-area {
  border: 1px dashed var(--button-secondary-border);
  border-radius: 8px;
  padding: 16px;
}

.upload-info {
  margin-bottom: 12px;
}

.upload-text {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
  font-style: italic;
}

.drag-drop-area {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  background-color: var(--dropdown-bg);
  border-radius: 6px;
  position: relative;
  cursor: pointer;
  
  p {
    margin: 0;
    font-size: 14px;
    color: var(--text-secondary);
  }
  
  .file-input {
    position: absolute;
    inset: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 1;
  }
}

// Embedding model selection styling
.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.add-new-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: transparent;
  border: 1px solid var(--button-secondary-border);
  border-radius: 6px;
  color: var(--button-secondary-text);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: var(--button-secondary-hover-bg);
  }
  
  svg {
    stroke: var(--button-secondary-text);
  }
}

.model-search {
  margin-bottom: 16px;
}

.search-input-container {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  
  .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
  }
  
  .search-input {
    width: 100%;
    padding: 10px 40px 10px 40px;
    border: 1px solid var(--form-input-border);
    border-radius: 8px;
    font-size: 14px;
    background-color: var(--form-input-bg);
    color: var(--form-input-color);
    
    &:focus {
      outline: none;
      border-color: var(--form-input-focus-border);
    }
  }
  
  .filter-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
    color: var(--text-secondary);
    
    &:hover svg {
      stroke: var(--dashboard-primary);
    }
  }
}

.model-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  
  .model-card {
    display: flex;
    flex-direction: column;
    padding: 16px;
    border: 1px solid var(--card-border);
    border-radius: 8px;
    background-color: var(--dropdown-bg);
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      box-shadow: 0 4px 8px var(--card-shadow);
    }
    
    &.selected {
      background-color: var(--card-bg);
      border: 2px solid transparent;
      border-radius: 12px;
      background-clip: padding-box;
      position: relative;
      color: var(--text-color);
      
      position: relative;
      z-index: 0;
      
      &::before {
        content: "";
        position: absolute;
        z-index: -2;
        inset: 0;
        padding: 1px; /* Control border width */
        border-radius: 16px; /* Should match parent border-radius */
        background: var(--button-gradient);
        -webkit-mask: 
          linear-gradient(#fff 0 0) content-box, 
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
      }
    }
    
    .model-name {
      font-size: 16px;
      font-weight: 500;
      margin: 0 0 4px;
      color: var(--text-color);
    }
    
    .model-tags {
      font-size: 12px;
      color: var(--text-secondary);
      margin: 0;
    }
  }
}

// Bottom buttons
.right-column-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 16px 0 4px;
  margin-top: 10px;
  margin-bottom: 0;
  flex-shrink: 0;
  
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px 0 0;
    margin-top: 8px;
    margin-bottom: 0;
  }
  
  .exit-button, .save-button {
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    @media (max-width: 576px) {
      padding: 8px 16px;
      font-size: 13px;
    }
  }
  
  .exit-button {
    background-color: transparent;
    border: 1px solid var(--button-secondary-border);
    color: var(--button-secondary-text);
    
    &:hover {
      background-color: var(--button-secondary-hover-bg);
    }
  }
  
  .save-button {
    background: var(--button-gradient);
    border: none;
    color: var(--button-primary-text);
    
    &:hover {
      opacity: var(--button-hover-opacity);
    }
  }
}

::ng-deep app-card {
  display: flex;
  flex-direction: column;
  flex: 1;
  
  .card-container {
    background: var(--card-bg) !important;
    border: 1px solid var(--card-border) !important;
    box-shadow: 0 2px 4px var(--card-shadow) !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    background-image: none !important;
    display: flex !important;
    flex-direction: column !important;
    flex: 1 !important;
  }
} 