.layout-identified-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  &.light-theme {
    background: #ffffff;
  }

  &.dark-theme {
    background: #1e1e1e;
  }
}

.layout-animation-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.layout-preview {
  width: 100%;
  height: 100%;
  position: relative;
  background: #ffffff;
  opacity: 1;
  transform: scale(1);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    opacity: 1;
    transform: scale(1);
  }
}

.dark-theme .layout-preview {
  background: #1e1e1e;
}

/* Common elements */
.header {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  height: 15%;
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 3rem;
}

.header::after {
  content: '';
  position: absolute;
  left: 3rem;
  width: 200px;
  height: 20px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
}

.body {
  background: #ffffff;
  flex: 1;
  position: relative;
  overflow: hidden;
}

.dark-theme .body {
  background: #2d3748;
}

.body::before {
  content: '';
  position: absolute;
  top: 3rem;
  left: 3rem;
  right: 3rem;
  height: 12px;
  background: linear-gradient(90deg, #e2e8f0, transparent);
  border-radius: 6px;
  animation: contentShimmer 2s ease-in-out infinite;
}

.dark-theme .body::before {
  background: linear-gradient(90deg, #4a5568, transparent);
}

.body::after {
  content: '';
  position: absolute;
  top: 6rem;
  left: 3rem;
  right: 4rem;
  height: 8px;
  background: linear-gradient(90deg, #f1f5f9, transparent);
  border-radius: 4px;
  animation: contentShimmer 2s ease-in-out infinite 0.5s;
}

.dark-theme .body::after {
  background: linear-gradient(90deg, #2d3748, transparent);
}

@keyframes contentShimmer {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.sidebar {
  background: linear-gradient(180deg, #4299e1 0%, #3182ce 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 3rem 2rem;
}

.sidebar::before,
.sidebar::after {
  content: '';
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  margin-bottom: 1.5rem;
  height: 16px;
}

.sidebar::before { width: 80%; }
.sidebar::after { width: 60%; }

.footer {
  background: linear-gradient(90deg, #2d3748 0%, #4a5568 100%);
  height: 12%;
  width: 100%;
  position: relative;
}

.footer::after {
  content: '';
  position: absolute;
  right: 3rem;
  top: 50%;
  transform: translateY(-50%);
  width: 120px;
  height: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
}

/* Layout 1: HB */
.hb {
  display: flex;
  flex-direction: column;
}

.hb .body {
  animation: fadeContent 3s ease-in-out infinite;
}

@keyframes fadeContent {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Layout 2: HBF */
.hbf {
  display: flex;
  flex-direction: column;
}

.hbf .footer {
  animation: slideFooter 4s ease-in-out infinite;
}

@keyframes slideFooter {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8px); box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15); }
}

/* Layout 3: HLSB */
.hlsb {
  display: flex;
  flex-direction: column;
}

.hlsb-content {
  display: flex;
  flex: 1;
}

.hlsb .sidebar {
  width: 280px;
  animation: responsiveHide 4s ease-in-out infinite;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes responsiveHide {
  0%, 60% {
    width: 280px;
    opacity: 1;
    transform: translateX(0);
  }
  70%, 90% {
    width: 0px;
    opacity: 0;
    transform: translateX(-100%);
  }
  100% {
    width: 280px;
    opacity: 1;
    transform: translateX(0);
  }
}

/* Layout 4: HLSBF */
.hlsbf {
  display: flex;
  flex-direction: column;
}

.hlsbf-content {
  display: flex;
  flex: 1;
}

.hlsbf .sidebar {
  width: 260px;
  animation: responsiveCollapseWithFooter 4s ease-in-out infinite;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes responsiveCollapseWithFooter {
  0%, 50% {
    width: 260px;
    opacity: 1;
    transform: translateX(0);
  }
  60%, 80% {
    width: 0px;
    opacity: 0;
    transform: translateX(-100%);
  }
  100% {
    width: 260px;
    opacity: 1;
    transform: translateX(0);
  }
}

/* Layout 5: HBRS */
.hbrs {
  display: flex;
  flex-direction: column;
}

.hbrs-content {
  display: flex;
  flex: 1;
}

.hbrs .sidebar {
  width: 260px;
  animation: rightSidebarResponsive 4s ease-in-out infinite;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes rightSidebarResponsive {
  0%, 60% {
    width: 260px;
    opacity: 1;
    transform: translateX(0);
  }
  70%, 90% {
    width: 0px;
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    width: 260px;
    opacity: 1;
    transform: translateX(0);
  }
}

/* Layout 6: HBRSF */
.hbrsf {
  display: flex;
  flex-direction: column;
}

.hbrsf-content {
  display: flex;
  flex: 1;
}

.hbrsf .sidebar {
  width: 280px;
  animation: rightSidebarResponsiveFooter 4s ease-in-out infinite;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes rightSidebarResponsiveFooter {
  0%, 50% {
    width: 280px;
    opacity: 1;
    transform: translateX(0);
  }
  60%, 80% {
    width: 0px;
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    width: 280px;
    opacity: 1;
    transform: translateX(0);
  }
}

/* Layout 7: HLSBRS */
.hlsbrs {
  display: flex;
  flex-direction: column;
}

.hlsbrs-content {
  display: flex;
  flex: 1;
}

.hlsbrs .sidebar:first-child {
  width: 220px;
  animation: dualSidebarLeftResponsive 5s ease-in-out infinite;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.hlsbrs .sidebar:last-child {
  width: 220px;
  animation: dualSidebarRightResponsive 5s ease-in-out infinite;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dualSidebarLeftResponsive {
  0%, 40% {
    width: 220px;
    opacity: 1;
    transform: translateX(0);
  }
  50%, 70% {
    width: 0px;
    opacity: 0;
    transform: translateX(-100%);
  }
  80%, 100% {
    width: 220px;
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes dualSidebarRightResponsive {
  0%, 30% {
    width: 220px;
    opacity: 1;
    transform: translateX(0);
  }
  40%, 60% {
    width: 0px;
    opacity: 0;
    transform: translateX(100%);
  }
  70%, 100% {
    width: 220px;
    opacity: 1;
    transform: translateX(0);
  }
}

/* Layout 8: HLSBRSF */
.hlsbrsf {
  display: flex;
  flex-direction: column;
}

.hlsbrsf-content {
  display: flex;
  flex: 1;
}

.hlsbrsf .sidebar:first-child {
  width: 200px;
  animation: complexLeftSidebarResponsive 6s ease-in-out infinite;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.hlsbrsf .sidebar:last-child {
  width: 200px;
  animation: complexRightSidebarResponsive 6s ease-in-out infinite;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes complexLeftSidebarResponsive {
  0%, 35% {
    width: 200px;
    opacity: 1;
    transform: translateX(0);
  }
  45%, 65% {
    width: 0px;
    opacity: 0;
    transform: translateX(-100%);
  }
  75%, 100% {
    width: 200px;
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes complexRightSidebarResponsive {
  0%, 25% {
    width: 200px;
    opacity: 1;
    transform: translateX(0);
  }
  35%, 55% {
    width: 0px;
    opacity: 0;
    transform: translateX(100%);
  }
  65%, 100% {
    width: 200px;
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive indicators */
.responsive-indicator {
  position: absolute;
  top: 2rem;
  right: 2rem;
  width: 16px;
  height: 16px;
  background: #48bb78;
  border-radius: 50%;
  animation: responsivePulse 2s ease-in-out infinite;
  box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);
}

@keyframes responsivePulse {
  0% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
    box-shadow: 0 0 0 15px rgba(72, 187, 120, 0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(72, 187, 120, 0);
  }
}
