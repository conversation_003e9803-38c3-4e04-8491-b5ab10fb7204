import { Component, OnInit, OnD<PERSON>roy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ThemeService } from '../../services/theme/theme.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-theme-toggle',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './theme-toggle.component.html',
  styleUrls: ['./theme-toggle.component.scss'],
  // Use OnPush change detection for better performance
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ThemeToggleComponent implements OnInit, OnDestroy {
  currentTheme: 'light' | 'dark' = 'light';
  private themeSubscription: Subscription | null = null;
  
  constructor(private themeService: ThemeService) {}
  
  ngOnInit(): void {
    // Subscribe to theme changes
    this.currentTheme = this.themeService.getCurrentTheme();
    this.themeSubscription = this.themeService.themeObservable.subscribe(theme => {
      this.currentTheme = theme;
    });
  }
  
  ngOnDestroy(): void {
    // Clean up subscription to prevent memory leaks
    if (this.themeSubscription) {
      this.themeSubscription.unsubscribe();
    }
  }
  
  /**
   * Toggle between light and dark themes
   * Using markForCheck instead of triggering change detection directly
   */
  toggleTheme(): void {
    // Call the theme service to toggle the theme
    this.themeService.toggleTheme();
    
    // Pre-fetch current theme to avoid lag in rendering
    this.currentTheme = this.themeService.getCurrentTheme();
  }
} 