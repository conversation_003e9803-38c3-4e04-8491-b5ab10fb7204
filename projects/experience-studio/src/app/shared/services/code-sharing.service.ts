import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AppStateService } from './app-state.service';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class CodeSharingService {
  private deployedUrlSource = new BehaviorSubject<string | null>(null);
  deployedUrl$ = this.deployedUrlSource.asObservable();

  // Use the AppStateService for generated code
  generatedCode$: Observable<any>;

  constructor(private appStateService: AppStateService) {
    // Get generated code from the AppStateService
    this.generatedCode$ = this.appStateService.project$.pipe(
      map(project => project.generatedCode)
    );
  }

  setGeneratedCode(code: any) {
    // Update both the local BehaviorSubject and the AppStateService
    this.appStateService.setGeneratedCode(code);
  }

  getGeneratedCode() {
    // Get the generated code from the AppStateService
    return this.appStateService.getProjectState().generatedCode;
  }

  setDeployedUrl(url: string) {
    this.deployedUrlSource.next(url);
  }

  getDeployedUrl() {
    return this.deployedUrlSource.getValue();
  }

  /**
   * Reset all state in the service
   */
  resetState() {
    // Reset the deployed URL
    this.deployedUrlSource.next(null);

    // The generatedCode is managed by AppStateService, which will be reset separately
  }
}
