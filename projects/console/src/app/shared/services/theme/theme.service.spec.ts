import { TestBed } from '@angular/core/testing';
import { ThemeService } from './theme.service';

describe('ThemeService', () => {
  let service: ThemeService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(ThemeService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should default to light theme if no theme is saved', () => {
    localStorage.removeItem('console-theme');
    service = new ThemeService();
    expect(service.getCurrentTheme()).toBe('light');
  });

  it('should toggle theme correctly', () => {
    // Start with light theme
    service.setTheme('light');
    expect(service.getCurrentTheme()).toBe('light');
    
    // Toggle to dark
    service.toggleTheme();
    expect(service.getCurrentTheme()).toBe('dark');
    
    // Toggle back to light
    service.toggleTheme();
    expect(service.getCurrentTheme()).toBe('light');
  });
}); 