<div class="mobile-frame-container" [class]="theme + '-theme'">
  <!-- Mobile Device Frame -->
  <div class="device-frame-container">
    <!-- Action buttons - top-right -->


    <div class="smartphone">
      <div class="screen">
        <div class="notch"></div>
        <div class="content-area">
          <iframe
            #mobileIframe
            *ngIf="currentPageContent"
            [safeSrcdoc]="currentPage?.content || ''"
            class="mobile-iframe"
            frameborder="0"
            sandbox="allow-same-origin allow-scripts allow-top-navigation-by-user-activation">
          </iframe>

          <!-- Placeholder when no content -->
          <div *ngIf="!currentPageContent" class="no-content-placeholder">
            <div class="placeholder-icon">📱</div>
            <div class="placeholder-text">No content available</div>
          </div>
        </div>
      </div>

      <!-- Side buttons -->
      <div class="side-button volume-up"></div>
      <div class="side-button volume-down"></div>
      <div class="side-button power-button"></div>
    </div>
  </div>

  <!-- Pagination Controls -->
  <div class="pagination-container" *ngIf="pages.length > 0">
    <div class="navigation-container">
      <!-- Previous Button -->
      <button
        class="nav-button"
        (click)="onPreviousPage()"
        [disabled]="currentPageIndex === 0"
        title="Previous page">
        ‹
      </button>

      <!-- Pill Container with Home Icon and Text -->
      <div class="pill-container">
        <svg class="home-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
        </svg>
        <span class="pill-text">{{ getPageDisplayName(currentPage || pages[0]) }}</span>
      </div>
      <div class="action-buttons">
        <!-- Download button -->
        <button
          class="action-button download-button"
          (click)="onDownloadClick()"
          [disabled]="!currentPage"
          title="Download as PNG">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M8.5 1a.5.5 0 0 0-1 0v5.793L5.354 4.646a.5.5 0 1 0-.708.708l3 3a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 6.793V1z" fill="currentColor"/>
            <path d="M3 9.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5z" fill="currentColor"/>
            <path d="M2.5 12a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 0-.5-.5h-11z" fill="currentColor"/>
          </svg>
        </button>

        <!-- Fullscreen button -->
        <button
          class="action-button fullscreen-button"
          (click)="onFullscreenClick()"
          [disabled]="!currentPage"
          title="Open in fullscreen">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M2 2h4v2H4v2H2V2zM10 2h4v4h-2V4h-2V2zM2 10v4h4v-2H4v-2H2zM12 10v2h-2v2h4v-4h-2z" fill="currentColor"/>
          </svg>
        </button>
      </div>
      <!-- Next Button -->
      <button
        class="nav-button"
        (click)="onNextPage()"
        [disabled]="currentPageIndex === pages.length - 1"
        title="Next page">
        ›
      </button>
    </div>
  </div>
</div>
