import { Component, Input, Output, EventEmitter, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AnimationBuilder, AnimationFactory, AnimationPlayer, style, animate } from '@angular/animations';

@Component({
  selector: 'app-modal',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './modal.component.html',
  styleUrls: ['./modal.component.scss']
})
export class ModalComponent {
  @Input() title: string = '';
  @Input() isOpen: boolean = false;
  @Input() size: 'small' | 'medium' | 'large' | 'fullscreen' = 'medium';
  @Input() closeOnClickOutside: boolean = true;
  @Input() showCloseButton: boolean = true;
  @Input() padding: boolean = true;
  
  @Output() close = new EventEmitter<void>();
  
  private player: AnimationPlayer | null = null;
  
  constructor(private animationBuilder: AnimationBuilder) {}
  
  /**
   * Handle click events within the modal container
   * @param event The mouse event
   */
  onContainerClick(event: MouseEvent): void {
    // Only close if clicking directly on the container (not its children)
    if (this.closeOnClickOutside && event.target === event.currentTarget) {
      this.closeModal();
    }
  }
  
  /**
   * Close the modal
   */
  closeModal(): void {
    if (this.player) {
      this.player.destroy();
    }
    
    // Create fade out animation
    const factory = this.animationBuilder.build([
      style({ opacity: 1 }),
      animate('250ms ease-out', style({ opacity: 0 }))
    ]);
    
    this.player = factory.create(document.querySelector('.modal-container'));
    this.player.play();
    
    // Emit close event after animation completes
    this.player.onDone(() => {
      this.close.emit();
      this.player = null;
    });
  }
  
  /**
   * Handle escape key press to close the modal
   * @param event The keyboard event
   */
  @HostListener('document:keydown.escape')
  onEscapeKey(): void {
    if (this.isOpen) {
      this.closeModal();
    }
  }
  
  /**
   * Get CSS class for modal content based on size
   */
  get modalContentClass(): string {
    return `modal-content modal-${this.size} ${this.padding ? '' : 'no-padding'}`;
  }
} 