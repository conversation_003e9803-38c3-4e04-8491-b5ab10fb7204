import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UserSignatureService } from './user-signature.service';
import { ApiRetryService } from './api-retry.service';
import { Observable } from 'rxjs';
import { createLogger } from '../utils/logger';

/**
 * Service Factory to create services dynamically and avoid circular dependencies
 */
@Injectable({
  providedIn: 'root'
})
export class ServiceFactoryService {
  private logger = createLogger('ServiceFactoryService');

  constructor(
    private http: HttpClient,
    private userSignatureService: UserSignatureService,
    private apiRetryService: ApiRetryService
  ) {}

  /**
   * Create a WireframeCodeGenerationService instance dynamically
   * This helps avoid circular dependencies between components and services
   * @returns Promise that resolves to the service instance
   */
  async createWireframeService(): Promise<any> {
    try {
      // Dynamically import the service
      const module = await import('./wireframe-code-generation.service');

      // Create a new instance of the service
      const wireframeService = new module.WireframeCodeGenerationService(
        this.http,
        this.userSignatureService,
        this.apiRetryService
      );

      this.logger.debug('WireframeCodeGenerationService created successfully');
      return wireframeService;
    } catch (error) {
      this.logger.error('Error creating WireframeCodeGenerationService:', error);
      throw new Error('Failed to create WireframeCodeGenerationService');
    }
  }

  /**
   * Generate wireframe code using the dynamically created service
   * @param request The wireframe code generation request
   * @param userSignature User signature for tracking
   * @returns Observable with the wireframe code generation response
   */
  async generateWireframeCode(request: any, userSignature: string): Promise<Observable<any>> {
    const service = await this.createWireframeService();
    return service.generateWireframeCode(request, userSignature);
  }

  /**
   * Map selections to wireframe request using the dynamically created service
   * @param prompt User prompt
   * @param formFactor Form factor (web, mobile, etc.)
   * @param technology Technology (angular, react, etc.)
   * @param designLibrary Design library (material, tailwind, etc.)
   * @param projectId Optional project ID
   * @param cardTitle Card title to determine project type
   * @returns Promise that resolves to the formatted request object
   */
  async mapSelectionsToWireframeRequest(
    prompt: string,
    formFactor: string | null,
    technology: string | null,
    designLibrary: string | null,
    projectId?: string,
    cardTitle?: string
  ): Promise<any> {
    const service = await this.createWireframeService();
    return service.mapSelectionsToWireframeRequest(
      prompt,
      formFactor,
      technology,
      designLibrary,
      projectId,
      cardTitle
    );
  }
}
