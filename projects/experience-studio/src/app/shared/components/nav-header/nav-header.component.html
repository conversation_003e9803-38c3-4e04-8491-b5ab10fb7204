<awe-header theme="light" class="py-1">
  <div left-content class="mt-4">
    <img [src]="logoSrc" class="px-2" alt="Ascendion Logo" loading="eager" />
  </div>
  <div right-content class="gap-4 d-flex align-items-center">
    <div
      class="d-flex justify-content-center align-items-center cursor-pointer"
      (click)="toggleTheme()">
      <img [src]="themeToggleIcon" alt="Toggle Theme" loading="lazy" />
    </div>
    <div>
      <img
        class="cursor-pointer profile-icon"
        [src]="getProfileImage()"
        [alt]="getDisplayName()"
        (click)="toggleProfileMenu()"
        loading="lazy" />
      <div
        *ngIf="showProfileMenu"
        class="p-3 card profile-flyout"
        role="menu"
        aria-label="Profile menu">
        <div class="d-flex align-items-center gap-2">
          <img
            class="profile-avatar"
            [src]="getProfileImage()"
            [alt]="getDisplayName()"
            loading="lazy" />
          <div>
            <awe-heading variant="s2" type="bold" class="profile-name">{{
              getDisplayName()
            }}</awe-heading>
            <awe-heading variant="s2" type="regular" class="profile-email">{{
              getEmail()
            }}</awe-heading>
          </div>
        </div>
        <hr />
        <awe-button
          label="Sign Out"
          variant="primary"
          animation="ripple"
          size="medium"
          class="gradient-button"
          (click)="onLogout()"></awe-button>
      </div>
    </div>
  </div>
</awe-header>
