import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { trigger, transition, style, animate } from '@angular/animations';
import { RouterOutlet } from '@angular/router';
import { NavHeaderComponent } from './shared/components/nav-header/nav-header.component';
import HeroComponent from './shared/components/hero/hero.component';
import { GlobalStoreService } from './shared/service/global-store.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, RouterOutlet, NavHeaderComponent, HeroComponent],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  animations: [
    trigger('fadeInUp', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate(
          '0.6s ease-out',
          style({ opacity: 1, transform: 'translateY(0)' }),
        ),
      ]),
    ]),
  ],
})
export class AppComponent {
  selectedUser: any;

  constructor(private readonly globalStoreService: GlobalStoreService) {}

  getSelectedUser(value: any) {
    this.globalStoreService.selectedUser.next(value);
  }
}
