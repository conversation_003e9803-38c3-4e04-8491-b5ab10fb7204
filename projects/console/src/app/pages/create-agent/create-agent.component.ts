import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { CardComponent } from '../../shared/components/card/card.component';

@Component({
  selector: 'app-create-agent',
  standalone: true,
  imports: [CommonModule, CardComponent],
  templateUrl: './create-agent.component.html',
  styleUrl: './create-agent.component.scss'
})
export class CreateAgentComponent {
  constructor(private router: Router) {}

  selectAgentType(type: 'individual' | 'collaborative'): void {
    console.log(`Selected agent type: ${type}`);
    
    if (type === 'individual') {
      this.router.navigate(['/launch/agents/create/individual']);
    } else if (type === 'collaborative') {
      this.router.navigate(['/launch/agents/create/collaborative']);
    }
  }
}
