<div class="workflow-editor-container">
  <!-- Main content area with left sidebar and right editor -->
  <div class="editor-layout">
    <!-- Left sidebar for workflow details and agent library -->
    <div class="sidebar">
      <!-- Workflow Details -->
      <app-card>
        <div class="sidebar-section">
          <h3>Workflow Name</h3>
          <app-form-field
            [control]="getControl('name')"
            placeholder="Enter workflow name"
            type="text"
          ></app-form-field>

          <h3 class="description-label">Description</h3>
          <app-form-field
            [control]="getControl('description')"
            placeholder="Describe your workflow's purpose"
            type="textarea"
          ></app-form-field>
        </div>
      </app-card>

      <app-card>
        <!-- Agent Library -->
        <div class="sidebar-section agent-library">
          <h3>Agent Library</h3>
          <p class="help-text">
            Drag and drop agents to establish connection and execute workflow
          </p>

          <!-- Search Filter -->
          <div class="search-filter">
            <app-form-field
              [control]="getControl('agentFilter')"
              placeholder="Search agents"
              type="text"
            ></app-form-field>
          </div>

          <!-- Draggable Agents Container with Fixed Height and Scroll -->
          <div class="agent-list-container">
            <div class="agent-list">
              <div
                *ngFor="let agent of availableAgents"
                class="agent-item"
                draggable="true"
                (dragstart)="onDragStart($event, agent)"
              >
                <h4>{{ agent.name }}</h4>
                <p>{{ agent.description }}</p>
                <div
                  class="agent-tags"
                  *ngIf="agent.capabilities && agent.capabilities.length > 0"
                >
                  <span
                    class="agent-tag"
                    *ngFor="let capability of agent.capabilities"
                    >{{ capability }}</span
                  >
                </div>
              </div>

              <!-- No results message -->
              <div class="no-agents" *ngIf="availableAgents.length === 0">
                No agents found matching your search criteria
              </div>
            </div>
          </div>
        </div>

        <!-- Enable Manager LLM -->
        <div class="sidebar-section llm-toggle">
          <div class="toggle-container">
            <label class="switch">
              <input
                type="checkbox"
                [formControl]="getControl('enableManagerLLM')"
              />
              <span class="slider"></span>
            </label>
            <span class="toggle-label">Enable Manager LLM</span>
          </div>

          <!-- LLM Settings - Only visible when enableManagerLLM is true -->
          <div
            class="llm-settings"
            *ngIf="getControl('enableManagerLLM').value"
          >
            <!-- Temperature -->
            <div class="setting-item">
              <h3>Temperature</h3>
              <div class="slider-with-value">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  [formControl]="getControl('temperature')"
                  class="setting-slider"
                />
                <div class="value-display">
                  {{ getControl("temperature").value }}
                </div>
              </div>
            </div>

            <!-- Top P -->
            <div class="setting-item">
              <h3>Top P</h3>
              <input
                type="number"
                min="0"
                max="1"
                step="0.01"
                [formControl]="getControl('topP')"
                class="setting-input"
              />
            </div>

            <!-- Max RPM -->
            <div class="setting-item">
              <h3>Max RPM</h3>
              <input
                type="number"
                min="0"
                [formControl]="getControl('maxRPM')"
                class="setting-input"
              />
            </div>

            <!-- Max Token -->
            <div class="setting-item">
              <h3>Max Token</h3>
              <div class="token-container">
                <input
                  type="number"
                  min="0"
                  [formControl]="getControl('maxToken')"
                  class="setting-input"
                />
                <span class="tokens-used">
                  {{ getControl("maxToken").value }}/8192 Tokens used
                </span>
              </div>
            </div>

            <!-- Max Iteration -->
            <div class="setting-item">
              <h3>Max Iteration</h3>
              <input
                type="number"
                min="1"
                [formControl]="getControl('maxIteration')"
                class="setting-input"
              />
            </div>

            <!-- Max Execution Time -->
            <div class="setting-item">
              <h3>Max Execution Time</h3>
              <input
                type="number"
                min="0"
                [formControl]="getControl('maxExecutionTime')"
                class="setting-input"
              />
            </div>
          </div>
        </div>
      </app-card>
    </div>

    <!-- Right side workflow editor canvas -->

    <div class="editor-canvas">
      <h2>Workflow Editor</h2>
      <div class="navigation-hint">
        <span><kbd>Alt</kbd> + <kbd>Drag</kbd> to pan canvas</span>
        <span><kbd>Mouse Wheel</kbd> to zoom</span>
        <span><kbd>Space</kbd> to reset view</span>
      </div>
      <div
        class="react-flow-container"
        #reactFlowCanvas
        (dragover)="onDragOver($event)"
        (drop)="onDrop($event)"
        (mouseup)="onCanvasMouseUp($event)"
        (mousemove)="onCanvasMouseMove($event)"
        (wheel)="onCanvasWheel($event)"
        (mousedown)="onCanvasMouseDown($event)"
      >
        <div
          class="workflow-viewport"
          [style.transform]="
            'translate(' +
            viewport.x +
            'px, ' +
            viewport.y +
            'px) scale(' +
            viewport.zoom +
            ')'
          "
        >
          <!-- All elements in the same transformed container -->
          <div class="manual-nodes-container">
            <!-- Connections between nodes -->
            <svg class="connections-layer">
              <!-- Static connections -->
              <path
                *ngFor="let edge of edges"
                [attr.d]="getEdgePath(edge)"
                [attr.id]="edge.id"
                class="edge-path"
                [class.animated]="edge.animated"
                marker-end="url(#arrow)"
              ></path>

              <!-- Arrow marker definition -->
              <defs>
                <marker
                  id="arrow"
                  viewBox="0 0 10 10"
                  refX="8"
                  refY="5"
                  markerWidth="6"
                  markerHeight="6"
                  orient="auto-start-reverse"
                >
                  <path d="M 0 0 L 10 5 L 0 10 z" class="arrow-head"></path>
                </marker>
              </defs>
            </svg>

            <!-- Nodes -->
            <app-agent-node
              *ngFor="let node of nodes"
              [node]="node"
              [selected]="selectedNodeId === node.id"
              (deleteNode)="onDeleteNode($event)"
              (moveNode)="onMoveNode($event)"
              (nodeSelected)="onNodeSelected($event)"
              (startConnection)="onStartConnection($event)"
              (resizeNode)="onResizeNode($event)"
              (nodePositionChanged)="updateNodePosition($event)"
            >
            </app-agent-node>

            <!-- Temporary connection line - render on top -->
            <svg *ngIf="tempConnection.isActive" class="temp-connection-layer">
              <path
                [attr.d]="getTempConnectionPath()"
                class="temp-connection-path"
                marker-end="url(#arrow)"
              ></path>
            </svg>

            <!-- Fallback message -->
            <div *ngIf="nodes.length === 0" class="no-nodes-message">
              Drag agents from the library to create a workflow
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom action buttons -->
      <div class="action-buttons">
        <button type="button" class="btn-reset" (click)="onReset()">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4.01 7.58 4.01 12C4.01 16.42 7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z"
              fill="currentColor"
            />
          </svg>
        </button>
        <button type="button" class="btn-execute" (click)="onExecute()">
          Execute
        </button>
      </div>
    </div>
  </div>
</div>
