@use '../../../assets/styles/mixins' as mixins;
#prompt-content-container {
  margin-top: 7%;
  #suggetions-contianer {
    .invisible {
      visibility: hidden !important;
    }
  }

  #divider-section-container {
    padding-top: 4rem;
    .divider-image {
      width: 100%;
      @media (max-width: 767px) {
        max-width: 90%;
      }
    }
  }
}

.disabled-prompt-bar {
  @include mixins.disabledProperty(0.7, none, not-allowed);
  ::ng-deep textarea {
    @include mixins.disabledProperty(0.7, none, not-allowed);
  }
}

.custom-content {
  awe-file-attach-pill.disabled {
    @include mixins.disabledProperty(0.5, none, not-allowed);
  }
  .selected-files {
    width: 100%;
    .file-item {
      background-color: var(--preview-page-bg-color) !important;
      border: 1px solid var(--code-viewer-search-border) !important;
      max-width: 200px;
      .file-preview {
        .file-preview-image {
          width: 24px;
          height: 24px;
          object-fit: cover;
          transition: transform 0.2s ease;
          &:hover {
            transform: scale(1.1);
          }
        }
      }
    }
  }

  .enhance-icons {
    justify-content: flex-end;
    flex-shrink: 0;
    awe-icons {
      cursor: pointer;
      transition: all 0.3s ease-in-out;
      font-size: 24px;
      color: var(--icon-enabled-color) !important;
      &.disabled {
        @include mixins.disabledProperty(0.4, none, not-allowed);
        color: var(--icon-disabled-color) !important;
      }
    }
    .loading-spinner {
      min-width: auto;
      width: 24px;
      height: 24px;
      .spinner {
        width: 20px;
        height: 20px;
        border: 2px solid var(--icon-enabled-color);
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

.preview-overlay {
  animation: fadeIn 0.3s ease-in-out;
  .preview-content {
    background: var(--preview-page-bg-color);
    border-radius: 8px;
    animation: scaleIn 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    .preview-header .preview-title {
      color: var(--pill-text-color);
    }
  }
}

:host ::ng-deep button.secondary {
  border: 1px solid var(--button-border-color);
  color: var(--button-text-color);
  border-radius: 0.5rem;
  background: var(--prompt-bar-suggestion-button-bg);
  &:hover {
    border-color: var(--prompt-bar-hover-color);
  }
}

:host {
  ::ng-deep {
    .prompt-bar {
      &.dark {
        @include mixins.prompt-bar-style(none, breatheDark, rgba(255, 92, 163, 1));
      }
      &.light {
        @include mixins.prompt-bar-style(
          var(--Light---60, rgba(240, 240, 245, 0.5)),
          breatheLight,
          rgba(66, 68, 194, 0.8)
        );
      }
    }
  }
}

:host ::ng-deep .prompt-bar.disabled {
  @include mixins.disabledProperty(0.7, none, not-allowed);
  border-color: var(--prompt-bar-disabled-border, #ccc) !important;
  background-color: var(--prompt-bar-disabled-bg, rgba(240, 240, 245, 0.3)) !important;
  animation: none !important;
}

:host ::ng-deep .prompt-bar.disabled .prompt-text {
  color: var(--prompt-bar-disabled-text, #999) !important;
}

::ng-deep .file-attach-pill {
  min-width: unset !important;
  transition: all 0.4s ease-in-out !important;
  awe-icons path {
    fill: var(--pill-text-color) !important;
  }
}

::ng-deep.icon-pill {
  min-width: unset !important;
  transition: all 0.4s ease-in-out !important;
}

::ng-deep awe-icons path {
  fill: var(--pill-text-color) !important;
}

::ng-deep {
  .file-attach-pill .icon-wrapper {
    margin-top: 8px !important;
  }
  .file-attach-pill .text,
  .icon-pill .text,
  .dropdown-item .dropdown-item-text {
    color: var(--pill-text-color) !important;
  }
  .dropdown-item .dropdown-item-text {
    width: max-content !important;
  }
  .dropdown-item {
    width: 100% !important;
  }
  .dropdown.show {
    background-color: var(--preview-page-bg-color) !important;
  }
  .dropdown-item:hover,
  .dropdown-item:focus {
    background-color: transparent !important;
  }
}
.tools-container {
  width: 100%;
}
.pills-container {
  flex: 1;
}
#prompt-bar-container {
  width: 100%;
  ::ng-deep .prompt-bar-wrapper {
    width: 100%;
  }
  ::ng-deep .prompt-input-wrapper {
    width: 100%;
  }
  ::ng-deep .text-input-container {
    width: 100%;
  }
}