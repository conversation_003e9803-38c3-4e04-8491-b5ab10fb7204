import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface DialogConfig {
  showHeader?: boolean;
  showFooter?: boolean;
  showCloseButton?: boolean;
  title?: string;
  width?: string;
  height?: string;
  backdrop?: boolean;
}

export interface DropdownItem {
  label: string;
  action: string;
  icon?: string;
}

@Component({
  selector: 'app-edit-dialog',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './edit-dialog.component.html',
  styleUrl: './edit-dialog.component.scss'
})
export class EditDialogComponent implements OnInit, OnDestroy {
  @Input() isVisible: boolean = false;
  @Input() config: DialogConfig = {
    showHeader: true,
    showFooter: true,
    showCloseButton: true,
    backdrop: true
  };
  @Input() dropdownItems: DropdownItem[] = [
    { label: 'Edit', action: 'edit' },
    { label: 'Delete', action: 'delete' }
  ];

  @Output() closeDialog = new EventEmitter<void>();
  @Output() dropdownAction = new EventEmitter<string>();

  isDropdownOpen: boolean = false;

  constructor() {}

  ngOnInit(): void {
    // Add event listener to close dropdown when clicking outside
    document.addEventListener('click', this.closeDropdownOnOutsideClick.bind(this));
  }

  ngOnDestroy(): void {
    // Remove event listener to prevent memory leaks
    document.removeEventListener('click', this.closeDropdownOnOutsideClick.bind(this));
  }

  onClose(): void {
    this.closeDialog.emit();
  }

  onBackdropClick(event: Event): void {
    if (this.config.backdrop && event.target === event.currentTarget) {
      this.onClose();
    }
  }

  toggleDropdown(event: Event): void {
    event.stopPropagation();
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  onDropdownItemClick(action: string, event: Event): void {
    event.stopPropagation();
    this.dropdownAction.emit(action);
    this.isDropdownOpen = false;
  }

  private closeDropdownOnOutsideClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdownContainer = document.querySelector('.dropdown-container');

    if (dropdownContainer && !dropdownContainer.contains(target)) {
      this.isDropdownOpen = false;
    }
  }
}
