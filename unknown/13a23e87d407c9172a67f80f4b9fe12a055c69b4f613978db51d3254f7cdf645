<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.4">
<g opacity="0.5" filter="url(#filter0_f_6342_16339)">
<circle cx="112.5" cy="876.5" r="215.5" fill="url(#paint0_linear_6342_16339)"/>
</g>
<g filter="url(#filter1_f_6342_16339)">
<ellipse cx="1715.5" cy="315.982" rx="419.5" ry="456.294" fill="#E30A6D"/>
</g>
<g opacity="0.32" filter="url(#filter2_f_6342_16339)">
<circle cx="1749" cy="772" r="413" fill="#6566CD"/>
</g>
</g>
<defs>
<filter id="filter0_f_6342_16339" x="-403" y="361" width="1031" height="1031" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_6342_16339"/>
</filter>
<filter id="filter1_f_6342_16339" x="896" y="-540.312" width="1639" height="1712.59" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_6342_16339"/>
</filter>
<filter id="filter2_f_6342_16339" x="1036" y="59" width="1426" height="1426" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_6342_16339"/>
</filter>
<linearGradient id="paint0_linear_6342_16339" x1="113.5" y1="551" x2="112.5" y2="1092" gradientUnits="userSpaceOnUse">
<stop stop-color="#FA91C0"/>
<stop offset="1" stop-color="#E30A6D"/>
</linearGradient>
</defs>
</svg>
