# Elder Wand Microfrontend Architecture

This project demonstrates a microfrontend architecture using Angular Module Federation. It consists of two applications:
- `elderWand` (Host Application)
- `experienceStudio` (Remote Application)
- `awe-components` (Shared Component Library)

## Table of Contents
- [Elder Wand Microfrontend Architecture](#elder-wand-microfrontend-architecture)
  - [Table of Contents](#table-of-contents)
  - [Architecture Overview](#architecture-overview)
  - [Prerequisites](#prerequisites)
  - [Local Development](#local-development)
  - [Creating a New Microfrontend](#creating-a-new-microfrontend)
  - [Using Remote Components](#using-remote-components)
  - [Deployment Guide](#deployment-guide)
    - [1. Azure Static Web Apps Setup](#1-azure-static-web-apps-setup)
    - [2. Azure DevOps Pipeline Setup](#2-azure-devops-pipeline-setup)
    - [3. Environment Configuration](#3-environment-configuration)
  - [Troubleshooting](#troubleshooting)
    - [Common Issues](#common-issues)
    - [Useful Commands](#useful-commands)
  - [Additional Resources](#additional-resources)
  - [Component Library (awe-components)](#component-library-awe-components)

## Architecture Overview

```mermaid
graph TD
    A[Elder Wand - Host App] -->|Imports| B[Experience Studio - Remote App]
    B -->|Exposes| C[Components]
    B -->|Exposes| D[Services]
    A -->|Uses| C
    A -->|Uses| D
```

## Prerequisites

- Node.js 20.x
- Angular CLI 19.x
- Azure Account
- Azure DevOps Account

## Local Development

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Start the Applications**:
   ```bash
   # Start Elder Wand (Host)
   npm run start:elder-wand    # Runs on http://localhost:4200

   # Start Experience Studio (Remote)
   npm run start:experience-studio    # Runs on http://localhost:4201
   ```

3. **Build Applications**:
   ```bash
   # Build both applications
   npm run build:all

   # Build individually
   npm run build:elder-wand
   npm run build:experience-studio
   ```

## Creating a New Microfrontend

1. **Generate New Angular Application**:
   ```bash
   # Create a new application with routing
   ng g application new-remote-app --routing

   # Add Module Federation capability
   ng add @angular-architects/module-federation --project new-remote-app --port 4202
   ```

   Note: Port numbers should be incremented for each new application:
   - elderWand (Host): 4200
   - experienceStudio: 4201
   - new-remote-app: 4202
   - next-remote-app: 4203 (and so on)

2. **Update Module Federation Configuration**:
   
   The `webpack.config.js` will be automatically created with the basic setup. Update it according to your needs:
   ```javascript
   const { ModuleFederationPlugin } = require('webpack').container;

   module.exports = {
     output: {
       publicPath: 'auto',
       uniqueName: 'newRemoteApp'
     },
     optimization: {
       runtimeChunk: false
     },
     plugins: [
       new ModuleFederationPlugin({
         name: 'newRemoteApp',
         filename: 'remoteEntry.js',
         exposes: {
           './Component': './projects/new-remote-app/src/app/app.component.ts'
         },
         shared: {
           '@angular/core': { singleton: true },
           '@angular/common': { singleton: true },
           '@angular/router': { singleton: true }
         }
       })
     ]
   };
   ```

3. **Update Angular Configuration**:
   
   The `angular.json` will be automatically updated with the new project and its Module Federation configuration. Verify the following:
   ```json
   {
     "projects": {
       "new-remote-app": {
         "architect": {
           "build": {
             "builder": "@angular-architects/module-federation:browser",
             "options": {
               "port": 4202
             }
           },
           "serve": {
             "builder": "@angular-architects/module-federation:dev-server",
             "options": {
               "port": 4202
             }
           }
         }
       }
     }
   }
   ```

4. **Add Start and Build Scripts**:
   
   Update `package.json` with new scripts:
   ```json
   {
     "scripts": {
       "start:new-remote-app": "ng serve new-remote-app",
       "build:new-remote-app": "ng build new-remote-app",
       "build:all": "npm run build:elder-wand && npm run build:experience-studio && npm run build:new-remote-app"
     }
   }
   ```

5. **Configure Static Web App**:
   
   Create `staticwebapp.config.json` in your new app's src directory:
   ```json
   {
     "navigationFallback": {
       "rewrite": "/index.html",
       "exclude": ["/images/*", "/css/*", "/js/*", "/assets/*"]
     },
     "cors": {
       "allowedOrigins": ["https://*.azurestaticapps.net"]
     }
   }
   ```

6. **Start the Development Server**:
   ```bash
   # Start all applications
   npm run start:elder-wand        # http://localhost:4200
   npm run start:experience-studio # http://localhost:4201
   npm run start:new-remote-app   # http://localhost:4202
   ```

## Using Remote Components

1. **In the Host Application (Elder Wand)**:

   ```typescript
   // app.routes.ts
   import { loadRemoteModule } from '@angular-architects/module-federation';

   export const routes: Routes = [
     {
       path: 'experience',
       loadChildren: () =>
         loadRemoteModule({
           type: 'module',
           remoteEntry: '${EXPERIENCE_STUDIO_URL}/remoteEntry.js',
           exposedModule: './Component'
         }).then(m => m.AppModule)
     }
   ];
   ```

2. **In Your Component**:
   ```typescript
   import { ExperienceComponent } from '@experience-studio/Component';

   @Component({
     template: '<experience-component></experience-component>'
   })
   export class AppComponent {}
   ```

## Deployment Guide

### 1. Azure Static Web Apps Setup

1. **Create Static Web Apps**:
   - Go to Azure Portal → Create Resource
   - Search for "Static Web App"
   - Create two apps (one for each microfrontend)
   - Basic settings:
     - Name: `elder-wand` and `experience-studio`
     - Hosting Plan: Free
     - Region: Select nearest

2. **Get Deployment Tokens**:
   - Go to each Static Web App
   - Navigate to Configuration → Deployment token
   - Copy the tokens

### 2. Azure DevOps Pipeline Setup

1. **Add Pipeline Variables**:
   - Go to Pipelines → Edit → Variables
   - Add these variables:
     ```
     AZURE_STATIC_WEB_APPS_API_TOKEN_ELDER_WAND: <token>
     AZURE_STATIC_WEB_APPS_API_TOKEN_EXPERIENCE_STUDIO: <token>
     ELDER_WAND_URL: <elder-wand-url>
     ```

2. **Configure Pipeline**:
   - Create/Update `azure-pipelines.yml`
   - Ensure correct build and deployment steps
   - Configure triggers and paths

### 3. Environment Configuration

1. **Update URLs**:
   - Get the URLs from Azure Static Web Apps
   - Update the Module Federation configuration
   - Update CORS settings if needed

## Troubleshooting

### Common Issues

1. **Build Errors**:
   - Verify project names in `angular.json`
   - Check output paths match deployment configuration
   - Ensure all dependencies are installed

2. **Runtime Errors**:
   - Check CORS configuration
   - Verify remote entry URLs
   - Check browser console for module loading errors

3. **Deployment Issues**:
   - Verify pipeline variables
   - Check build output paths
   - Validate Static Web App configuration

### Useful Commands

```bash
# Check build output
ls -la dist/elder-wand
ls -la dist/experience-studio

# Verify local development
npm run start:elder-wand
npm run start:experience-studio

# Clean and rebuild
rm -rf dist
npm ci
npm run build:all
```

## Additional Resources

- [Angular Module Federation Documentation](https://www.npmjs.com/package/@angular-architects/module-federation)
- [Azure Static Web Apps Documentation](https://docs.microsoft.com/azure/static-web-apps)
- [Microfrontend Architecture Guide](https://martinfowler.com/articles/micro-frontends.html)

## Component Library (awe-components)

The project includes a shared component library that can be used across all microfrontends:

1. **Installation within the workspace**:
   ```bash
   # The library is already available in the workspace
   # Just import the components you need
   import { ButtonComponent } from '@elderwand/awe-components';
   ```

2. **Available Components**:
   - `awe-button`: A customizable button component
     ```html
     <awe-button
       variant="primary"
       size="medium"
       [loading]="isLoading"
       [disabled]="isDisabled"
       (clicked)="handleClick($event)">
       Click Me
     </awe-button>
     ```

3. **Building the Library**:
   ```bash
   # Build the library
   ng build awe-components

   # Build the library in watch mode during development
   ng build awe-components --watch
   ```

4. **Publishing the Library**:
   ```bash
   # Build the library
   ng build awe-components --configuration production

   # Navigate to the dist folder
   cd dist/awe-components

   # Publish to NPM (requires NPM login)
   npm publish
   ```

5. **Local Development**:
   - The library is built using standalone components
   - Uses SCSS for styling
   - Includes TypeScript types
   - Supports tree-shaking
   - Follows Angular best practices
