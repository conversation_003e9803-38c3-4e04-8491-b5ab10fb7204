import { Routes } from '@angular/router';

export const PRODUCT_STUDIO_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./product-studio.component').then(m => m.ProductStudioComponent),
    children: [
      {
        path: 'dashboard',
        loadComponent: () =>
          import('./main-dashboard/main-dashboard.component').then(
            m => m.MainDashboardComponent
          ),
      },
      {
        path: 'brainstormer',
        loadChildren: () =>
          import('./brainstormer/brainstormer-routing').then(m => m.BRAINSTORMER_ROUTES),
      },   
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full',
      },
    ],
  },
];
