import { CardData } from '../models/card.model';

export const MOCK_TOOLS: CardData[] = [
  {
    id: '1',
    title: 'API Integration Tool',
    tags: [
      { label: 'API' },
      { label: 'Integration' }
    ],
    createdDate: '2/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'developer',
    client: 'technology',
    department: 'engineering',
    role: 'integration-specialist',
    project: 'api-gateway',
    category: 'integration'
  },
  {
    id: '2',
    title: 'Data Extraction Tool',
    tags: [
      { label: 'Data' },
      { label: 'Extraction' }
    ],
    createdDate: '1/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'analyst',
    client: 'financial',
    department: 'data-science',
    role: 'data-engineer',
    project: 'data-platform',
    category: 'extraction'
  },
  {
    id: '3',
    title: 'Document Processor',
    tags: [
      { label: 'Document' },
      { label: 'Processing' }
    ],
    createdDate: '3/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'business',
    client: 'legal',
    department: 'document-management',
    role: 'content-manager',
    project: 'document-automation',
    category: 'processing'
  },
  {
    id: '4',
    title: 'Image Recognition Tool',
    tags: [
      { label: 'Image' },
      { label: 'Recognition' },
      { label: 'AI' }
    ],
    createdDate: '2/28/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'developer',
    client: 'technology',
    department: 'ai-research',
    role: 'ml-engineer',
    project: 'computer-vision',
    category: 'ai'
  },
  {
    id: '5',
    title: 'Notification System',
    tags: [
      { label: 'Notification' },
      { label: 'System' }
    ],
    createdDate: '1/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'developer',
    client: 'services',
    department: 'communications',
    role: 'backend-developer',
    project: 'messaging-platform',
    category: 'communication'
  },
  {
    id: '6',
    title: 'PDF Generation Tool',
    tags: [
      { label: 'PDF' },
      { label: 'Generation' }
    ],
    createdDate: '2/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'business',
    client: 'publishing',
    department: 'content-creation',
    role: 'document-specialist',
    project: 'document-generation',
    category: 'document'
  },
  {
    id: '7',
    title: 'Database Migration Tool',
    tags: [
      { label: 'Database' },
      { label: 'Migration' }
    ],
    createdDate: '3/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'developer',
    client: 'technology',
    department: 'database-admin',
    role: 'db-engineer',
    project: 'cloud-migration',
    category: 'migration'
  },
  {
    id: '8',
    title: 'Inventory Management Tool',
    tags: [
      { label: 'Inventory' },
      { label: 'Management' }
    ],
    createdDate: '1/25/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'business',
    client: 'retail',
    department: 'operations',
    role: 'operations-manager',
    project: 'inventory-system',
    category: 'management'
  },
  {
    id: '9',
    title: 'Natural Language Processor',
    tags: [
      { label: 'NLP' },
      { label: 'AI' }
    ],
    createdDate: '2/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'developer',
    client: 'technology',
    department: 'ai-research',
    role: 'ml-engineer',
    project: 'nlp',
    category: 'ai'
  },
  {
    id: '10',
    title: 'Payment Processing Tool',
    tags: [
      { label: 'Payment' },
      { label: 'Processing' }
    ],
    createdDate: '1/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'business',
    client: 'financial',
    department: 'finance',
    role: 'payment-specialist',
    project: 'payment-gateway',
    category: 'finance'
  },
  {
    id: '11',
    title: 'SEO Analysis Tool',
    tags: [
      { label: 'SEO' },
      { label: 'Analysis' }
    ],
    createdDate: '3/1/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'business',
    client: 'marketing',
    department: 'digital-marketing',
    role: 'seo-specialist',
    project: 'website-optimization',
    category: 'analysis'
  },
  {
    id: '12',
    title: 'Data Visualization Tool',
    tags: [
      { label: 'Data' },
      { label: 'Visualization' }
    ],
    createdDate: '2/12/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Tool' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Tool' }
    ],
    // Filter properties
    userType: 'analyst',
    client: 'business-intelligence',
    department: 'data-analysis',
    role: 'data-analyst',
    project: 'dashboard',
    category: 'visualization'
  }
]; 