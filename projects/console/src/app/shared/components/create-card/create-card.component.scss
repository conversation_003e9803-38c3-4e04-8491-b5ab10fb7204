::ng-deep .create-card {
  border: 1px solid var(--card-border, rgba(255, 255, 255, 0.2));
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--card-bg, linear-gradient(102.14deg, rgba(255, 255, 255, 0.56) 1.07%, rgba(255, 255, 255, 0.64) 98.01%));
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  height: 190px; /* Match the fixed height of data cards */

  &:hover {
    border-color: var(--card-hover-border, rgba(102, 109, 153, 0.3));
    box-shadow: 0 6px 20px var(--card-hover-shadow, rgba(0, 0, 0, 0.08));
    transform: translateY(-2px);
    
    .create-icon {
      color: var(--nav-active-text, #666D99);
      background-color: var(--create-icon-hover-bg, rgba(102, 109, 153, 0.15));
    }
    
    .create-label {
      color: var(--text-color, #333);
    }
  }
}

.create-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 16px;
  text-align: center;
  width: 100%;
  gap: 12px;
}

.create-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: var(--create-icon-bg, rgba(102, 109, 153, 0.1));
  color: var(--create-icon-color, #858AAD);
  transition: all 0.3s ease;
}

.create-label {
  font-size: 18px;
  font-weight: 500;
  color: var(--create-label-color, #666D99);
  transition: color 0.3s ease;
} 