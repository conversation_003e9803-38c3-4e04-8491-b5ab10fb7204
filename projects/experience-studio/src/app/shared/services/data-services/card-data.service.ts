import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CardDataService {
  private selectedCardTitleSource = new BehaviorSubject<string>('');
  readonly selectedCardTitle$: Observable<string> = this.selectedCardTitleSource.asObservable();

  setSelectedCardTitle(title: string): void {
    this.selectedCardTitleSource.next(title);
  }
}