<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="monitorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="screenWebGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a8edea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fed6e3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="standGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Monitor screen -->
  <rect x="3" y="4" width="18" height="12" rx="1.5" ry="1.5" fill="url(#monitorGradient)" stroke="#2563eb" stroke-width="0.5"/>
  
  <!-- Screen content -->
  <rect x="4" y="5" width="16" height="10" rx="0.8" ry="0.8" fill="url(#screenWebGradient)"/>
  
  <!-- Browser bar -->
  <rect x="4" y="5" width="16" height="2" rx="0.8" ry="0.8" fill="#ffffff" opacity="0.9"/>
  
  <!-- Browser buttons -->
  <circle cx="5.5" cy="6" r="0.3" fill="#ff5f56"/>
  <circle cx="6.5" cy="6" r="0.3" fill="#ffbd2e"/>
  <circle cx="7.5" cy="6" r="0.3" fill="#27ca3f"/>
  
  <!-- Address bar -->
  <rect x="9" y="5.5" width="10" height="1" rx="0.5" fill="#e5e7eb"/>
  
  <!-- Website content -->
  <rect x="5" y="8.5" width="14" height="1" rx="0.5" fill="#ffffff" opacity="0.8"/>
  <rect x="5" y="10" width="10" height="0.8" rx="0.4" fill="#ffffff" opacity="0.6"/>
  <rect x="5" y="11.5" width="12" height="0.8" rx="0.4" fill="#ffffff" opacity="0.6"/>
  <rect x="5" y="13" width="8" height="0.8" rx="0.4" fill="#ffffff" opacity="0.6"/>
  
  <!-- Website elements -->
  <rect x="16" y="9" width="2.5" height="2" rx="0.3" fill="#ffffff" opacity="0.7"/>
  <rect x="16" y="11.5" width="2.5" height="2" rx="0.3" fill="#ffffff" opacity="0.5"/>
  
  <!-- Monitor stand -->
  <rect x="10.5" y="16" width="3" height="2" rx="0.3" fill="url(#standGradient)"/>
  
  <!-- Monitor base -->
  <ellipse cx="12" cy="19" rx="4" ry="1" fill="url(#standGradient)" opacity="0.8"/>
  
  <!-- Stand connection -->
  <rect x="11.7" y="15.5" width="0.6" height="1" fill="url(#standGradient)"/>
</svg>
