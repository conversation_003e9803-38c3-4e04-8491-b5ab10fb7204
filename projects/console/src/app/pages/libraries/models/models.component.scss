@import '../../../shared/styles/header.scss';
@import '../../../shared/styles/cards.scss';

.models-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 15px 0;
  position: relative;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
  overflow: hidden;
}

.models-header {
  @extend .component-header;
}

.header-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.search-section {
  flex: 1;
  margin-right: 16px;

  ::ng-deep .search-container {
    height: 40px;
    width: 100%;
  }

  ::ng-deep .search-input {
    height: 40px;
    box-sizing: border-box;
    width: 100%;
  }
}

.action-buttons {
  flex-shrink: 0;
  display: flex;
  gap: 8px;
}

.action-button {
  padding: 0 16px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--dashboard-bg-action-button);
  border: 1px solid var(--dashboard-action-button-border);
  font-family: 'Mulish', -apple-system, 'Roboto', 'Helvetica', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--dashboard-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  svg {
    margin-left: 4px;
    transition: transform 0.3s ease;
    color: var(--dashboard-text-secondary);
  }
  
  &:hover {
    background-color: var(--dashboard-bg-lighter);
    border-color: var(--dashboard-action-button-border);
  }
  
  &.active-filter {
    background-color: var(--nav-active);
    border-color: var(--dropdown-focus-border);
    color: var(--nav-active-text);
    
    svg {
      color: var(--nav-active-text);
    }
  }
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 24px;
  position: relative;
  z-index: 100;
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 300px);
  padding-bottom: 20px;
}

app-create-card, app-data-card {
  height: 100%;
  display: block;
}

@media (max-width: 1200px) {
  .header-content {
    flex-wrap: wrap;
  }
  
  .search-section {
    width: 70%;
  }
  
  .action-buttons {
    width: 30%;
  }
  
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 992px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-section {
    width: 100%;
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: flex-start;
  }
  
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 576px) {
  .action-buttons {
    flex-wrap: wrap;
  }
  
  .action-button {
    flex: 1;
    min-width: 100px;
  }
  
  .cards-container {
    grid-template-columns: 1fr;
  }
}

.filter-section {
  width: 100%;
  overflow: visible;
  transition: max-height 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
  animation: fadeIn 0.3s ease-out;
  position: relative;
  z-index: 500;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 20px;
  background-color: var(--glass-bg);
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  color: var(--dashboard-text-secondary);
  font-size: 16px;
}

app-page-footer {
  position: sticky;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 200;
  margin-top: auto;
  flex-shrink: 0;
}
