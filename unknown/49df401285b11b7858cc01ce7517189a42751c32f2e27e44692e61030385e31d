// Shared Card Styles
// This file contains shared card styles to ensure consistent card heights across all pages and pagination

// Card styles for all components
app-create-card, app-data-card {
  height: 190px !important;
  display: block !important;
  margin-bottom: 0 !important;
  
  ::ng-deep .data-card,
  ::ng-deep .create-card,
  ::ng-deep .card-container {
    height: 190px !important;
    min-height: 190px !important;
    max-height: 190px !important;
  }
}

// Ensure cards are displayed consistently regardless of pagination or component
.cards-container {
  app-create-card, app-data-card {
    height: 190px !important;
  }
  
  ::ng-deep .card-container {
    height: 190px !important;
    min-height: 190px !important;
    max-height: 190px !important;
  }
}

// Override any dynamic height calculations that might be applied
::ng-deep .fixed-height {
  height: 190px !important;
  min-height: 190px !important;
  max-height: 190px !important;
} 