// import { TestBed } from '@angular/core/testing';
// import { FilenameNormalizationService } from './filename-normalization.service';

// describe('FilenameNormalizationService', () => {
//   let service: FilenameNormalizationService;

//   beforeEach(() => {
//     TestBed.configureTestingModule({});
//     service = TestBed.inject(FilenameNormalizationService);
//   });

//   it('should be created', () => {
//     expect(service).toBeTruthy();
//   });

//   describe('normalizeFilename', () => {
//     it('should handle spaces in filenames', () => {
//       const result = service.normalizeFilename('Login Page');
//       expect(result.canonicalKey).toBe('loginpage');
//       expect(result.displayName).toBe('Login Page');
//       expect(result.variations).toContain('Login Page');
//       expect(result.variations).toContain('login page');
//     });

//     it('should handle underscores in filenames', () => {
//       const result = service.normalizeFilename('Login_Page');
//       expect(result.canonicalKey).toBe('loginpage');
//       expect(result.displayName).toBe('Login Page');
//       expect(result.variations).toContain('Login_Page');
//       expect(result.variations).toContain('Login Page');
//     });

//     it('should handle file extensions', () => {
//       const result = service.normalizeFilename('Login_Page.html');
//       expect(result.canonicalKey).toBe('loginpage');
//       expect(result.displayName).toBe('Login Page');
//       expect(result.originalFilename).toBe('Login_Page.html');
//     });

//     it('should handle camelCase variations', () => {
//       const result = service.normalizeFilename('LoginPage');
//       expect(result.canonicalKey).toBe('loginpage');
//       expect(result.displayName).toBe('Login Page');
//       expect(result.variations).toContain('LoginPage');
//       expect(result.variations).toContain('loginPage');
//     });

//     it('should handle mixed case combinations', () => {
//       const result = service.normalizeFilename('login_Page.HTML');
//       expect(result.canonicalKey).toBe('loginpage');
//       expect(result.displayName).toBe('Login Page');
//     });

//     it('should handle empty or invalid input', () => {
//       const result = service.normalizeFilename('');
//       expect(result.canonicalKey).toBeTruthy();
//       expect(result.displayName).toContain('Page');
//       expect(result.confidence).toBe(0);
//     });

//     it('should handle null or undefined input', () => {
//       const result1 = service.normalizeFilename(null as any);
//       const result2 = service.normalizeFilename(undefined as any);

//       expect(result1.confidence).toBe(0);
//       expect(result2.confidence).toBe(0);
//     });

//     it('should generate comprehensive variations', () => {
//       const result = service.normalizeFilename('User_Profile_Settings');
//       const variations = result.variations;

//       expect(variations).toContain('User_Profile_Settings');
//       expect(variations).toContain('user_profile_settings');
//       expect(variations).toContain('User Profile Settings');
//       expect(variations).toContain('UserProfileSettings');
//       expect(variations).toContain('userProfileSettings');
//     });

//     it('should handle special characters', () => {
//       const result = service.normalizeFilename('User-Profile@Settings#Page.html');
//       expect(result.canonicalKey).toBe('userprofilesettingspage');
//       expect(result.displayName).toBe('User Profile Settings Page');
//     });

//     it('should maintain reasonable confidence scores', () => {
//       const validResult = service.normalizeFilename('LoginPage');
//       const invalidResult = service.normalizeFilename('');

//       expect(validResult.confidence).toBeGreaterThan(0.5);
//       expect(invalidResult.confidence).toBe(0);
//     });
//   });

//   describe('findMatchingNode', () => {
//     const mockNodes = [
//       { id: 'node1', title: 'Login Page', displayTitle: 'Login Page' },
//       { id: 'node2', title: 'User_Profile', displayTitle: 'User Profile' },
//       { id: 'node3', title: 'dashboard.html', displayTitle: 'Dashboard' },
//       { id: 'node4', title: 'ContactUs', displayTitle: 'Contact Us' }
//     ];

//     it('should find exact matches', () => {
//       const result = service.findMatchingNode('Login Page', mockNodes);
//       expect(result.isMatch).toBe(true);
//       expect(result.matchType).toBe('exact');
//       expect(result.matchedNodeId).toBe('node1');
//       expect(result.confidence).toBe(1.0);
//     });

//     it('should find canonical matches', () => {
//       const result = service.findMatchingNode('login_page', mockNodes);
//       expect(result.isMatch).toBe(true);
//       expect(result.matchType).toBe('canonical');
//       expect(result.matchedNodeId).toBe('node1');
//     });

//     it('should find variation matches', () => {
//       const result = service.findMatchingNode('userprofile', mockNodes);
//       expect(result.isMatch).toBe(true);
//       expect(result.matchType).toBe('variation');
//       expect(result.matchedNodeId).toBe('node2');
//     });

//     it('should handle case insensitive matching', () => {
//       const result = service.findMatchingNode('DASHBOARD', mockNodes);
//       expect(result.isMatch).toBe(true);
//       expect(result.matchedNodeId).toBe('node3');
//     });

//     it('should handle extension variations', () => {
//       const result = service.findMatchingNode('dashboard.htm', mockNodes);
//       expect(result.isMatch).toBe(true);
//       expect(result.matchedNodeId).toBe('node3');
//     });

//     it('should handle camelCase variations', () => {
//       const result = service.findMatchingNode('contact_us', mockNodes);
//       expect(result.isMatch).toBe(true);
//       expect(result.matchedNodeId).toBe('node4');
//     });

//     it('should return no match for non-existent files', () => {
//       const result = service.findMatchingNode('NonExistentPage', mockNodes);
//       expect(result.isMatch).toBe(false);
//       expect(result.matchType).toBe('none');
//       expect(result.confidence).toBe(0);
//     });

//     it('should handle empty input gracefully', () => {
//       const result = service.findMatchingNode('', mockNodes);
//       expect(result.isMatch).toBe(false);
//       expect(result.matchType).toBe('none');
//     });

//     it('should handle empty node list', () => {
//       const result = service.findMatchingNode('LoginPage', []);
//       expect(result.isMatch).toBe(false);
//       expect(result.matchType).toBe('none');
//     });
//   });

//   describe('generateVariations', () => {
//     it('should generate comprehensive variations for underscored names', () => {
//       const variations = service.generateVariations('user_profile_page');

//       expect(variations).toContain('user_profile_page');
//       expect(variations).toContain('user profile page');
//       expect(variations).toContain('User Profile Page');
//       expect(variations).toContain('userProfilePage');
//       expect(variations).toContain('UserProfilePage');
//     });

//     it('should generate variations for camelCase names', () => {
//       const variations = service.generateVariations('userProfilePage');

//       expect(variations).toContain('userProfilePage');
//       expect(variations).toContain('UserProfilePage');
//       expect(variations).toContain('user Profile Page');
//     });

//     it('should handle hyphenated names', () => {
//       const variations = service.generateVariations('user-profile-page');

//       expect(variations).toContain('user-profile-page');
//       expect(variations).toContain('user profile page');
//       expect(variations).toContain('User Profile Page');
//     });

//     it('should remove empty variations', () => {
//       const variations = service.generateVariations('test');

//       expect(variations.every(v => v.length > 0)).toBe(true);
//     });

//     it('should handle empty input', () => {
//       const variations = service.generateVariations('');
//       expect(variations).toEqual([]);
//     });
//   });

//   describe('batchNormalize', () => {
//     it('should normalize multiple filenames', () => {
//       const filenames = ['Login Page', 'User_Profile.html', 'ContactUs'];
//       const results = service.batchNormalize(filenames);

//       expect(results).toHaveLength(3);
//       expect(results[0].canonicalKey).toBe('loginpage');
//       expect(results[1].canonicalKey).toBe('userprofile');
//       expect(results[2].canonicalKey).toBe('contactus');
//     });

//     it('should handle empty array', () => {
//       const results = service.batchNormalize([]);
//       expect(results).toEqual([]);
//     });
//   });

//   describe('configuration', () => {
//     it('should allow configuration updates', () => {
//       service.updateConfig({
//         caseSensitive: true,
//         fallbackPrefix: 'CustomPage'
//       });

//       const result = service.normalizeFilename('');
//       expect(result.displayName).toContain('CustomPage');
//     });

//     it('should handle custom extensions', () => {
//       service.updateConfig({
//         removeExtensions: ['.html', '.htm', '.php', '.asp']
//       });

//       const result = service.normalizeFilename('page.php');
//       expect(result.canonicalKey).toBe('page');
//       expect(result.displayName).toBe('Page');
//     });
//   });

//   describe('edge cases', () => {
//     it('should handle very long filenames', () => {
//       const longName = 'a'.repeat(200);
//       const result = service.normalizeFilename(longName);

//       expect(result.canonicalKey.length).toBeLessThanOrEqual(100); // Default maxLength
//     });

//     it('should handle filenames with only special characters', () => {
//       const result = service.normalizeFilename('___---...');
//       expect(result.canonicalKey).toBeTruthy();
//       expect(result.displayName).toBeTruthy();
//     });

//     it('should handle unicode characters', () => {
//       const result = service.normalizeFilename('Página_de_Início');
//       expect(result.canonicalKey).toBeTruthy();
//       expect(result.displayName).toBe('Página De Início');
//     });

//     it('should handle numbers in filenames', () => {
//       const result = service.normalizeFilename('Page123_Test456');
//       expect(result.canonicalKey).toBe('page123test456');
//       expect(result.displayName).toBe('Page123 Test456');
//     });
//   });
// });
