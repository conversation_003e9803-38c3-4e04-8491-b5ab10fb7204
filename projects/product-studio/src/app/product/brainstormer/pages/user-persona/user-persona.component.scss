.user-persona {
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;

  &__title {
    font-size: 32px;
    font-weight: 400;
    text-align: center;
    margin: 0;
    padding: 40px 40px 20px 40px;
    color: #1f2937;
    letter-spacing: -0.5px;
  }

  // Stepper component styling (no container wrapper)
  app-brainstormer-stepper {
    display: block;
    padding: 20px 0;
    margin-bottom: 40px;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 40px;
  }

  &__card {
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    padding: 24px 20px;
    position: relative;
    height: fit-content;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  &__close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 24px;
    height: 24px;
    background: none;
    border: none;
    font-size: 18px;
    line-height: 1;
    color: #9ca3af;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      color: #374151;
      background: #f3f4f6;
    }
  }

  &__header {
    text-align: center;
    margin-bottom: 24px;
  }

  &__role {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 16px 0;
    padding-right: 30px;
    text-align: center;
  }

  &__avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    overflow: hidden;
    background: #f3f4f6;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #e5e7eb;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__info {
    margin-bottom: 20px;
  }

  &__info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 400;
  }

  &__value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 500;
  }

  &__quote-section {
    background: #f9fafb;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 3px solid #e5e7eb;
  }

  &__quote {
    margin: 0;
    font-style: italic;
    color: #6b7280;
    font-size: 13px;
    line-height: 1.5;
    quotes: none;

    &::before,
    &::after {
      content: '';
    }
  }

  &__personality {
    margin-top: 0;
  }

  &__section-label {
    display: block;
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 12px;
    font-weight: 500;
  }

  &__tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  &__tag {
    padding: 6px 12px;
    background: #f3f4f6;
    border-radius: 16px;
    font-size: 12px;
    color: #6b7280;
    font-weight: 400;
    border: 1px solid #e5e7eb;
  }

  &__navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 60px auto 40px;
    max-width: 1280px;
    padding: 0 40px;
  }
}

.nav-btn {
  background: #111827;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #1f2937;
  }

  &--prev {
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;

    &:hover {
      background: #f9fafb;
    }
  }

  &--next {
    background: #111827;
    color: white;

    &:hover {
      background: #1f2937;
    }
  }
}

/* Responsive Design */
@media (max-width: 1440px) {
  .user-persona {
    &__title {
      padding: 40px 32px 20px 32px;
    }

    &__grid {
      grid-template-columns: repeat(4, 1fr);
      max-width: 1200px;
      padding: 0 32px;
    }

    &__navigation {
      max-width: 1200px;
      padding: 0 32px;
    }
  }
}

@media (max-width: 1200px) {
  .user-persona {
    &__grid {
      grid-template-columns: repeat(3, 1fr);
      max-width: 900px;
    }

    &__navigation {
      max-width: 900px;
    }
  }
}

@media (max-width: 900px) {
  .user-persona {
    &__title {
      font-size: 28px;
      padding: 32px 20px 16px 20px;
    }

    &__grid {
      grid-template-columns: repeat(2, 1fr);
      max-width: 600px;
      gap: 20px;
    }

    &__navigation {
      max-width: 600px;
    }
  }
}

@media (max-width: 640px) {
  .user-persona {
    &__title {
      font-size: 24px;
      padding: 24px 20px 16px 20px;
    }

    &__grid {
      grid-template-columns: 1fr;
      gap: 16px;
      padding: 0 20px;
      max-width: none;
    }

    &__card {
      padding: 20px 16px;
    }

    &__navigation {
      padding: 0 20px;
      margin: 40px auto 20px;
      flex-direction: column;
      gap: 16px;
      max-width: none;
    }

    app-brainstormer-stepper {
      padding: 16px 0;
      margin-bottom: 24px;
    }
  }

  .nav-btn {
    width: 100%;
    order: 2;

    &--prev {
      order: 1;
    }
  }
}

@media (max-width: 480px) {
  .user-persona {
    &__title {
      font-size: 22px;
      padding: 20px 16px 12px 16px;
    }

    &__card {
      padding: 16px 12px;
    }
    
    &__role {
      font-size: 16px;
      padding-right: 24px;
    }
    
    &__avatar {
      width: 56px;
      height: 56px;
    }

    &__grid {
      padding: 0 16px;
    }

    &__navigation {
      padding: 0 16px;
      margin: 32px auto 16px;
    }

    app-brainstormer-stepper {
      padding: 12px 0;
      margin-bottom: 20px;
    }
  }
}