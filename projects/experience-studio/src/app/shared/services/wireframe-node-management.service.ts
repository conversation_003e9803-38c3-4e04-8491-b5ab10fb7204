import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { DomSanitizer } from '@angular/platform-browser';
import { createLogger } from '../utils/logger';
import { FilenameNormalizationService, NodeMatchResult } from './filename-normalization.service';
import { ToastService } from './toast.service';

/**
 * Wireframe page data from API response
 */
export interface WireframePageData {
  fileName: string;
  content: string;
  pageName?: string; // Alternative field name
}

/**
 * Node operation result
 */
export interface NodeOperationResult {
  success: boolean;
  operation: 'created' | 'updated' | 'skipped' | 'failed';
  nodeId?: string;
  filename: string;
  displayName: string;
  reason: string;
  error?: string;
}

/**
 * Batch operation summary
 */
export interface BatchOperationSummary {
  totalProcessed: number;
  created: number;
  updated: number;
  skipped: number;
  failed: number;
  results: NodeOperationResult[];
  errors: string[];
}

/**
 * Node data structure for UI Design nodes
 */
export interface UIDesignNode {
  id: string;
  type: 'ui-design';
  data: {
    title: string;
    displayTitle?: string;
    htmlContent: any; // SafeHtml from DomSanitizer
    rawContent: string;
    width: number;
    height: number;
    isLoading: boolean;
    loadingMessage?: string; // Context-specific loading message
    originalNodeId?: string; // For loading nodes, track which original node they represent
    zIndex?: number; // For z-index management
  };
  position: { x: number; y: number };
  selected: boolean;
  dragging: boolean;
  visible: boolean;
}

/**
 * Position calculation interface
 */
export interface NodePosition {
  x: number;
  y: number;
}

/**
 * Robust node management service for wireframe regeneration
 * Handles filename normalization and intelligent node creation/updates
 */
@Injectable({
  providedIn: 'root'
})
export class WireframeNodeManagementService {
  private readonly logger = createLogger('WireframeNodeManagementService');

  private readonly filenameNormalizationService = inject(FilenameNormalizationService);
  private readonly sanitizer = inject(DomSanitizer);
  private readonly toastService = inject(ToastService);

  // State management
  private readonly operationInProgress$ = new BehaviorSubject<boolean>(false);
  private readonly lastOperationSummarySubject$ = new BehaviorSubject<BatchOperationSummary | null>(null);

  // Default node dimensions
  private readonly defaultNodeSize = {
    width: 420,
    height: 720
  };

  /**
   * Get operation status observables
   */
  get isOperationInProgress$(): Observable<boolean> {
    return this.operationInProgress$.asObservable();
  }

  get lastOperationSummary$(): Observable<BatchOperationSummary | null> {
    return this.lastOperationSummarySubject$.asObservable();
  }

  /**
   * Process wireframe regeneration response and manage nodes
   */
  async processRegenerationResponse(
    apiResponse: WireframePageData[],
    existingNodes: UIDesignNode[],
    positionCalculator: (count: number, existingNodes: any[]) => NodePosition[]
  ): Promise<{
    updatedNodes: UIDesignNode[];
    summary: BatchOperationSummary;
  }> {
    this.logger.info('🔄 Processing wireframe regeneration response:', {
      responsePages: apiResponse.length,
      existingNodes: existingNodes.length
    });

    this.operationInProgress$.next(true);

    try {
      // Normalize all filenames from the response
      const normalizedPages = this.normalizeResponsePages(apiResponse);

      // Analyze which nodes need to be created vs updated
      const operationPlan = this.createOperationPlan(normalizedPages, existingNodes);

      // Execute the operations
      const results = await this.executeOperationPlan(
        operationPlan,
        existingNodes,
        positionCalculator
      );

      // Create summary
      const summary = this.createOperationSummary(results);
      this.lastOperationSummarySubject$.next(summary);

      // Show user feedback
      this.showUserFeedback(summary);

      this.logger.info('✅ Wireframe regeneration processing completed:', summary);

      return {
        updatedNodes: results.updatedNodes,
        summary
      };

    } catch (error) {
      this.logger.error('❌ Error processing wireframe regeneration:', error);

      const errorSummary: BatchOperationSummary = {
        totalProcessed: apiResponse.length,
        created: 0,
        updated: 0,
        skipped: 0,
        failed: apiResponse.length,
        results: [],
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };

      this.lastOperationSummarySubject$.next(errorSummary);
      this.toastService.error('Failed to process wireframe regeneration');

      throw error;
    } finally {
      this.operationInProgress$.next(false);
    }
  }

  /**
   * Normalize API response pages
   */
  private normalizeResponsePages(apiResponse: WireframePageData[]): Array<{
    originalData: WireframePageData;
    filename: string;
    content: string;
    normalizationResult: any;
  }> {
    return apiResponse.map(page => {
      // Handle different field names (fileName vs pageName)
      const filename = page.fileName || page.pageName || `Page_${Date.now()}`;
      const content = page.content || '';

      const normalizationResult = this.filenameNormalizationService.normalizeFilename(filename);

      return {
        originalData: page,
        filename,
        content,
        normalizationResult
      };
    });
  }

  /**
   * Create operation plan for node management
   */
  private createOperationPlan(
    normalizedPages: any[],
    existingNodes: UIDesignNode[]
  ): Array<{
    page: any;
    operation: 'create' | 'update';
    targetNode?: UIDesignNode;
    matchResult?: NodeMatchResult;
  }> {
    const plan: any[] = [];

    // Create simplified node data for matching
    const nodeData = existingNodes.map(node => ({
      id: node.id,
      title: node.data.title,
      displayTitle: node.data.displayTitle
    }));

    for (const page of normalizedPages) {
      // Find matching existing node
      const matchResult = this.filenameNormalizationService.findMatchingNode(
        page.filename,
        nodeData
      );

      if (matchResult.isMatch && matchResult.matchedNodeId) {
        // Update existing node
        const targetNode = existingNodes.find(node => node.id === matchResult.matchedNodeId);
        if (targetNode) {
          plan.push({
            page,
            operation: 'update',
            targetNode,
            matchResult
          });
        } else {
          // Node not found, create new
          plan.push({
            page,
            operation: 'create',
            matchResult
          });
        }
      } else {
        // Create new node
        plan.push({
          page,
          operation: 'create',
          matchResult
        });
      }
    }

    this.logger.info('📋 Operation plan created:', {
      totalPages: normalizedPages.length,
      createOperations: plan.filter(p => p.operation === 'create').length,
      updateOperations: plan.filter(p => p.operation === 'update').length
    });

    return plan;
  }

  /**
   * Execute the operation plan
   */
  private async executeOperationPlan(
    operationPlan: any[],
    existingNodes: UIDesignNode[],
    positionCalculator: (count: number, existingNodes: any[]) => NodePosition[]
  ): Promise<{
    updatedNodes: UIDesignNode[];
    operationResults: NodeOperationResult[];
  }> {
    const operationResults: NodeOperationResult[] = [];
    let updatedNodes = [...existingNodes];

    // Calculate positions for new nodes
    const newNodesCount = operationPlan.filter(p => p.operation === 'create').length;
    const newPositions = newNodesCount > 0 ?
      positionCalculator(newNodesCount, existingNodes) : [];

    let newNodeIndex = 0;

    for (const planItem of operationPlan) {
      try {
        if (planItem.operation === 'update') {
          // Update existing node
          const result = this.updateExistingNode(
            planItem.targetNode,
            planItem.page,
            updatedNodes
          );

          if (result.success) {
            updatedNodes = result.updatedNodes!;
          }

          operationResults.push(result.operationResult);

        } else if (planItem.operation === 'create') {
          // Create new node
          const position = newPositions[newNodeIndex] || { x: 0, y: 0 };
          newNodeIndex++;

          const result = this.createNewNode(planItem.page, position);

          if (result.success && result.newNode) {
            updatedNodes.push(result.newNode);
          }

          operationResults.push(result.operationResult);
        }

      } catch (error) {
        this.logger.error('❌ Error executing operation:', error);

        operationResults.push({
          success: false,
          operation: 'failed',
          filename: planItem.page.filename,
          displayName: planItem.page.normalizationResult.displayName,
          reason: 'Operation execution failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return {
      updatedNodes,
      operationResults
    };
  }

  /**
   * Update existing node with new content
   */
  private updateExistingNode(
    targetNode: UIDesignNode,
    page: any,
    currentNodes: UIDesignNode[]
  ): {
    success: boolean;
    updatedNodes?: UIDesignNode[];
    operationResult: NodeOperationResult;
  } {
    try {
      // Process HTML content
      const processedContent = this.processHtmlContent(page.content);
      const sanitizedContent = this.sanitizer.bypassSecurityTrustHtml(processedContent);

      // Update the node
      const updatedNodes = currentNodes.map(node => {
        if (node.id === targetNode.id) {
          return {
            ...node,
            data: {
              ...node.data,
              title: page.filename, // Keep API filename for compatibility
              displayTitle: page.normalizationResult.displayName,
              htmlContent: sanitizedContent,
              rawContent: processedContent
            }
          };
        }
        return node;
      });

      const operationResult: NodeOperationResult = {
        success: true,
        operation: 'updated',
        nodeId: targetNode.id,
        filename: page.filename,
        displayName: page.normalizationResult.displayName,
        reason: `Updated existing node "${targetNode.data.title}"`
      };

      this.logger.info('✅ Node updated successfully:', {
        nodeId: targetNode.id,
        filename: page.filename,
        displayName: page.normalizationResult.displayName
      });

      return {
        success: true,
        updatedNodes,
        operationResult
      };

    } catch (error) {
      this.logger.error('❌ Failed to update node:', error);

      return {
        success: false,
        operationResult: {
          success: false,
          operation: 'failed',
          nodeId: targetNode.id,
          filename: page.filename,
          displayName: page.normalizationResult.displayName,
          reason: 'Failed to update node content',
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  /**
   * Create new node
   */
  private createNewNode(
    page: any,
    position: NodePosition
  ): {
    success: boolean;
    newNode?: UIDesignNode;
    operationResult: NodeOperationResult;
  } {
    try {
      // Process HTML content
      const processedContent = this.processHtmlContent(page.content);
      const sanitizedContent = this.sanitizer.bypassSecurityTrustHtml(processedContent);

      // Generate unique node ID
      const nodeId = this.generateNodeId();

      // Create new node
      const newNode: UIDesignNode = {
        id: nodeId,
        type: 'ui-design',
        data: {
          title: page.filename, // Keep API filename for compatibility
          displayTitle: page.normalizationResult.displayName,
          htmlContent: sanitizedContent,
          rawContent: processedContent,
          width: this.defaultNodeSize.width,
          height: this.defaultNodeSize.height,
          isLoading: false
        },
        position,
        selected: false,
        dragging: false,
        visible: true
      };

      const operationResult: NodeOperationResult = {
        success: true,
        operation: 'created',
        nodeId,
        filename: page.filename,
        displayName: page.normalizationResult.displayName,
        reason: 'Created new node for new wireframe page'
      };

      this.logger.info('✅ New node created successfully:', {
        nodeId,
        filename: page.filename,
        displayName: page.normalizationResult.displayName,
        position
      });

      return {
        success: true,
        newNode,
        operationResult
      };

    } catch (error) {
      this.logger.error('❌ Failed to create new node:', error);

      return {
        success: false,
        operationResult: {
          success: false,
          operation: 'failed',
          filename: page.filename,
          displayName: page.normalizationResult.displayName,
          reason: 'Failed to create new node',
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  /**
   * Process HTML content for iframe rendering
   */
  private processHtmlContent(htmlContent: string): string {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return '<html><body><p>No content available</p></body></html>';
    }

    // Basic HTML enhancement (simplified version)
    const hasDoctype = htmlContent.includes('<!DOCTYPE');
    const hasHtmlTag = htmlContent.includes('<html');

    if (hasDoctype && hasHtmlTag) {
      return htmlContent;
    }

    // Wrap in complete HTML structure
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Wireframe Preview</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; }
  </style>
</head>
<body>
${htmlContent}
</body>
</html>`;
  }

  /**
   * Generate unique node ID
   */
  private generateNodeId(): string {
    return `wireframe-node-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Create operation summary
   */
  private createOperationSummary(results: { operationResults: NodeOperationResult[] }): BatchOperationSummary {
    const { operationResults } = results;

    return {
      totalProcessed: operationResults.length,
      created: operationResults.filter(r => r.operation === 'created').length,
      updated: operationResults.filter(r => r.operation === 'updated').length,
      skipped: operationResults.filter(r => r.operation === 'skipped').length,
      failed: operationResults.filter(r => r.operation === 'failed').length,
      results: operationResults,
      errors: operationResults.filter(r => r.error).map(r => r.error!)
    };
  }

  /**
   * Show user feedback based on operation results
   */
  private showUserFeedback(summary: BatchOperationSummary): void {
    if (summary.failed > 0) {
      this.toastService.error(`Failed to process ${summary.failed} wireframe(s)`);
    } else if (summary.created > 0 || summary.updated > 0) {
      const message = `Successfully processed ${summary.totalProcessed} wireframe(s): ${summary.created} created, ${summary.updated} updated`;
      this.toastService.success(message);
    }
  }
}
