.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 280px;
  background-color: var(--dropdown-bg);
  border-radius: 8px;
  box-shadow: 0 4px 16px var(--dropdown-shadow);
  border: 1px solid var(--dropdown-menu-border, var(--dropdown-border));
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.2s ease;
  z-index: 1500;
  margin-top: 8px;
  padding: 8px;

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    pointer-events: auto;
  }
}

.dropdown-columns {
  display: flex;
  flex-direction: row;
  gap: 16px;
}

.dropdown-column {
  min-width: 280px;
  display: flex;
  flex-direction: column;
} 