.news-blogs {
  padding: 60px 0;

  .header {
    text-align: center;
    margin-bottom: 48px;

    h2 {
      font-family: Mulish;
      font-size: 48px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.912px;
      display: inline-flex;
      align-items: center;
      gap: 8px;

      .star {
        font-size: 28px;
      }

      .gradient-text {
        background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 118.04%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .normal-text {
        -webkit-text-fill-color: #14161f;
        color: #14161f;
      }
    }
  }

  .blog-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }

  .blog-card {
    padding: 0.75rem;
    display: flex;
    background: var(--background-color);
    border-radius: 16px;
    overflow: hidden;
    box-shadow:
      0px 1px 2px rgba(0, 0, 0, 0.06),
      0px 1px 3px rgba(0, 0, 0, 0.1);
    transition:
      transform 0.2s ease-in-out,
      box-shadow 0.2s ease-in-out;
    height: 260px;

    &:hover {
      transform: translateY(-4px);
      box-shadow:
        0px 4px 6px rgba(0, 0, 0, 0.05),
        0px 10px 15px rgba(0, 0, 0, 0.1);
    }

    .blog-image {
      flex: 0 0 240px;
      position: relative;
      overflow: hidden;

      img {
        width: 228px;
        height: 228px;
        object-fit: cover;
        border-radius: 8px;
      }
    }

    .blog-content {
      flex: 1;
      padding: 1rem;
      display: flex;
      flex-direction: column;

      h3 {
        margin-bottom: 0.5rem;
        font-size: 1.25rem;
        font-weight: 600;
      }

      .description {
        color: #666;
        margin-bottom: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.5;
        max-height: 4.5em; // 3 lines * 1.5 line-height
        cursor: default;
        text-align: left;
      }

      p {
        font-size: 14px;
        line-height: 1.6;
        color: var(--Text-Body, #666d99);
        // margin-bottom: 16px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        margin: 0;
      }
       .read-more {
        color: #6566cd;
        text-decoration: none;
        font-size: 16px;
        font-weight: 500;
        margin-top: auto;
        margin-bottom: 16px;

        &:hover {
          text-decoration: underline;
        }
      }

      /* Updated class name here */
      .blog-read-more-link {
        align-self: stretch;
        font-family: Mulish;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 140%; /* 22.4px */
        letter-spacing: 0.08px;
        background: linear-gradient(90deg, var(--Pink-P-300, #F96CAB) -14.75%, var(--Blue-B-600, #4244C2) 34.27%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-decoration: none; // Remove default underline for anchor tag
        cursor: pointer; // Indicate it's clickable
        margin-top: auto; // Push to the bottom above the footer
        margin-bottom: 16px; // Add space before footer
        display: block; // Ensures it takes full width for align-self to work

        &:hover {
          text-decoration: underline; // Add underline on hover for better UX
        }
      }

      .blog-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: auto;
        padding-top: 16px;
        border-top: 1px solid var(--Border-Color, #e5e7eb);

        .author {
          display: flex;
          align-items: center;
          gap: 8px;

          .author-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
          }

          .author-name {
            font-size: 14px;
            color: var(--Text-Body, #666d99);
          }
        }

        .views {
          display: flex;
          align-items: center;
          gap: 6px;
          color: var(--Text-Body, #666d99);
          font-size: 14px;

          i {
            font-size: 16px;
          }
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .news-blogs {
    .container-fluid {
      padding: 0 5%;
    }

    .blog-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .blog-card {
      height: 220px;

      .blog-image {
        flex: 0 0 200px;
      }

      .blog-content {
        h3 {
          font-size: 20px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .news-blogs {
    padding: 40px 0;

    .container-fluid {
      padding: 0 20px;
    }

    .header h2 {
      font-size: 36px;

      .star {
        font-size: 20px;
      }
    }

    .blog-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .blog-card {
      height: auto;
      flex-direction: column;

      .blog-image {
        flex: none;
        height: 200px;
      }

      .blog-content {
        padding: 20px;

        h3 {
          font-size: 18px;
          margin-bottom: 8px;
        }

        p {
          margin-bottom: 12px;
          -webkit-line-clamp: 2;
        }
         .read-more {
          margin-bottom: 12px;
        }

        .blog-read-more-link { /* Updated class name here too */
          margin-bottom: 12px;
        }

        .blog-footer {
          padding-top: 12px;
        }
      }
    }
  }
}