import { ButtonVariant, LoadingType, ButtonAnimation } from '@awe/play-comp-library';

export type IconStatus = 'default' | 'active' | 'disable';

export interface FileAttachOption {
  name: string;
  icon: string;
  value: string;
}

export interface IconOption {
  name: string;
  icon: string;
  value: string;
  isLocalSvg?: boolean;
  disabled?: boolean;
}

export interface SelectedFile {
  id: string;
  name: string;
  url: string;
  type: string;
}

export interface Buttons {
  label: string;
  variant: ButtonVariant;
  loadingType: LoadingType;
  buttonAnimation: ButtonAnimation;
}
