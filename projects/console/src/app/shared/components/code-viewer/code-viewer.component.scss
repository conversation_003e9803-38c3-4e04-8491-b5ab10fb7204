.code-viewer-container {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
  
  &.dark {
    background-color: #1e1e1e;
    color: #e0e0e0;
  }
}

.file-explorer {
  width: 220px;
  height: 100%;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  
  .dark & {
    border-right-color: #333;
  }
}

.file-explorer-header {
  padding: 16px;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #e0e0e0;
  
  .dark & {
    border-bottom-color: #333;
  }
}

.file-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.file-item {
  padding: 8px 16px;
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-left: 3px solid transparent;
  
  &:hover {
    background-color: #f0f0f0;
    
    .dark & {
      background-color: #2a2a2a;
    }
  }
  
  &.selected {
    background-color: #e8f0fe;
    border-left-color: #6566cd;
    
    .dark & {
      background-color: #2d3748;
    }
  }
  
  .file-icon {
    margin-right: 8px;
  }
  
  .file-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.code-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.code-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 8px;
  height: 40px;
  overflow-x: auto;
  background-color: #fafafa;
  
  .dark & {
    border-bottom-color: #333;
    background-color: #252526;
  }
}

.code-tab {
  padding: 0 16px;
  height: 40px;
  display: flex;
  align-items: center;
  font-size: 13px;
  border-right: 1px solid #e0e0e0;
  cursor: pointer;
  white-space: nowrap;
  
  .dark & {
    border-right-color: #333;
  }
  
  &:hover {
    background-color: #f0f0f0;
    
    .dark & {
      background-color: #2a2a2a;
    }
  }
  
  &.active {
    background-color: #e8f0fe;
    border-bottom: 2px solid #6566cd;
    
    .dark & {
      background-color: #2d3748;
    }
  }
}

.code-display {
  flex: 1;
  overflow: auto;
  padding: 16px;
  background-color: #fafafa;
  
  .dark & {
    background-color: #1e1e1e;
  }
  
  pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    
    code {
      white-space: pre;
      
      &.language-html {
        color: #333;
      }
      
      &.language-css {
        color: #333;
      }
      
      &.language-javascript {
        color: #333;
      }
      
      .dark & {
        &.language-html {
          color: #e0e0e0;
        }
        
        &.language-css {
          color: #e0e0e0;
        }
        
        &.language-javascript {
          color: #e0e0e0;
        }
      }
    }
  }
}

.no-file-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-file-message {
  color: #666;
  font-style: italic;
  
  .dark & {
    color: #999;
  }
}