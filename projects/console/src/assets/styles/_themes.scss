@import './variables';

html {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  color: var(--text-color);
  background-color: transparent;
  font-family: var(--font-family);
  font-size: var(--font-size);
  line-height: var(--line-height);
  overflow: hidden;
  transition: color 0.2s ease;
  will-change: color;

  &.dark-theme {
    color: var(--text-color);
  }
}

// Themed background effect for the app container
.app-container {
  background-color: transparent;
  color: var(--text-color);
  transition: color 0.2s ease;
  position: relative;
  z-index: 1;
}

// Card component theming
.card-container {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  box-shadow: 0 4px 16px var(--card-shadow);
  transition: box-shadow 0.2s ease;
  
  &.clickable:hover:not(.no-hover) {
    box-shadow: 0 6px 20px var(--card-hover-shadow);
  }
}

// Form fields theming
.form-input, .form-textarea {
  background-color: var(--input-bg);
  color: var(--input-text);
  border: 1px solid var(--input-border);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  
  &:focus {
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 2px var(--input-focus-shadow);
  }
}

// Navigation theming
.nav-items {
  background-color: var(--nav-bg);
  
  .nav-item {
    color: var(--nav-text);
    transition: background-color 0.2s ease, color 0.2s ease;
    
    &:hover {
      background-color: var(--nav-hover);
    }
    
    &.active {
      background-color: var(--nav-active);
      color: var(--nav-active-text);
    }
  }
}

// Button theming
.button-primary {
  background: var(--button-primary-bg);
  color: var(--button-primary-text);
  transition: opacity 0.2s ease;
  
  &:hover {
    opacity: var(--button-hover-opacity);
  }
}

.button-secondary {
  background: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: 1px solid var(--button-secondary-border);
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: var(--nav-hover);
  }
}

// Global scrollbar styling
* {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb);
    border-radius: 4px;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: var(--scrollbar-thumb-hover);
    }
  }
}

.glass-card {
  background: var(--card-bg);
  border-radius: var(--card-border-radius);
  border: 1px solid var(--card-border-color);
  box-shadow: var(--card-box-shadow);
  overflow: hidden;
  transition: background 0.2s ease, border 0.2s ease, box-shadow 0.2s ease;
  
  .card-header {
    background: var(--card-header-bg);
    color: var(--card-header-color);
    border-bottom: 1px solid var(--card-border-color);
    transition: background 0.2s ease, color 0.2s ease, border 0.2s ease;
  }
  
  .card-body {
    background: var(--card-body-bg);
    color: var(--card-body-color);
    transition: background 0.2s ease, color 0.2s ease;
  }
  
  .card-footer {
    background: var(--card-footer-bg);
    color: var(--card-footer-color);
    border-top: 1px solid var(--card-border-color);
    transition: background 0.2s ease, color 0.2s ease, border 0.2s ease;
  }
}

// Form fields
.form-field {
  label {
    color: var(--form-label-color);
    transition: color 0.2s ease;
  }
  
  input, select, textarea {
    background: var(--form-input-bg);
    color: var(--form-input-color);
    border: 1px solid var(--form-input-border);
    transition: background 0.2s ease, color 0.2s ease, border 0.2s ease;
    
    &:focus {
      border-color: var(--form-input-focus-border);
      box-shadow: 0 0 0 2px var(--form-input-focus-shadow);
    }
    
    &::placeholder {
      color: var(--form-input-placeholder);
    }
  }
}

// Navigation items
.nav-item {
  color: var(--nav-item-color);
  transition: color 0.2s ease;
  
  &:hover, &.active {
    color: var(--nav-item-active-color);
    background: var(--nav-item-active-bg);
  }
}

// Buttons
.btn {
  background: var(--btn-bg);
  color: var(--btn-color);
  border: 1px solid var(--btn-border);
  transition: background 0.2s ease, color 0.2s ease, border 0.2s ease;
  
  &:hover {
    background: var(--btn-hover-bg);
    color: var(--btn-hover-color);
  }
  
  &.primary {
    background: var(--btn-primary-bg);
    color: var(--btn-primary-color);
    
    &:hover {
      background: var(--btn-primary-hover-bg);
    }
  }
} 