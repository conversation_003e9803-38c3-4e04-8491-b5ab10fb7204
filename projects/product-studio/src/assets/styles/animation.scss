@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 0.5;
  }
}

@keyframes breatheDark {
  0%,
  100% {
    box-shadow:
      0 0 10px rgba(246, 59, 143, 0.4),
      0 0 20px rgba(246, 59, 143, 0.4);
  }
  50% {
    box-shadow:
      0 0 25px rgba(246, 59, 143, 0.8),
      0 0 45px rgba(246, 59, 143, 0.8);
  }
}

@keyframes breatheLight {
  0%,
  100% {
    box-shadow:
      0 0 10px rgba(101, 102, 205, 0.4),
      0 0 20px rgba(101, 102, 205, 0.4);
  }
  50% {
    box-shadow:
      0 0 25px rgba(101, 102, 205, 0.8),
      0 0 45px rgba(101, 102, 205, 0.8);
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-20%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(20%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}
