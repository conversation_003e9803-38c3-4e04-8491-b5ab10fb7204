import { Buttons, FileAttachOption } from '../modals/promt-screen.modal';

export const promptContentConstants = {
  acceptedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
  animatedTexts: [
    '"Generate Design"',
    '"Code Generation"',
    '"Generate Application"',
    '"Review Accessibility"',
  ],
};
export const buttonLabels: Buttons[] = [
  {
    label: '✨Brainstorm a new idea',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Help me improve user engagement for my app.',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create user personas for Fly high airlines',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
];

export const fileOptions: FileAttachOption[] = [
  { name: 'Attach file from Computer', icon: 'awe_upload', value: 'computer' },
  { name: 'Attach file from Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
];
