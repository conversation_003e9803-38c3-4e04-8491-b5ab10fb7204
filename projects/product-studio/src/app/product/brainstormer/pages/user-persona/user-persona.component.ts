import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent, HeadingComponent, IconsComponent } from '@awe/play-comp-library';
import { UserPersona } from '../../models/user-persona.model';
import { BrainstormerStepperComponent } from '../../components/stepper/stepper.component';

@Component({
  selector: 'app-user-persona',
  standalone: true,
  imports: [
    CommonModule,
  ],
  templateUrl: './user-persona.component.html',
  styleUrls: ['./user-persona.component.scss']
})
export class UserPersonaComponent implements OnInit {
  personas: UserPersona[] = [
    {
      role: 'Sales Manager',
      age: 30,
      education: 'Masters in Business',
      status: 'Single',
      location: 'Sydney',
      techLiteracy: 'High',
      quote: 'I am used to with online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender', 'Tech-savy'],
      avatar: `assets/svgs/icons/awe_avatar.svg`
    },
    {
      role: 'UI Designer',
      age: 30,
      education: 'Masters in Business',
      status: 'Single',
      location: 'Sydney',
      techLiteracy: 'High',
      quote: 'I am used to with online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender', 'Tech-savy'],
      avatar: `assets/svgs/icons/awe_avatar.svg`
    },
    {
      role: 'School Teacher',
      age: 30,
      education: 'Masters in Business',
      status: 'Single',
      location: 'Sydney',
      techLiteracy: 'High',
      quote: 'I am used to with online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender', 'Tech-savy'],
      avatar: `assets/svgs/icons/awe_avatar.svg`
    },
    {
      role: 'Architect',
      age: 30,
      education: 'Masters in Business',
      status: 'Single',
      location: 'Sydney',
      techLiteracy: 'High',
      quote: 'I am used to with online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender', 'Tech-savy'],
      avatar: `assets/svgs/icons/awe_avatar.svg`
    }
  ];

  ngOnInit(): void {
    // Initialization logic if needed
  }

  removePersona(persona: UserPersona): void {
    const index = this.personas.indexOf(persona);
    if (index > -1) {
      this.personas.splice(index, 1);
    }
  }
}