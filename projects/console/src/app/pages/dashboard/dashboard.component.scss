/* Dashboard Component Styles
 * This file contains styles for the dashboard layout including grid system,
 * cards, tables, and responsive adjustments for various screen sizes.
 */

// CSS Variables for consistent values
:host {
  --dashboard-primary: var(--dashboard-primary);
  --dashboard-secondary: var(--dashboard-secondary);
  --dashboard-text-primary: var(--dashboard-text-primary);
  --dashboard-text-secondary: var(--dashboard-text-secondary);
  --dashboard-text-kpi-title : var(--dashboard-text-kpi-title);
  --dashboard-text-kpi-value : var(--dashboard-text-kpi-value);
  --dashboard-border-light: var(--dashboard-border-light);
  --dashboard-card-shadow: var(--dashboard-shadow-color);
  --dashboard-bg-light: var(--dashboard-bg-light);
  --dashboard-bg-lighter: var(--dashboard-bg-lighter);
  --dashboard-gradient: linear-gradient(90deg, var(--dashboard-primary) 0%, var(--dashboard-secondary) 100%);
  
  // Enhanced shadow variables for better depth
  --shadow-soft: 0 4px 12px var(--dashboard-shadow-color);
  --shadow-medium: 0 6px 16px var(--dashboard-shadow-medium);
  --shadow-strong: 0 8px 24px var(--dashboard-shadow-strong);
  --shadow-hover: 0 8px 20px var(--dashboard-shadow-hover);
  
  // Border variables
  --border-thin: 1px solid var(--dashboard-border-light);
  --border-medium: 2px solid var(--dashboard-border-light);
  --border-accent: 1px solid var(--dashboard-border-accent);
  --border-radius-standard: 12px;
  --border-radius-small: 8px;
  
  // Spacing
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-base: 16px;
  --spacing-lg: 20px;
  --spacing-xl: 24px;
  
  // Transition speeds
  --transition-speed: 0.2s;
  --transition-speed-medium: 0.3s;
  
  // ThinkPad specific variables - for referencing in media queries
  --thinkpad-spacing-compact: 10px;
  --thinkpad-font-size-reduced: 12px;
  --thinkpad-element-height-reduced: 90%;
}

// Add new mixin for expanded state
@mixin expanded-kpi-font {
  .kpi-card-content {
    .kpi-title {
      font-size: 16px;
    }
  }
} 

/* Main container */
.dashboard-container {
  width: 100%;
  height: auto;
  max-height: auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-xl);
  padding-bottom: 0px !important;
  gap: var(--spacing-xl);
  overflow: hidden !important;
  max-width: 1920px;
  margin: 0 auto;
  
  @media (max-width: 768px) {
    padding: var(--spacing-base);
    gap: var(--spacing-base);
  }
  
  @media (max-width: 576px) {
    padding: var(--spacing-md);
    gap: var(--spacing-md);
  }
}


.dashboard-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--spacing-base);
  margin-bottom: var(--spacing-lg);
}

.search-section {
  flex: 0 0 75%;
  min-width: 300px;
}

.action-buttons {
  flex: 1;
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

.action-button {
  padding: 10px 16px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--dashboard-bg-action-button);
  border: 1px solid var(--dashboard-action-button-border);
  font-family: 'Mulish', -apple-system, 'Roboto', 'Helvetica', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--dashboard-text-primary);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 55px minmax(0, 1fr) 400px;
  grid-template-rows: auto 1fr;
  grid-template-areas:
    "quick-actions kpi-cards approvals"
    "quick-actions data-tables approvals";
  gap: var(--spacing-xl);
  position: relative;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.quick-actions-expanded {
    grid-template-columns: 240px minmax(0, 1fr) 400px;
    @include expanded-kpi-font;
  }
  
  /* Responsive adjustments */
  @media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {
    &.quick-actions-expanded {
      grid-template-columns: 240px minmax(0, 1fr) 380px;
    }
  }
  
  /* ThinkPad 14" specific adjustments (1366x768) */
  @media (min-width: 1340px) and (max-width: 1380px) and (max-height: 780px) {
    grid-template-columns: 48px minmax(0, 1fr) 350px;
    gap: var(--spacing-lg);
    
    &.quick-actions-expanded {
      grid-template-columns: 200px minmax(0, 1fr) 320px;
    }
  }
  
  /* ThinkPad 15" HD specific adjustments (1920x1080 at 100% scaling) */
  @media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) and (min-height: 1000px) {
    grid-template-columns: 55px minmax(0, 1fr) 420px;
    
    &.quick-actions-expanded {
      grid-template-columns: 240px minmax(0, 1fr) 400px;
    }
  }
  
  @media (min-width: 992px) and (max-width: 1200px) {
    grid-template-columns: 55px minmax(0, 1fr) 320px;
    
    &.quick-actions-expanded {
      grid-template-columns: 240px minmax(0, 1fr) 320px;
    }
  }
  
  @media (max-width: 992px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto;
    grid-template-areas:
      "quick-actions"
      "kpi-cards"
      "data-tables"
      "approvals";
    gap: var(--spacing-lg); /* Increased gap for better separation on mobile */
  }
  
  /* Fixed heights for different screen sizes */
  height: calc(100vh - 30%) !important; /* Adjusted from 35% to provide more vertical space */
  max-height: calc(100vh - 30%) !important;
  /* Add transitions for smooth expansion/collapse */
  transition: grid-template-columns var(--transition-speed-medium) ease;
  
  /* ThinkPad specific height adjustments */
  @media (min-width: 1340px) and (max-width: 1380px) and (max-height: 780px) {
    height: 600px; /* Fixed height for 14" ThinkPad */
    max-height: 600px;
  }
  
  @media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {
    height: 720px; /* Optimal fixed height for 15" ThinkPad */
    max-height: 720px;
  }
  
  @media (min-height: 900px) {
    height: 750px; /* Increased from 700px for more space */
  }
  
  @media (max-height: 800px) {
    height: 650px; /* Increased from 600px for more space */
  }
  
  @media (max-height: 700px) {
    height: 550px; /* Increased from 500px for more space */
  }
  
  /* Add specific styling for medium-width screens (13" laptops, etc.) */
  @media (min-width: 1200px) and (max-width: 1400px) {
    grid-template-columns: 48px minmax(0, 0.7fr) 320px; /* Reduce approvals panel width by ~20% */
    gap: var(--spacing-lg); /* Increased from spacing-base for better separation */
    
    &.quick-actions-expanded {
      grid-template-columns: 200px minmax(0, 1fr) 320px; /* Slightly smaller quick actions panel */
    }
  }
  
  @media (max-width: 1200px) {
    grid-template-columns: 48px 1fr;
    grid-template-rows: auto auto 1fr;
    grid-template-areas:
      "quick-actions kpi-cards"
      "quick-actions data-tables"
      "quick-actions approvals";
    height: auto; /* Let it flow naturally on smaller widths */
    min-height: 750px; /* Increased from 680px for more space */
    
    &.quick-actions-expanded {
      grid-template-columns: 192px 1fr;
    }
  }
  
  @media (max-width: 576px) {
    gap: var(--spacing-base);
  }
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .search-section {
    flex: 0 0 70%;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-section {
    flex: 0 0 100%;
  }
  
  .action-buttons {
    flex: 0 0 100%;
    justify-content: flex-start;
  }
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  margin-bottom: var(--spacing-xl);
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  @media (max-width: 576px) {
    margin-bottom: var(--spacing-base);
  }
}

.user-greeting {
  display: flex;
  flex-direction: column;
  
  .greeting-text {
    font-size: 24px;
    font-weight: 200;
    margin: 0;
    color: var(--dashboard-text-primary);
  }
  
  .welcome-text {
    display: inline-flex;
    align-items: baseline;
    font-size: 24px;
    font-weight: 400;
    margin-top: var(--spacing-xs);
    color: var(--dashboard-text-welcome);
    
    .highlight {
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      text-fill-color: transparent;
      margin-left: 4px;
      display: inline-block; /* Required for the gradient to work properly */
      vertical-align: baseline;
      position: relative;
    }
    
    .console-icon {
      margin-left: var(--spacing-sm);
    }
  }
}

.last-update {
  font-size: 14px;
  color: var(--dashboard-text-secondary);
}

/* Quick Actions Panel */
.quick-actions-wrapper {
  grid-area: quick-actions;
  background-color: var(--dashboard-card-bg);
  border-radius: var(--border-radius-standard);
  display: flex;
  flex-direction: column;
  width: 55px;
  height: 575px;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  border: var(--border-thin);
  position: relative;
  
  &:hover {
    box-shadow: var(--shadow-hover);
  }
  
  /* Expanded state - width is controlled by parent grid */
  &.expanded {
    width: 100%; /* Use 100% width to fit the grid cell */
  }

  @media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {
    height: 595px;
  }
  
  /* Responsive adjustments */
  @media (min-width: 1200px) and (max-width: 1400px) {
    height: 580px !important;
    max-height: 580px !important;
  }
  
  @media (max-width: 1200px) {
    height: 320px;
  }
  
  @media (max-width: 992px) {
    flex-direction: row;
    width: 100%;
    height: 48px;
    
    &.expanded {
      height: auto;
      max-height: 320px;
      width: 100%;
    }
  }
  
  @media (max-width: 576px) {
    height: 280px;
  }
  
  /* Special case for 13" laptops */
  @media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {
    height: 100%;
    
    ::ng-deep .card-container {
      height: 100%;
    }
  }
  
  /* Card styling */
  ::ng-deep .quick-actions-card {
    height: 100% !important;
    width: 100% !important;
    
    .card-container {
      height: 100% !important;
      width: 100% !important;
      padding: 0 !important;
      overflow: hidden !important;
      display: flex !important;
      flex-direction: column !important;
      
      .card-body {
        padding: 0 !important;
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
      }
    }
    
    .card-content {
      height: 100% !important;
      display: flex !important;
      flex-direction: column !important;
      padding: 0 !important;
      
      @media (max-width: 992px) {
        flex-direction: row !important;
      }
    }
    
    /* Responsive card adjustments */
    @media (max-width: 992px) {
      .card-container {
        height: 48px !important;
        width: 100% !important;
        flex-direction: row !important;
        
        &.expanded {
          height: auto !important;
        }
      }
    }
    
    /* Special case for 13" laptops */
    @media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {
      .card-container {
        width: 100% !important;
      }
    }
  }
}

/* Toggle button in quick actions panel */
.quick-actions-toggle {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 16px;
  padding-bottom: 0px;
  cursor: pointer;
  transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  
  /* Adjust the toggle for the collapsed state to center the button */
  .quick-actions-wrapper:not(.expanded) & {
    padding: 20px 0;
    justify-content: center;
  }
  
  .toggle-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background-color: transparent;
    position: relative;
    
    /* Add gradient border for expanded state */
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 8px;
      padding: 1px;
      background: var(--dashboard-gradient);
      -webkit-mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    svg {
      transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      width: 16px;
      height: 16px;
      stroke: var(--dashboard-toggle-stroke);
      z-index: 1;
      
      &.rotate {
        transform: rotate(180deg);
      }
    }
  }
  
  /* Special styling for collapsed state to match other buttons */
  .quick-actions-wrapper:not(.expanded) .toggle-button {
    background: var(--dashboard-gradient);
    transition: background 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    
    &::before {
      display: none;
    }
    
    svg {
      stroke: var(--dashboard-toggle-stroke-collapsed);
      transition: stroke 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
  
  span {
    font-weight: 580;
    font-size: 16px;
    color: var(--dashboard-text-primary);
    opacity: 1;
    transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Content area for quick actions panel */
.quick-actions-content {
  padding: 20px 16px;
  overflow-y: auto;
  flex-grow: 1;
  
  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 16px; /* Increased gap for better spacing */
    
    .action-button {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 16px; /* Wider gap for better spacing */
      padding: 16px 20px; /* More padding for better touch area */
      border-radius: 12px; /* Rounded corners */
      border: none;
      background: var(--dashboard-quick-action-button-bg); /* Use the new variable */
      cursor: pointer;
      transition: all var(--transition-speed) ease;
      width: 100%;
      text-align: left;
      
      &:hover {
        opacity: 0.9;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--dashboard-shadow-hover);
      }
      
      .action-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        
        img {
          width: 20px;
          height: 20px;
          filter: brightness(0) invert(1); /* Make SVG white */
        }
      }
      
      .action-label {
        font-size: 12px;
        font-weight: 300;
        color: var(--dashboard-quick-action-text); /* Use the new variable */
      }
    }
  }
}

/* Icons for collapsed quick actions panel */
.quick-actions-icons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px; /* Increased gap between items */
  padding: 20px 0; /* More vertical padding */
  
  @media (max-width: 992px) {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    padding: 8px;
  }
  
  .icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px; /* Larger button size */
    height: 36px; /* Larger button size */
    border-radius: 8px; /* Slightly rounded corners */
    border: none;
    background: var(--dashboard-quick-action-button-bg); /* Use the new variable */
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    
    &:hover {
      opacity: 0.9;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px var(--dashboard-shadow-hover);
    }
    
    .icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      
      img {
        width: 20px;
        height: 20px;
        filter: brightness(0) invert(1); /* Make SVG white */
      }
    }
  }
}

/* KPI Cards Section */
.kpi-cards {
  grid-area: kpi-cards;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-base);
  height: 160px; /* Increased from 140px to account for larger font sizes */
  min-height: 160px;
  max-height: 160px !important; /* Add !important to ensure it's never exceeded */
  margin-bottom: 20px; /* Add explicit margin to create more separation */
  box-sizing: border-box; /* Ensure padding is included in height calculation */
  overflow: visible; /* Allow overflow to be visible but not affect layout */
  
  /* ThinkPad specific adjustments */
  @media (min-width: 1340px) and (max-width: 1380px) and (max-height: 780px) {
    height: 140px;
    min-height: 140px;
    max-height: 140px !important;
    gap: var(--spacing-sm);
    margin-bottom: 16px;
  }
  
  @media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {
    height: 170px;
    min-height: 170px;
    max-height: 170px !important;
  }
  
  /* Responsive height adjustments */
  @media (min-height: 900px) {
    height: 180px; /* Increased from 160px */
    min-height: 180px;
    max-height: 180px !important;
  }
  
  /* Medium screens */
  @media (min-width: 1200px) and (max-width: 1400px) {
    gap: var(--spacing-md);
    height: 150px; /* Increased from 130px */
    min-height: 150px;
    max-height: 150px !important;
  }
  
  /* Responsive column adjustments */
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    height: auto;
    min-height: unset;
    max-height: unset !important;
  }
  
  @media (max-width: 576px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .kpi-card {
    border-radius: var(--border-radius-standard);
    background-color: var(--dashboard-card-bg);
    box-shadow: var(--shadow-soft);
    border: var(--border-thin);
    height: 100%;
    max-height: 100%;
    overflow: hidden;
    transition: all var(--transition-speed) ease;
    position: relative;
    box-sizing: border-box;
    
    /* Add subtle accent border on hover */
    &:hover {
      box-shadow: var(--shadow-hover);
      border: var(--border-accent);
      transform: translateY(-2px);
    }
    
    /* Add subtle top border accent */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: var(--dashboard-gradient);
      opacity: 0.7;
    }
  }
}

/* KPI Card styles - using ::ng-deep for .card-container integration */
::ng-deep .kpi-card {
  .card-container {
    height: 100% !important;
    max-height: 100% !important;
    padding: var(--spacing-base) !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }
}

.kpi-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  
  .kpi-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    
    .kpi-title {
      font-size: 24px;
      font-weight: 580;
      margin: 0;
      color: var(--dashboard-text-kpi-title);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: calc(100% - 48px); /* Reserve space for the icon */
      padding-right: 8px; /* Add some padding for visual comfort */
      transition: font-size 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .kpi-icon {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--dashboard-bg-icon-button);
      border-radius: 8px;
      flex-shrink: 0; /* Prevent the icon from shrinking */
      
      img {
        width: 20px;
        height: 20px;
        object-fit: contain; /* Ensure the image fits properly */
      }
      
      /* Style for SVGs loaded directly */
      svg {
        width: 20px;
        height: 20px;
        fill: var(--dashboard-text-kpi-value);
      }
    }
  }
  
  .kpi-value {
    font-size: 46px;
    font-weight: 500;
    color: var(--dashboard-text-kpi-value);
    margin: 2px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: font-size 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    
    @media (min-width: 1200px) and (max-width: 1400px) {
      font-size: 24px;
    }
  }
  
  .kpi-subtitle {
    font-size: 12px;
    color: var(--dashboard-text-secondary);
    margin-top: auto;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: font-size 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    
    @media (min-width: 1200px) and (max-width: 1400px) {
      font-size: 11px;
    }
  }
}

/* Data Tables Section */
.data-tables-row {
  grid-area: data-tables;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-base);
  height: calc(100% - 200px); /* Significantly increased from 184px to account for larger font sizes */
  
  /* ThinkPad specific height adjustments */
  @media (min-width: 1340px) and (max-width: 1380px) and (max-height: 780px) {
    height: calc(600px - 180px);
    gap: var(--spacing-sm);
  }
  
  @media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {
    height: calc(720px - 210px);
    gap: var(--spacing-xl);
  }
  
  /* Height adjustments for different screen sizes */
  @media (min-height: 900px) {
    height: calc(700px - 220px); /* Significantly increased from 194px */
  }
  
  @media (max-height: 700px) {
    height: calc(500px - 180px); /* Significantly increased from 154px */
  }
  
  @media (min-width: 1200px) and (max-width: 1400px) {
    gap: var(--spacing-md);
    height: calc(100% - 190px); /* Significantly increased from 156px */
  }
  
  @media (max-width: 1200px) {
    height: auto;
    min-height: 300px;
  }
  
  @media (max-width: 992px) {
    grid-template-columns: 1fr;
    height: auto;
    min-height: 340px;
  }
  
  @media (max-width: 576px) {
    gap: var(--spacing-md);
  }
  
  /* Ensure equal dimensions for children */
  & > * {
    width: 100%;
    height: 100%;
  }
}

/* Create a reusable mixin for the fading border effect */
@mixin fading-border-bottom {
  border-bottom: none;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      to right,
      rgba(240, 240, 245, 0), /* Start transparent */
      rgba(240, 240, 245, 1) 25%, /* Fade in more gradually */
      rgba(240, 240, 245, 1) 75%, /* More centered solid section */
      rgba(240, 240, 245, 0) /* Fade out */
    );
  }
}

/* Data Card styles - using ::ng-deep for .card-container integration */
::ng-deep .data-card {
  .card-container {
    padding: 0 !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
    min-height: 0 !important;
    border-radius: var(--border-radius-standard) !important;
    box-shadow: var(--shadow-medium) !important;
    border: var(--border-thin) !important;
    transition: all var(--transition-speed) ease !important;
    position: relative !important;
    
    /* Add subtle top border accent */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: var(--dashboard-gradient);
      opacity: 0.7;
      z-index: 1;
    }
    
    &:hover {
      box-shadow: var(--shadow-hover) !important;
    }
  }
}

.data-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .data-card-title {
    font-size: 18px;
    font-weight: 580;
    color: var(--dashboard-text-primary);
    margin: 0;
    padding: var(--spacing-base) 20px;
    @include fading-border-bottom; /* Apply the fading border mixin */
    flex-shrink: 0;
    height: 56px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    
    @media (min-width: 1200px) and (max-width: 1400px) {
      font-size: 15px;
      padding: 10px 14px;
      height: 38px;
    }
  }
}

/* Table container for scrollable content */
.table-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100% - 112px); /* Subtract space for header (56px) and footer (56px) */
  
  &:has(.user-logs-table), &:has(.models-table) {
    display: flex;
    flex-direction: column;
  }
  
  /* ThinkPad specific adjustments */
  @media (min-width: 1340px) and (max-width: 1380px) and (max-height: 780px) {
    max-height: calc(100% - 96px);
  }
  
  @media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {
    max-height: calc(100% - 124px);
  }
  
  @media (min-width: 1200px) and (max-width: 1400px) {
    max-height: calc(100% - 104px);
  }
  
  /* Enhanced scrollbar styles */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f6f6f6;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(101, 102, 205, 0.15);
    border-radius: 4px;
    border: 1px solid rgba(101, 102, 205, 0.05);
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(101, 102, 205, 0.3);
  }
}

/* Data table styling */
.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed;
  
  th {
    position: sticky;
    top: 0;
    background-color: transparent;
    z-index: 1;
    text-align: center;
    padding: 12px var(--spacing-base);
    font-weight: 580;
    font-size: 14px;
    color: var(--dashboard-text-secondary);
    // border-bottom: 1px solid var(--dashboard-border-light);
    height: 46px;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    
    @media (min-width: 1200px) and (max-width: 1400px) {
      padding: 10px 12px;
      font-size: 13px;
    }
  }
  
  td {
    padding: 10px var(--spacing-base);
    font-size: 14px;
    // border-bottom: 1px solid var(--dashboard-border-light);
    text-align: center;
    vertical-align: middle;
    height: 44px;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    
    @media (min-width: 1200px) and (max-width: 1400px) {
      padding: 8px 12px;
      font-size: 13px;
    }
  }
  
  tr:last-child td {
    border-bottom: none;
  }
  
  /* Specific table styling for user logs and models tables */
  &.user-logs-table {
    th:first-child, td:first-child {
      width: 40%;
      text-align: left; /* Changed to left alignment for better readability */
      padding-left: var(--spacing-base);
    }
    
    th:nth-child(2), td:nth-child(2) {
      width: 40%;
      text-align: left; /* Changed to left alignment for better readability */
    }
    
    th:last-child, td:last-child {
      width: 20%;
      text-align: center;
      padding-right: var(--spacing-base);
      white-space: normal; /* Allow text to wrap instead of ellipses */
      overflow: visible; /* Allow overflow to be visible */
      text-overflow: clip; /* Remove ellipses */
    }
    
    /* Improve text truncation in cells except for last column */
    td:not(:last-child) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 0; /* This forces the text-overflow to work properly */
    }
    
    /* Add spacing between rows for better readability */
    tr {
      border-bottom: 1px solid var(--dashboard-table-row-border);
      
      &:last-child {
        border-bottom: none;
      }
    }
  }
}

/* Table cell components */
.user-cell {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* Changed from center to flex-start for left alignment */
  height: 24px;
  overflow: hidden;
  max-width: 100%;
  
  .username {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 10px); /* Reserve some space for potential icons */
  }
}

.publisher-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  gap: 8px;
  
  .publisher-logo {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  span {
    font-weight: 500;
  }
}

/* Status badge styles */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  height: 24px;
  white-space: nowrap; /* Prevent wrapping within the badge */
  overflow: visible; /* Allow full visibility */
  
  &.active {
    background-color: transparent;
    color: var(--status-active-color);
    border: 1px solid var(--status-active-border);
  }
  
  &.inactive {
    background-color: var(--status-inactive-bg);
    color: var(--status-inactive-color);
  }
  
  &.pending {
    background-color: var(--status-pending-bg);
    color: var(--status-pending-color);
  }
}

/* View more link at bottom of tables */
.view-more {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 20px;
  border-top: none;
  flex-shrink: 0;
  height: 40px;
  box-sizing: border-box;
  position: relative;
  margin-top: 8px;
  
  /* Create the line that spans most of the width */
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 100px; /* Increased space for the "View More" text and gap */
    height: 1px;
    background-color: var(--dashboard-border-light);
    transform: translateY(-50%);
  }
  
  @media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {
    height: 32px;
    padding: 6px 16px;
    
    &::before {
      right: 90px; /* Adjust space for smaller screens */
    }
  }
  
  a {
    color: var(--dashboard-text-secondary);
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    opacity: 0.7;
    transition: all var(--transition-speed) ease;
    background-color: var(--dashboard-card-bg); /* Add background to overlay the line */
    position: relative; /* Ensure it's above the line */
    z-index: 1; /* Make sure text appears above the line */
    padding: 0 5px; /* Add padding around text */
    margin-left: 12px; /* Add margin to create gap from the line */
    
    &:hover {
      color: var(--dashboard-primary);
      opacity: 1;
    }
  }
}

/* Adding specific styling for the models table to match screenshot */
.data-table {
  &.models-table {
    th, td {
      &:first-child {
        text-align: left; /* Left align model name column */
        padding-left: var(--spacing-xl);
      }
      
      &:last-child {
        text-align: right; /* Right align agents count column */
        padding-right: var(--spacing-xl);
      }
    }
    
    /* Add spacing between rows for better readability */
    tr {
      border-bottom: 1px solid var(--dashboard-table-row-border);
      
      &:last-child {
        border-bottom: none;
      }
    }
  }
}

/* Approvals Panel */
.approvals-panel {
  grid-area: approvals;
  border-radius: var(--border-radius-standard);
  height: 575px;
  overflow: hidden; /* Changed from auto to hidden to contain children */
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-medium);
  border: var(--border-thin);
  position: relative;
  background-color: var(--dashboard-card-bg);
  
  ::ng-deep .card-container{
    height: 580px !important;
  }
  
  &:hover {
    box-shadow: var(--shadow-hover);
  }
  
  /* ThinkPad specific adjustments */
  @media (min-width: 1340px) and (max-width: 1380px) and (max-height: 780px) {
    height: 595px;
    min-height: 595px;
    
    ::ng-deep .card-container {
      height: 595px !important;
      max-height: 595px !important;
    }
  }
  
  @media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {
    height: 595px;
    
    ::ng-deep .card-container {
      height: 595px !important;
    }
  }
  
  /* Responsive sizing for different screens */
  @media (min-width: 1200px) and (max-width: 1400px) {
    min-width: 320px;
    height: 580px;
    
    ::ng-deep .card-container {
      height: 580px;
      max-height: 580px;
    }
  }
  
  @media (max-width: 1200px) {
    height: 320px;
    min-height: 320px;
    
    ::ng-deep .card-container {
      height: 320px;
      max-height: 320px;
    }
  }
  
  @media (max-width: 576px) {
    height: 280px;
    min-height: 280px;
    
    ::ng-deep .card-container {
      height: 280px;
      max-height: 280px;
    }
  }
  
  /* Special case for 13" laptops */
  @media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {
    height: 100%;
    
    ::ng-deep .card-container {
      height: 100%;
    }
  }
}

/* Approvals content layout */
.approvals-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .approvals-title {
    font-size: 16px;
    font-weight: 580;
    color: var(--dashboard-text-primary);
    margin: 0;
    padding: var(--spacing-md) var(--spacing-base);
    @include fading-border-bottom; /* Apply the fading border mixin */
    flex-shrink: 0;
    height: 44px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }
}

/* Approvals list with scrollable content */
.approvals-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px 14px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-height: 0;
  max-height: calc(100% - 88px);
  
  /* ThinkPad specific adjustments */
  @media (min-width: 1340px) and (max-width: 1380px) and (max-height: 780px) {
    padding: 8px 10px;
    gap: 4px;
    max-height: calc(100% - 78px);
    
    .approval-item {
      padding: 6px 8px;
      
      .approval-info {
        .approval-name {
          font-size: 12px;
          max-width: 160px;
        }
        
        .approval-date {
          font-size: 10px;
        }
      }
      
      .approve-button {
        padding: 4px 8px;
        min-width: 48px;
        font-size: 10px;
      }
    }
  }
  
  @media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {
    max-height: calc(100% - 96px);
    padding: 12px 16px;
    gap: 8px;
    
    .approval-item {
      padding: 10px 12px;
      
      .approval-info {
        .approval-name {
          max-width: 240px;
          font-size: 14px;
        }
      }
    }
  }
  
  /* Enhanced scrollbar styles */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--dashboard-scrollbar-track);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: var(--dashboard-scrollbar-thumb);
    border-radius: 4px;
    border: 1px solid var(--dashboard-scrollbar-thumb-border);
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background-color: var(--dashboard-scrollbar-thumb-hover);
  }
  
  /* Medium screen adjustments */
  @media (min-width: 1200px) and (max-width: 1400px) {
    padding: 8px 10px;
    gap: 5px;
  }
  
  /* Special case for 13" laptops */
  @media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {
    max-height: calc(100% - 76px);
  }
  
  /* Individual approval item */
  .approval-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 10px;
    // border-bottom: 1px solid var(--dashboard-border-light);
    
    &:last-child {
      border-bottom: none;
    }
    
    /* Medium screen size adjustments */
    @media (min-width: 1200px) and (max-width: 1400px) {
      padding: 6px 8px;
    }
    
    .approval-info {
      flex: 1;
      min-width: 0;
      overflow: hidden;
      
      .approval-name {
        font-size: 13px;
        font-weight: 500;
        color: var(--dashboard-text-primary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 210px;
        
        @media (min-width: 1200px) and (max-width: 1400px) {
          max-width: 180px;
          font-size: 12px;
        }
      }

      .approval-date {
        font-size: 11px;
        color: var(--dashboard-text-secondary);
        margin-top: 3px;
        display: block;
        
        @media (min-width: 1200px) and (max-width: 1400px) {
          font-size: 10px;
          margin-top: 2px;
        }
      }
    }
    
    .approve-button {
      padding: 5px 10px;
      background: var(--dashboard-approve-button-bg);
      color: var(--dashboard-approve-button-color);
      border: 0.5px solid var(--dashboard-approve-button-border);
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-speed) ease;
      flex-shrink: 0;
      margin-left: 10px;
      min-width: 56px;
      text-align: center;
      
      @media (min-width: 1200px) and (max-width: 1400px) {
        padding: 4px 8px;
        min-width: 52px;
        font-size: 10px;
        margin-left: 6px;
      }
      
      &:hover {
        opacity: 0.9;
        box-shadow: 0 2px 4px var(--dashboard-approve-button-shadow);
      }
    }
  }
}

/* Footer styles */
.dashboard-footer {
  padding: var(--spacing-base) 0;
  padding-bottom: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  
  .copyright-text {
    font-size: 14px;
    color: var(--dashboard-copyright);
  }
  
  @media (max-width: 576px) {
    padding: var(--spacing-md) 0;
    
    .copyright-text {
      font-size: 12px;
    }
  }
}

/* Approvals card styling */
:host ::ng-deep .approvals-card {
  .card-container {
    padding: 0 !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
    border-radius: var(--border-radius-standard) !important;
    box-shadow: var(--shadow-strong) !important;
    border: var(--border-thin) !important;
    transition: all var(--transition-speed) ease !important;
    position: relative !important;
    
    /* Add subtle top border accent */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: var(--dashboard-gradient);
      opacity: 0.8;
      z-index: 1;
    }
    
    &:hover {
      box-shadow: var(--shadow-hover) !important;
    }
    
    /* ThinkPad specific adjustments */
    @media (min-width: 1340px) and (max-width: 1380px) and (max-height: 780px) {
      height: 100% !important;
      
      .approvals-title {
        font-size: 14px;
        padding: 8px 12px;
        height: 36px;
      }
      
      .approvals-list {
        padding: 6px 8px;
        max-height: calc(100% - 72px);
      }
      
      .view-more {
        height: 36px;
        padding: 4px;
      }
    }
    
    @media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {
      .approvals-title {
        font-size: 18px;
        padding: 14px 18px;
        height: 48px;
      }
    }
    
    /* Responsive sizing */
    @media (min-width: 1200px) and (max-width: 1400px) {
      height: 100% !important;
      max-height: 550px !important;
    }
    
    @media (max-width: 1200px) {
      height: 320px !important;
      max-height: 320px !important;
    }
    
    @media (max-width: 576px) {
      height: 280px !important;
      max-height: 280px !important;
    }
  }
  
  /* Card content area */
  .card-content {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    
    @media (min-width: 1200px) and (max-width: 1400px), (max-width: 992px) {
      padding: 0 !important;
    }
  }
}

/* Medium screen optimization */
@media (min-width: 1200px) and (max-width: 1400px) {
  .kpi-card-content .kpi-value {
    font-size: 24px;
  }
  
  .kpi-card-content .kpi-subtitle {
    font-size: 13px;
  }
}

/* ThinkPad screen optimizations */
@media (min-width: 1340px) and (max-width: 1380px) and (max-height: 780px) {
  .kpi-card-content {
    .kpi-header {
      margin-bottom: 4px;
      
      .kpi-title {
        font-size: 13px;
        max-width: calc(100% - 36px);
      }
      
      .kpi-icon {
        width: 30px;
        height: 30px;
      }
    }
    
    .kpi-value {
      font-size: 20px;
      margin: 0;
    }
    
    .kpi-subtitle {
      font-size: 10px;
    }
  }
  
  .data-card-content {
    .data-card-title {
      font-size: 14px;
      padding: 8px 12px;
      height: 36px;
    }
  }
  
  .data-table {
    th {
      padding: 8px 10px;
      font-size: 12px;
      height: 36px;
    }
    
    td {
      padding: 6px 10px;
      font-size: 12px;
      height: 34px;
    }
  }
  
  .view-more {
    height: 30px;
    padding: 4px;
    
    a {
      font-size: 12px;
    }
  }
}

/* Activity description truncation */
.activity-description {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: block; /* Ensure it takes full width of its container */
  text-align: left;
}

.emoji {
  font-size: 1em;
  margin-left: 6px;
}

