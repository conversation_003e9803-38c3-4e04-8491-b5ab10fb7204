import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MarkdownModule } from 'ngx-markdown';

import { CardComponent } from './card/card.component';
import { FormFieldComponent } from './form-field/form-field.component';
import { SelectDropdownComponent } from './select-dropdown/select-dropdown.component';
import { SuggestedPromptComponent } from './suggested-prompt/suggested-prompt.component';
import { ChatWindowComponent } from './chat-window/chat-window.component';
import { CodeWindowComponent } from './code-window/code-window.component';
import { CodeViewerComponent } from './code-viewer/code-viewer.component';
import { ChatInterfaceComponent } from './chat-interface/chat-interface.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MarkdownModule.forChild(),
    CardComponent,
    FormFieldComponent,
    SelectDropdownComponent,
    SuggestedPromptComponent,
    ChatWindowComponent,
    CodeWindowComponent,
    CodeViewerComponent,
    ChatInterfaceComponent
  ],
  exports: [
    CardComponent,
    FormFieldComponent,
    SelectDropdownComponent,
    SuggestedPromptComponent,
    ChatWindowComponent,
    CodeWindowComponent,
    CodeViewerComponent,
    ChatInterfaceComponent
  ]
})
export class SharedComponentsModule { }