/**
 * Logger utility for the Experience Studio application
 * Provides a consistent logging interface that can be disabled in production
 */
import { Injectable, InjectionToken } from '@angular/core';
import { environment } from '../../../environments/environment'; 

/**
 * Logger configuration interface
 */
export interface LoggerConfig {
  enableLogging: boolean;
  logLevel: string;
  enableConsoleColors: boolean;
}

/**
 * Default logger configuration
 */
export const DEFAULT_LOGGER_CONFIG: LoggerConfig = {
  enableLogging: !environment.production, // Disable logging in production
  logLevel: 'info', // 'debug', 'info', 'warn', 'error'
  enableConsoleColors: true, // Enable colored console output
};

/**
 * Injection token for logger configuration
 */
export const LOGGER_CONFIG = new InjectionToken<LoggerConfig>(
  'Logger Configuration',
  {
    providedIn: 'root',
    factory: () => DEFAULT_LOGGER_CONFIG
  }
);

// Log levels with numeric values for comparison
enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4,
}

// Map string log levels to enum values
const LOG_LEVEL_MAP: Record<string, LogLevel> = {
  debug: LogLevel.DEBUG,
  info: LogLevel.INFO,
  warn: LogLevel.WARN,
  error: LogLevel.ERROR,
  none: LogLevel.NONE,
};

// Console colors for different log levels
const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  // Foreground colors
  black: '\x1b[30m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  // Background colors
  bgBlack: '\x1b[40m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgBlue: '\x1b[44m',
  bgMagenta: '\x1b[45m',
  bgCyan: '\x1b[46m',
  bgWhite: '\x1b[47m',
};

/**
 * Logger class that provides methods for logging at different levels
 */
@Injectable({ providedIn: 'root' })
export class LoggerService {
  private readonly config: LoggerConfig;

  constructor() {
    // Use the default configuration
    this.config = DEFAULT_LOGGER_CONFIG;
  }

  /**
   * Create a new logger instance with the given name
   * @param name The name of the logger (usually the component or service name)
   * @returns A new Logger instance
   */
  createLogger(name: string): Logger {
    return new Logger(name, this.config);
  }
}

/**
 * Logger class that provides methods for logging at different levels
 */
export class Logger {
  private readonly name: string;
  private readonly minLevel: LogLevel;
  private readonly config: LoggerConfig;

  constructor(name: string, config: LoggerConfig) {
    this.name = name;
    this.config = config;
    this.minLevel = LOG_LEVEL_MAP[config.logLevel] || LogLevel.INFO;
  }

  /**
   * Format a log message with timestamp, level, and context
   */
  private formatMessage(level: string, message: string): string {
    const timestamp = new Date().toISOString();
    return `[${timestamp}] [${level.toUpperCase()}] [${this.name}] ${message}`;
  }

  /**
   * Log a debug message
   */
  debug(message: string, ...args: any[]): void {
    if (!this.config.enableLogging || this.minLevel > LogLevel.DEBUG) return;

    const formattedMessage = this.formatMessage('debug', message);
    if (this.config.enableConsoleColors) {
      console.debug(COLORS.cyan + formattedMessage + COLORS.reset, ...args);
    } else {
      console.debug(formattedMessage, ...args);
    }
  }

  /**
   * Log an info message
   */
  info(message: string, ...args: any[]): void {
    if (!this.config.enableLogging || this.minLevel > LogLevel.INFO) return;

    const formattedMessage = this.formatMessage('info', message);
    if (this.config.enableConsoleColors) {
      console.info(COLORS.green + formattedMessage + COLORS.reset, ...args);
    } else {
      console.info(formattedMessage, ...args);
    }
  }

  /**
   * Log a warning message
   */
  warn(message: string, ...args: any[]): void {
    if (!this.config.enableLogging || this.minLevel > LogLevel.WARN) return;

    const formattedMessage = this.formatMessage('warn', message);
    if (this.config.enableConsoleColors) {
      console.warn(COLORS.yellow + formattedMessage + COLORS.reset, ...args);
    } else {
      console.warn(formattedMessage, ...args);
    }
  }

  /**
   * Log an error message
   */
  error(message: string, ...args: any[]): void {
    if (!this.config.enableLogging || this.minLevel > LogLevel.ERROR) return;

    const formattedMessage = this.formatMessage('error', message);
    if (this.config.enableConsoleColors) {
      console.error(COLORS.red + formattedMessage + COLORS.reset, ...args);
    } else {
      console.error(formattedMessage, ...args);
    }
  }
}

/**
 * Create a new logger instance with the given name
 * This is a convenience function for backward compatibility
 * @param name The name of the logger (usually the component or service name)
 * @returns A new Logger instance
 */
export function createLogger(name: string): Logger {
  return new Logger(name, DEFAULT_LOGGER_CONFIG);
}

/**
 * Global logger instance for use in non-class contexts
 */
export const logger = new Logger('Global', DEFAULT_LOGGER_CONFIG);
