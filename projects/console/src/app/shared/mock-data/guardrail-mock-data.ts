import { CardData } from '../models/card.model';

export const MOCK_GUARDRAILS: CardData[] = [
  {
    id: '1',
    title: 'Content Moderation Guardrail',
    tags: [
      { label: 'Safety' },
      { label: 'Basic' },
      { label: 'Content' },
      { label: 'AI Safety' },
      { label: 'Public' }
    ],
    createdDate: '1/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'safety',
    client: 'content',
    department: 'ai-safety',
    role: 'basic',
    project: 'public'
  },
  {
    id: '2',
    title: 'Healthcare Data Compliance',
    tags: [
      { label: 'Compliance' },
      { label: 'Advanced' },
      { label: 'Healthcare' },
      { label: 'HIPAA' },
      { label: 'Private' }
    ],
    createdDate: '2/3/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'compliance',
    client: 'healthcare',
    department: 'hipaa',
    role: 'advanced',
    project: 'private'
  },
  {
    id: '3',
    title: 'Financial Advice Guardrail',
    tags: [
      { label: 'Domain' },
      { label: 'Advanced' },
      { label: 'Finance' },
      { label: 'Banking' },
      { label: 'Team' }
    ],
    createdDate: '1/22/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'domain',
    client: 'finance',
    department: 'banking',
    role: 'advanced',
    project: 'team'
  },
  {
    id: '4',
    title: 'PII Detection and Redaction',
    tags: [
      { label: 'Security' },
      { label: 'Basic' },
      { label: 'PII' },
      { label: 'Data Protection' },
      { label: 'Public' }
    ],
    createdDate: '2/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'security',
    client: 'pii',
    department: 'data-protection',
    role: 'basic',
    project: 'public'
  },
  {
    id: '5',
    title: 'Legal Advice Limiter',
    tags: [
      { label: 'Domain' },
      { label: 'Intermediate' },
      { label: 'Legal' },
      { label: 'Compliance' },
      { label: 'Team' }
    ],
    createdDate: '3/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'domain',
    client: 'legal',
    department: 'compliance',
    role: 'intermediate',
    project: 'team'
  },
  {
    id: '6',
    title: 'Education Content Guardrail',
    tags: [
      { label: 'Safety' },
      { label: 'Basic' },
      { label: 'Education' },
      { label: 'Academic' },
      { label: 'Public' }
    ],
    createdDate: '1/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'safety',
    client: 'education',
    department: 'academic',
    role: 'basic',
    project: 'public'
  },
  {
    id: '7',
    title: 'GDPR Compliance Guardrail',
    tags: [
      { label: 'Compliance' },
      { label: 'Advanced' },
      { label: 'GDPR' },
      { label: 'EU' },
      { label: 'Organization' }
    ],
    createdDate: '2/28/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'compliance',
    client: 'gdpr',
    department: 'eu',
    role: 'advanced',
    project: 'organization'
  },
  {
    id: '8',
    title: 'Code Output Security',
    tags: [
      { label: 'Security' },
      { label: 'Advanced' },
      { label: 'Code' },
      { label: 'Cybersecurity' },
      { label: 'Private' }
    ],
    createdDate: '3/12/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'security',
    client: 'code',
    department: 'cybersecurity',
    role: 'advanced',
    project: 'private'
  },
  {
    id: '9',
    title: 'Harmful Content Filter',
    tags: [
      { label: 'Safety' },
      { label: 'Basic' },
      { label: 'Content' },
      { label: 'Moderation' },
      { label: 'Public' }
    ],
    createdDate: '1/8/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'safety',
    client: 'content',
    department: 'moderation',
    role: 'basic',
    project: 'public'
  },
  {
    id: '10',
    title: 'Pharmaceutical Industry Guardrail',
    tags: [
      { label: 'Domain' },
      { label: 'Advanced' },
      { label: 'Pharma' },
      { label: 'Healthcare' },
      { label: 'Organization' }
    ],
    createdDate: '2/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'domain',
    client: 'pharma',
    department: 'healthcare',
    role: 'advanced',
    project: 'organization'
  },
  {
    id: '11',
    title: 'Financial Fraud Prevention',
    tags: [
      { label: 'Security' },
      { label: 'Advanced' },
      { label: 'Finance' },
      { label: 'Fraud' },
      { label: 'Private' }
    ],
    createdDate: '3/1/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'security',
    client: 'finance',
    department: 'fraud',
    role: 'advanced',
    project: 'private'
  },
  {
    id: '12',
    title: 'Language Tone Moderator',
    tags: [
      { label: 'Safety' },
      { label: 'Intermediate' },
      { label: 'Language' },
      { label: 'Communication' },
      { label: 'Team' }
    ],
    createdDate: '1/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'safety',
    client: 'language',
    department: 'communication',
    role: 'intermediate',
    project: 'team'
  },
  {
    id: '13',
    title: 'CCPA Compliance Guardrail',
    tags: [
      { label: 'Compliance' },
      { label: 'Advanced' },
      { label: 'CCPA' },
      { label: 'California' },
      { label: 'Private' }
    ],
    createdDate: '2/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'compliance',
    client: 'ccpa',
    department: 'california',
    role: 'advanced',
    project: 'private'
  },
  {
    id: '14',
    title: 'Medical Advice Limiter',
    tags: [
      { label: 'Domain' },
      { label: 'Advanced' },
      { label: 'Medical' },
      { label: 'Healthcare' },
      { label: 'Organization' }
    ],
    createdDate: '3/8/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'domain',
    client: 'medical',
    department: 'healthcare',
    role: 'advanced',
    project: 'organization'
  },
  {
    id: '15',
    title: 'Child Safety Guardrail',
    tags: [
      { label: 'Safety' },
      { label: 'Advanced' },
      { label: 'Child' },
      { label: 'Protection' },
      { label: 'Public' }
    ],
    createdDate: '1/25/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'safety',
    client: 'child',
    department: 'protection',
    role: 'advanced',
    project: 'public'
  },
  {
    id: '16',
    title: 'SOC 2 Compliance Guardrail',
    tags: [
      { label: 'Compliance' },
      { label: 'Advanced' },
      { label: 'SOC2' },
      { label: 'Security' },
      { label: 'Organization' }
    ],
    createdDate: '2/12/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'compliance',
    client: 'soc2',
    department: 'security',
    role: 'advanced',
    project: 'organization'
  },
  {
    id: '17',
    title: 'Insurance Industry Guardrail',
    tags: [
      { label: 'Domain' },
      { label: 'Intermediate' },
      { label: 'Insurance' },
      { label: 'Finance' },
      { label: 'Team' }
    ],
    createdDate: '3/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'domain',
    client: 'insurance',
    department: 'finance',
    role: 'intermediate',
    project: 'team'
  },
  {
    id: '18',
    title: 'Intellectual Property Protection',
    tags: [
      { label: 'Security' },
      { label: 'Advanced' },
      { label: 'IP' },
      { label: 'Legal' },
      { label: 'Private' }
    ],
    createdDate: '1/30/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'security',
    client: 'ip',
    department: 'legal',
    role: 'advanced',
    project: 'private'
  },
  {
    id: '19',
    title: 'Hate Speech Filter',
    tags: [
      { label: 'Safety' },
      { label: 'Basic' },
      { label: 'Content' },
      { label: 'Moderation' },
      { label: 'Public' }
    ],
    createdDate: '2/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'safety',
    client: 'content',
    department: 'moderation',
    role: 'basic',
    project: 'public'
  },
  {
    id: '20',
    title: 'Government Data Protection',
    tags: [
      { label: 'Compliance' },
      { label: 'Advanced' },
      { label: 'Government' },
      { label: 'Security' },
      { label: 'Organization' }
    ],
    createdDate: '3/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Guardrail' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Guardrail' }
    ],
    // Filter properties
    userType: 'compliance',
    client: 'government',
    department: 'security',
    role: 'advanced',
    project: 'organization'
  }
]; 