<div class="custom-stepper">
  <div class="stepper-container">
    <div class="stepper-header">
      <div class="steps-wrapper">
        @for (step of steps; track step.id; let i = $index) {
          <div class="step-item"
               [class.completed]="step.state === 'completed'"
               [class.active]="step.state === 'active'"
               [class.inactive]="step.state === 'inactive'"
               (click)="onStepClick(i)">

            <!-- Step Circle -->
            <div class="step-circle">
              @if (step.state === 'completed') {
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              } @else {
                <!-- <span class="step-number">{{ i + 1 }}</span> -->
              }
            </div>

            <!-- Step Label -->
            <div class="step-label">
              <span class="step-title">{{ step.label }}</span>
              <!-- @if (step.description && showDescriptions) {
                <span class="step-description">{{ step.description }}</span>
              } -->
            </div>

            <!-- Connector Line -->
            @if (i < steps.length - 1) {
              <div class="step-connector"
                   [class.completed]="steps[i + 1].state === 'completed' || steps[i + 1].state === 'active'">
              </div>
            }
          </div>
        }
      </div>
    </div>

    <!-- Progress Bar -->
    <!-- @if (showProgressBar) {
      <div class="progress-container">
        <div class="progress-bar">
          <div class="progress-fill" [style.width.%]="progressPercentage"></div>
        </div>
        <span class="progress-text">{{ progressPercentage }}% Complete</span>
      </div>
    } -->
  </div>
</div>
