import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { SelectDropdownComponent, SelectOption } from '../select-dropdown/select-dropdown.component';
import { CardComponent } from '../card/card.component';

@Component({
  selector: 'app-suggested-prompt',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    SelectDropdownComponent,
    CardComponent,
  ],
  templateUrl: './suggested-prompt.component.html',
  styleUrls: ['./suggested-prompt.component.scss']
})
export class SuggestedPromptComponent {
  @Input() visible: boolean = false;
  @Input() form!: FormGroup;
  
  @Output() editPrompt = new EventEmitter<void>();
  @Output() enhanceBackstory = new EventEmitter<void>();
  @Output() enhanceOutput = new EventEmitter<void>();
  
  promptOptions: SelectOption[] = [
    { value: 'default', label: 'Choose Prompt' },
    { value: 'ruby-developer', label: 'Senior Ruby Developer' },
    { value: 'python-developer', label: 'Python Developer' },
    { value: 'data-scientist', label: 'Data Scientist' },
    { value: 'frontend-developer', label: 'Frontend Developer' }
  ];
  
  selectedPrompt: string = 'default';
  
  onPromptChange(value: string | string[]): void {
    // Ensure we're working with a single string value
    const selectedValue = Array.isArray(value) ? value[0] : value;
    this.selectedPrompt = selectedValue;
    // In a real application, this would fetch the prompt details
    console.log('Selected prompt:', selectedValue);
  }
  
  onEdit(): void {
    this.editPrompt.emit();
  }
  
  onEnhanceBackstory(): void {
    this.enhanceBackstory.emit();
  }
  
  onEnhanceOutput(): void {
    this.enhanceOutput.emit();
  }
} 