// Dialog Backdrop
.dialog-backdrop {
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
}

// Dialog Container
.dialog-container {
  max-width: 90vw;
  max-height: 90vh;
  min-width: 400px;
  border: 2px solid #007bff;

  // Blue corner indicators (matching Figma design)
  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
  }

  &::before {
    top: -1px;
    right: 50px;
    content: '24';
  }

  &::after {
    bottom: -1px;
    left: 50px;
    content: '24';
  }
}

// Card Icon Styling
.card-icon {
  color: #007bff;
  font-size: 14px;
}

// Close Button
.btn-close {
  font-size: 18px;
  color: #6c757d;

  &:hover {
    color: #000;
  }
}

// Dropdown Menu
.dropdown-menu {
  min-width: 120px;
  transform: translateX(-50%);
  left: 50%;

  &.show {
    display: block;
  }

  &:not(.show) {
    display: none;
  }
}

// Dropdown Items
.dropdown-item {
  transition: background-color 0.15s ease-in-out;
  text-decoration: none;

  &:hover {
    background-color: #f8f9fa;
  }

  &:focus {
    background-color: #e9ecef;
    outline: none;
  }
}