<div class="knowledge-base-container">
  <!-- Header section -->
  <div class="knowledge-base-header">
    <div class="header-content">
      <div class="search-section">
        <app-search-bar></app-search-bar>
      </div>
      <div class="action-buttons">
        <button class="action-button" [class.active-filter]="showFilterBar" (click)="toggleFilterBar()">
          Filters 
          <!-- Down arrow when closed, Up arrow when open -->
          <svg *ngIf="!showFilterBar" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <svg *ngIf="showFilterBar" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 15L12 10L17 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
  
  <!-- Filter Bar -->
  <div class="filter-section" *ngIf="showFilterBar">
    <app-filter-bar 
      [filterConfig]="filterConfig"
      (filterChange)="onFilterChange($event)">
    </app-filter-bar>
  </div>
  
  <div class="cards-container">
    <!-- Create Knowledge Base Card - show only on first page -->
    <app-create-card 
      *ngIf="currentPage === 1"
      label="Create Knowledge Base" 
      (cardClick)="onCreateKnowledgeBase()">
    </app-create-card>
    
    <!-- No results message -->
    <div class="no-results" *ngIf="filteredKnowledgeBase.length === 0">
      No knowledge bases found matching your criteria
    </div>
    
    <!-- Knowledge Base Data Cards -->
    <app-data-card 
      *ngFor="let knowledgeBase of displayedKnowledgeBase" 
      [data]="knowledgeBase"
      (cardClicked)="onCardClicked($event)"
      (actionClicked)="onActionClicked($event)">
    </app-data-card>
  </div>
  
  <!-- Page Footer with Pagination -->
  <app-page-footer
    *ngIf="filteredKnowledgeBase.length > 0"
    [totalItems]="filteredKnowledgeBase.length + (currentPage === 1 ? 1 : 0)" 
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  ></app-page-footer>
</div>
