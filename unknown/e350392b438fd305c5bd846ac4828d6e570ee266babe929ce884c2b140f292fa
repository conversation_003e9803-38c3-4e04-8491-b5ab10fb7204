import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule, FormControl } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CardComponent } from '../../../../shared/components/card/card.component';
import { FormFieldComponent } from '../../../../shared/components/form-field/form-field.component';
import { ChatInterfaceComponent } from '../../../../shared/components/chat-interface/chat-interface.component';
import { ChatMessage } from '../../../../shared/components/chat-window/chat-window.component';
import { MOCK_TOOLS } from '../../../../shared/mock-data/tool-mock-data';
import { ToolExecutionService } from '../../../../shared/services/tool-execution/tool-execution.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-create-tools',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardComponent,
    FormFieldComponent,
    ChatInterfaceComponent
  ],
  templateUrl: './create-tools.component.html',
  styleUrls: ['./create-tools.component.scss']
})
export class CreateToolsComponent implements OnInit, OnDestroy {
  // Mode flags
  toolId: string | null = null;
  isEditMode: boolean = false;
  isExecuteMode: boolean = false;
  showChatInterface: boolean = false;
  
  toolForm: FormGroup;
  
  // Chat interface properties
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;
  
  // Subscription
  private executionSubscription: Subscription = new Subscription();
  
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toolExecutionService: ToolExecutionService
  ) {
    this.toolForm = this.fb.group({
      // Tool details
      name: [''],
      description: [''],
      
      // Filters
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],
      
      // Tool class definition
      classDefinition: ['<!DOCTYPE html>\n<html lang="en">\n<head>\n  <meta charset="UTF-8">\n  <meta name="viewport" content="width=device-width, initial-scale=1">\n  <title>Agentic Activity Log</title>\n  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">\n</head>\n<body>\n\n<div class="container mt-5">\n  <h2 class="text-center mb-4">Agentic Activity Log</h2>\n  \n  <div class="card shadow-lg">\n    <div class="card-body">\n      <h5 class="card-title">Lead Qualification Agent</h5>\n      <p><strong>Execution ID:</strong> LQ-20250323-001</p>\n      <p><strong>Status:</strong> <span class="badge bg-success">Completed</span></p>\n      \n      <table class="table table-bordered mt-3">\n        <thead class="table-dark">\n          <tr>\n            <th>Step</th>\n            <th>Details</th>\n            <th>Time</th>\n        </thead>\n      </table>\n    </div>\n  </div>\n</div>\n\n<footer>\n  <tr>\n    <td>Note: For any user-defined tools, if additional Python libraries are required to be installed, please contact AVA Core Team.</td>\n  </tr>\n</footer>\n</body>\n</html>']
    });
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.toolId = this.route.snapshot.paramMap.get('id');
    const executeParam = this.route.snapshot.queryParamMap.get('execute');
    
    this.isEditMode = !!this.toolId;
    this.isExecuteMode = executeParam === 'true';
    this.showChatInterface = this.isExecuteMode;
    
    if (this.isEditMode && this.toolId) {
      // Load tool data for editing
      console.log(`Editing tool with ID: ${this.toolId}`);
      this.loadToolData(this.toolId);
      
      // If in execute mode, start the execution
      if (this.isExecuteMode) {
        // Initialize messages
        this.chatMessages = [
          {
            from: 'ai',
            text: 'Hi Akash, this is the tool testing'
          },
          {
            from: 'user',
            text: 'Test this input'
          },
          {
            from: 'ai',
            text: 'Here is the output'
          }
        ];
        
        // Start execution (after a small delay to ensure UI is ready)
        setTimeout(() => {
          this.toolExecutionService.startExecution(this.toolId!, this.chatMessages);
          
          // Subscribe to execution state changes
          this.executionSubscription = this.toolExecutionService.getExecutionState().subscribe(state => {
            if (state.isExecuting && state.toolId === this.toolId) {
              this.chatMessages = state.chatMessages;
            }
          });
        }, 100);
      }
    }
  }
  
  ngOnDestroy(): void {
    // Clean up subscription
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }
  }

  onSave(): void {
    console.log('Form data:', this.toolForm.value);
    
    // Get the return page if available
    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
    const pageNumber = returnPage ? parseInt(returnPage) : 1;
    
    if (this.isEditMode) {
      console.log('Updating existing tool');
      // this.toolService.updateTool(this.toolId, this.toolForm.value);
      this.router.navigate(['/libraries/tools'], {
        queryParams: { page: pageNumber }
      });
    } else {
      console.log('Creating new tool');
      // this.toolService.createTool(this.toolForm.value);
      this.router.navigate(['/libraries/tools']);
    }
  }
  
  onExecute(): void {
    console.log('Executing tool:', this.toolForm.value);
    if (this.toolId) {
      // If we're already in execute mode with chat interface showing
      if (this.isExecuteMode && this.showChatInterface) {
        // Process the execution
        console.log('Processing execution');
      } else {
        console.log('Entering execute mode, showing chat interface');
        
        // Set flags to show chat interface
        this.isExecuteMode = true;
        this.showChatInterface = true;
        
        // Set the initial messages
        this.chatMessages = [
          {
            from: 'ai',
            text: 'Hi Akash, this is the tool testing'
          },
          {
            from: 'user',
            text: 'Test this input'
          },
          {
            from: 'ai',
            text: 'Here is the output'
          }
        ];
        
        // Delay starting the execution service slightly to allow UI to update
        setTimeout(() => {
          console.log('Starting execution service for tool ID:', this.toolId);
          this.toolExecutionService.startExecution(this.toolId!, this.chatMessages);
        }, 100);
      }
    }
  }

  onExit(): void {
    // If we're in execute mode with chat interface showing
    if (this.isExecuteMode && this.isEditMode) {
      // Return to edit mode without chat interface
      this.isExecuteMode = false;
      this.showChatInterface = false;
      this.toolExecutionService.stopExecution();
      console.log('Exited execution mode, returning to edit mode');
    } else {
      // Get the return page if available
      const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
      const pageNumber = returnPage ? parseInt(returnPage) : 1;
      
      // Exit to tools list at correct page
      this.router.navigate(['/libraries/tools'], {
        queryParams: { page: pageNumber }
      });
    }
  }
  
  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.toolForm.get(name) as FormControl;
  }
  
  // Load tool data from mock data
  loadToolData(toolId: string): void {
    // In a real app, this would use data fetched from a service
    const tool = MOCK_TOOLS.find(t => t.id === toolId);
    
    if (tool) {
      // Set form values based on the tool data
      this.toolForm.get('name')?.setValue(tool.title);
      this.toolForm.get('description')?.setValue(`This is the ${tool.title} description.`);
      
      // Set filter data based on the tool properties
      this.toolForm.get('organization')?.setValue('Ascendion');
      this.toolForm.get('domain')?.setValue(tool.department || 'Platform Engineering');
      this.toolForm.get('project')?.setValue(tool.project || 'Digital Ascender');
      this.toolForm.get('team')?.setValue('Revamp Demo');
      
      // Set class definition based on the tool type
      // (In a real app, you would have actual code here)
    }
  }
  
  // Handle chat messages
  handleChatMessage(message: string): void {
    if (this.toolId && this.isExecuteMode) {
      this.isProcessingChat = true;
      
      // Process through the service - it will handle adding user and AI messages
      this.toolExecutionService.processUserMessage(message);
      
      // Reset loading state after a delay that matches the service's response time
      setTimeout(() => {
        this.isProcessingChat = false;
      }, 1000);
    }
  }
}