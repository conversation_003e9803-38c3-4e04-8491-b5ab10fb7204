<div class="select-container" [class.disabled]="disabled">
  <label *ngIf="label" class="select-label">{{ label }}</label>

  <div
    class="select-field"
    (click)="toggleDropdown()"
    [class.open]="isOpen"
    [class.required]="required"
    [class.multi-select]="isMultiSelect"
  >
    <div class="selected-value">
      <span>{{ getSelectedLabel() }}</span>
    </div>

    <div class="select-icon">
      <svg
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        [class.open]="isOpen"
      >
        <path
          d="M2.5 4L6 7.5L9.5 4"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
  </div>

  <div class="dropdown-menu" *ngIf="isOpen">
    <div
      *ngFor="let option of options"
      class="dropdown-item"
      [class.selected]="isOptionSelected(option)"
      (click)="selectOption(option, $event)"
    >
      <div class="option-content">
        <div *ngIf="isMultiSelect" class="checkbox-container">
          <div class="checkbox" [class.checked]="isOptionSelected(option)">
            <svg
              *ngIf="isOptionSelected(option)"
              width="10"
              height="10"
              viewBox="0 0 12 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2 6L5 9L10 3"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
        {{ option.label }}
      </div>
    </div>
  </div>
</div>
