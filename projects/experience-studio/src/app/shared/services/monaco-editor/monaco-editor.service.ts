import { Injectable, NgZone } from '@angular/core';
import { BehaviorSubject, Observable, from, of } from 'rxjs';
import { catchError, map, shareReplay, tap } from 'rxjs/operators';
import { createLogger } from '../../utils/logger';

/**
 * Service for managing Monaco Editor instances and configuration
 */
@Injectable({
  providedIn: 'root'
})
export class MonacoEditorService {
  private monaco: typeof import('monaco-editor') | null = null;
  private monacoLoading = false;
  private monacoLoaded$ = new BehaviorSubject<boolean>(false);
  private editorInstances: Map<string, any> = new Map();
  private editorModels: Map<string, any> = new Map();
  private logger = createLogger('MonacoEditorService');

  constructor(private ngZone: NgZone) {
    // Configure Monaco environment when service is created
    this.configureMonacoEnvironment();
  }

  /**
   * Configure the Monaco environment for web workers
   */
  private configureMonacoEnvironment(): void {
    // Only configure once
    if ((window as any).MonacoEnvironment) {
      return;
    }

    // Configure Monaco environment to load workers on demand
    (window as any).MonacoEnvironment = {
      getWorkerUrl: (_moduleId: string, label: string) => {
        const workerMap: { [key: string]: string } = {
          typescript: 'ts.worker.js',
          javascript: 'ts.worker.js',
          html: 'html.worker.js',
          css: 'css.worker.js',
          scss: 'css.worker.js',
          less: 'css.worker.js',
          json: 'json.worker.js'
        };

        // Return the appropriate worker URL based on the language
        return workerMap[label] || 'editor.worker.js';
      }
    };
  }

  /**
   * Lazy load Monaco Editor
   * @returns Observable that resolves when Monaco is loaded
   */
  public loadMonaco(): Observable<typeof import('monaco-editor')> {
    // If Monaco is already loaded, return it immediately
    if (this.monaco) {
      return of(this.monaco);
    }

    // If Monaco is currently loading, return the loading observable
    if (this.monacoLoading) {
      return this.monacoLoaded$.pipe(
        map(() => this.monaco!),
        shareReplay(1)
      );
    }

    // Start loading Monaco
    this.monacoLoading = true;

    // Import Monaco dynamically
    return from(import('monaco-editor')).pipe(
      tap(monaco => {
        this.monaco = monaco;
        this.monacoLoaded$.next(true);
        this.monacoLoading = false;
        this.defineCustomThemes(monaco);
      }),
      catchError(error => {
        this.logger.error('Failed to load Monaco Editor:', error);
        this.monacoLoading = false;
        this.monacoLoaded$.next(false);
        throw error;
      }),
      shareReplay(1)
    );
  }

  /**
   * Create a Monaco Editor instance
   * @param domElement The DOM element to create the editor in
   * @param options Editor options
   * @param id Optional ID for the editor instance
   * @returns Observable that resolves with the editor instance
   */
  public createEditor(
    domElement: HTMLElement,
    options: any = {},
    id: string = 'default'
  ): Observable<any> {
    return this.loadMonaco().pipe(
      map(monaco => {
        // Run editor creation inside NgZone to ensure change detection works properly
        return this.ngZone.run(() => {
          // Create the editor with default options merged with provided options
          const defaultOptions = {
            automaticLayout: true,
            theme: 'dark-theme',
            language: 'plaintext',
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            fontSize: 14,
            tabSize: 2,
            lineHeight: 22
          };

          const editor = monaco.editor.create(
            domElement,
            { ...defaultOptions, ...options }
          );

          // Store the editor instance for later retrieval or disposal
          this.editorInstances.set(id, editor);

          return editor;
        });
      })
    );
  }

  /**
   * Get an existing editor instance by ID
   * @param id The ID of the editor instance
   * @returns The editor instance or null if not found
   */
  public getEditor(id: string = 'default'): any {
    return this.editorInstances.get(id) || null;
  }

  /**
   * Dispose of an editor instance
   * @param id The ID of the editor instance to dispose
   */
  public disposeEditor(id: string = 'default'): void {
    const editor = this.editorInstances.get(id);
    if (editor) {
      editor.dispose();
      this.editorInstances.delete(id);
    }
  }

  /**
   * Create or get a model for a file
   * @param content The content of the file
   * @param language The language of the file
   * @param uri Optional URI for the model
   * @returns The model
   */
  public createOrGetModel(content: string, language: string, uri?: string): Observable<any> {
    return this.loadMonaco().pipe(
      map(monaco => {
        // If URI is provided and model exists, return it
        if (uri && this.editorModels.has(uri)) {
          return this.editorModels.get(uri);
        }

        // Create a new model
        const model = monaco.editor.createModel(content, language, uri ? monaco.Uri.parse(uri) : undefined);

        // Store the model if URI is provided
        if (uri) {
          this.editorModels.set(uri, model);
        }

        return model;
      })
    );
  }

  /**
   * Dispose of a model
   * @param uri The URI of the model to dispose
   */
  public disposeModel(uri: string): void {
    const model = this.editorModels.get(uri);
    if (model) {
      model.dispose();
      this.editorModels.delete(uri);
    }
  }

  /**
   * Define custom themes for Monaco Editor
   * @param monaco The Monaco instance
   */
  private defineCustomThemes(monaco: typeof import('monaco-editor')): void {
    // Define light theme
    monaco.editor.defineTheme('light-theme', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: '', foreground: '000000' },
        { token: 'comment', foreground: '008000' },
        { token: 'keyword', foreground: '0000FF' },
        { token: 'string', foreground: 'A31515' },
        { token: 'number', foreground: '098658' },
        { token: 'type', foreground: '267F99' }
      ],
      colors: {
        // Use transparent background
        'editor.background': '#00000000',
        'editor.foreground': '#000000',
        'editor.lineHighlightBackground': '#f5f5f5',
        'editorCursor.foreground': '#000000',
        'editor.selectionBackground': '#add6ff',
        'editor.inactiveSelectionBackground': '#e5ebf1',
        'editorLineNumber.foreground': '#6e7681',
        'editorGutter.background': '#00000000',
        'scrollbarSlider.background': '#64646466',
        'scrollbarSlider.hoverBackground': '#646464B3',
        'scrollbarSlider.activeBackground': '#646464B3',
        // // Minimap settings
        // 'minimap.background': '#00000000',
        // 'minimap.foreground': '#00000080',
        // 'minimap.selectionHighlight': '#add6ff80',
        // Overview ruler settings
        'editorOverviewRuler.background': '#00000000',
        'editorOverviewRuler.border': '#00000000'
      }
    });

    // Define dark theme
    monaco.editor.defineTheme('dark-theme', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: '', foreground: 'D4D4D4' },
        { token: 'comment', foreground: '6A9955' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'type', foreground: '4EC9B0' }
      ],
      colors: {
        // Use transparent background
        'editor.background': '#00000000',
        'editor.foreground': '#D4D4D4',
        'editor.lineHighlightBackground': '#2F3137',
        'editorCursor.foreground': '#FFFFFF',
        'editor.selectionBackground': '#264F78',
        'editor.inactiveSelectionBackground': '#3A3D41',
        'editorLineNumber.foreground': '#858585',
        'editorGutter.background': '#00000000',
        'scrollbarSlider.background': '#79797966',
        'scrollbarSlider.hoverBackground': '#646464B3',
        'scrollbarSlider.activeBackground': '#646464B3',
        // Minimap settings
        // 'minimap.background': '#00000000',
        // 'minimap.foreground': '#FFFFFF80',
        // 'minimap.selectionHighlight': '#264F7880',
        // Overview ruler settings
        'editorOverviewRuler.background': '#00000000',
        'editorOverviewRuler.border': '#00000000'
      }
    });
  }

  /**
   * Set the theme for all editor instances
   * @param theme The theme to set ('light-theme' or 'dark-theme')
   */
  public setTheme(theme: 'light-theme' | 'dark-theme'): void {
    if (!this.monaco) {
      return;
    }

    this.monaco.editor.setTheme(theme);
  }

  /**
   * Get the language for a file based on its extension
   * @param fileName The name of the file
   * @returns The language ID
   */
  public getLanguageForFile(fileName: string): string {
    const ext = fileName.split('.').pop()?.toLowerCase();

    const languageMap: { [key: string]: string } = {
      'ts': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'tsx': 'typescript',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'less': 'less',
      'json': 'json',
      'md': 'markdown',
      'yml': 'yaml',
      'yaml': 'yaml',
      'xml': 'xml',
      'svg': 'xml',
      'py': 'python',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'cs': 'csharp',
      'go': 'go',
      'rs': 'rust',
      'rb': 'ruby',
      'php': 'php'
    };

    return ext && languageMap[ext] ? languageMap[ext] : 'plaintext';
  }

  /**
   * Dispose all editor instances and models
   */
  public disposeAll(): void {
    // Dispose all editor instances
    this.editorInstances.forEach(editor => {
      editor.dispose();
    });
    this.editorInstances.clear();

    // Dispose all models
    this.editorModels.forEach(model => {
      model.dispose();
    });
    this.editorModels.clear();
  }
}
