<button
  [type]="type"
  [ngClass]="buttonClasses"
  [disabled]="disabled"
  [attr.aria-label]="ariaLabel || null"
  [attr.aria-disabled]="disabled ? true : null"
  (click)="onClick($event)"
>
  <!-- Loading Spinner -->
  <div *ngIf="loading" class="spinner">
    <div class="spinner-circle"></div>
  </div>

  <!-- Left Icon -->
  <span *ngIf="icon && iconPosition === 'left'" class="button-icon left">
    <img [src]="icon" alt="" aria-hidden="true" />
  </span>

  <!-- Button Content -->
  <span class="button-content">
    <ng-content></ng-content>
  </span>

  <!-- Right Icon -->
  <span *ngIf="icon && iconPosition === 'right'" class="button-icon right">
    <img [src]="icon" alt="" aria-hidden="true" />
  </span>
</button>
