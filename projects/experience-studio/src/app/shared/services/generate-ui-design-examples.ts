/**
 * Generate UI Design API Examples and Test Payloads
 *
 * This file contains example payloads and test data for the Generate UI Design feature.
 * Use these examples to understand the expected API payload structure and for testing purposes.
 */

import { GenerateUIDesignRequest } from './generate-ui-design.service';

/**
 * Example API Payloads for Generate UI Design
 */
export const GENERATE_UI_DESIGN_EXAMPLES = {

  /**
   * Mobile E-commerce App Example (Default - Mobile First)
   */
  mobileEcommerce: {
    prompt: 'Design a mobile e-commerce app interface with product listings, search functionality, and shopping cart'
    // Commented out for wireframe-generation/generate API - only prompt needed
    // input_prompt: 'Design a mobile e-commerce app interface with product listings, search functionality, and shopping cart',
    // title: 'UI',
    // application_target: 'mobile' as const
  },

  /**
   * Web Dashboard Example
   */
  webDashboard: {
    prompt: 'Create a modern analytics dashboard with data visualization charts, user metrics, and a clean sidebar navigation'
    // Commented out for wireframe-generation/generate API - only prompt needed
    // input_prompt: 'Create a modern analytics dashboard with data visualization charts, user metrics, and a clean sidebar navigation',
    // title: 'UI',
    // application_target: 'web' as const
  },

  /**
   * Web Landing Page Example
   */
  webLandingPage: {
    prompt: 'Create a SaaS product landing page with hero section, feature highlights, pricing table, and call-to-action buttons'
    // Commented out for wireframe-generation/generate API - only prompt needed
    // input_prompt: 'Create a SaaS product landing page with hero section, feature highlights, pricing table, and call-to-action buttons',
    // title: 'UI',
    // application_target: 'web' as const
  },

  /**
   * Mobile Social Media App Example
   */
  mobileSocialMedia: {
    prompt: 'Design a social media app interface with news feed, user profiles, messaging, and photo sharing capabilities'
    // Commented out for wireframe-generation/generate API - only prompt needed
    // input_prompt: 'Design a social media app interface with news feed, user profiles, messaging, and photo sharing capabilities',
    // title: 'UI',
    // application_target: 'mobile' as const
  },

  /**
   * Web Admin Panel Example
   */
  webAdminPanel: {
    prompt: 'Create an admin panel interface with user management, settings, reports, and system monitoring tools'
    // Commented out for wireframe-generation/generate API - only prompt needed
    // input_prompt: 'Create an admin panel interface with user management, settings, reports, and system monitoring tools',
    // title: 'UI',
    // application_target: 'web' as const
  },

  /**
   * Mobile Banking App Example
   */
  mobileBanking: {
    prompt: 'Design a mobile banking app with account overview, transaction history, money transfer, and security features'
    // Commented out for wireframe-generation/generate API - only prompt needed
    // input_prompt: 'Design a mobile banking app with account overview, transaction history, money transfer, and security features',
    // title: 'UI',
    // application_target: 'mobile' as const
  }

} as const;

/**
 * API Endpoint Information
 */
export const API_INFO = {
  endpoint: '/wireframe-generation/generate',
  method: 'POST',
  queryParams: {
    user_signature: 'string (required)'
  },
  headers: {
    'Content-Type': 'application/json'
  },
  payload: {
    prompt: 'string (required) - Only prompt field is needed for wireframe generation'
  }
} as const;

/**
 * Complete API Request Example with Headers and Query Params
 */
export const COMPLETE_API_EXAMPLE = {
  url: 'https://ava-plus-experience-studio-api-dev.azurewebsites.net/wireframe-generation/generate?user_signature=<EMAIL>',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  body: GENERATE_UI_DESIGN_EXAMPLES.mobileEcommerce // Using mobile example as default - now only contains prompt
} as const;

/**
 * Expected API Response Structure
 */
export const EXPECTED_RESPONSE_EXAMPLE = {
  job_id: 'ui-design-job-12345',
  project_id: 'ui-project-67890',
  status: 'success',
  message: 'UI design generation started successfully'
} as const;

/**
 * Utility function to create a custom payload
 */
export function createCustomUIDesignPayload(
  prompt: string
  // applicationTarget: 'mobile' | 'web' = 'mobile' // Commented out for wireframe-generation/generate API
): GenerateUIDesignRequest {
  return {
    prompt: prompt
    // Commented out for wireframe-generation/generate API - only prompt needed
    // input_prompt: prompt,
    // title: 'UI',
    // application_target: applicationTarget
  };
}

/**
 * Utility function to get all example payloads as an array
 */
export function getAllExamplePayloads(): Array<{
  name: string;
  description: string;
  payload: GenerateUIDesignRequest;
}> {
  return [
    {
      name: 'mobileEcommerce',
      description: 'E-commerce mobile app interface (Default)',
      payload: GENERATE_UI_DESIGN_EXAMPLES.mobileEcommerce
    },
    {
      name: 'webDashboard',
      description: 'Modern analytics dashboard for web applications',
      payload: GENERATE_UI_DESIGN_EXAMPLES.webDashboard
    },
    {
      name: 'webLandingPage',
      description: 'SaaS product landing page',
      payload: GENERATE_UI_DESIGN_EXAMPLES.webLandingPage
    },
    {
      name: 'mobileSocialMedia',
      description: 'Social media mobile app interface',
      payload: GENERATE_UI_DESIGN_EXAMPLES.mobileSocialMedia
    },
    {
      name: 'webAdminPanel',
      description: 'Admin panel for web applications',
      payload: GENERATE_UI_DESIGN_EXAMPLES.webAdminPanel
    },
    {
      name: 'mobileBanking',
      description: 'Banking mobile app interface',
      payload: GENERATE_UI_DESIGN_EXAMPLES.mobileBanking
    }
  ];
}

/**
 * Console logging utility for debugging payloads
 */
export function logExamplePayloads(): void {
  console.group('🎨 Generate UI Design API Examples');

  console.log('📡 API Endpoint:', API_INFO.endpoint);
  console.log('🔧 Method:', API_INFO.method);
  console.log('📋 Query Params:', API_INFO.queryParams);

  console.group('📦 Example Payloads:');
  getAllExamplePayloads().forEach(example => {
    console.group(`${example.name} - ${example.description}`);
    console.log('Payload:', JSON.stringify(example.payload, null, 2));
    console.groupEnd();
  });
  console.groupEnd();

  console.group('🌐 Complete API Request Example:');
  console.log('URL:', COMPLETE_API_EXAMPLE.url);
  console.log('Method:', COMPLETE_API_EXAMPLE.method);
  console.log('Headers:', COMPLETE_API_EXAMPLE.headers);
  console.log('Body:', JSON.stringify(COMPLETE_API_EXAMPLE.body, null, 2));
  console.groupEnd();

  console.group('✅ Expected Response:');
  console.log(JSON.stringify(EXPECTED_RESPONSE_EXAMPLE, null, 2));
  console.groupEnd();

  console.groupEnd();
}

/**
 * Export all examples for easy access
 */
export default {
  GENERATE_UI_DESIGN_EXAMPLES,
  API_INFO,
  COMPLETE_API_EXAMPLE,
  EXPECTED_RESPONSE_EXAMPLE,
  createCustomUIDesignPayload,
  getAllExamplePayloads,
  logExamplePayloads
};
