.nav-item-container {
  position: relative;
  z-index: 1000; /* Ensure this is higher than any base elements */
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 400;
  color: var(--nav-item-color);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: var(--nav-hover);
    color: var(--nav-item-active-color);
    
    .nav-icon {
      opacity: 0.9;
    }
    
    &:after {
      opacity: 0.1;
    }
  }

  &.selected {
    background-color: var(--nav-item-active-bg);
    color: var(--nav-item-active-color);
    font-weight: 500;
    
    .item-icon {
      color: var(--nav-item-active-color);
    }
    
    .nav-icon {
      opacity: 1;
      filter: brightness(0) saturate(100%); /* Makes the icon black when selected */
    }
    
    &:after {
      opacity: 0;
    }
  }
  
  // Subtle gradient overlay for nav items
  &:after {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  transition: color 0.3s ease;
  color: var(--nav-item-color);
  
  &.selected {
    color: var(--nav-item-active-color);
  }
}

.item-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  transition: filter 0.3s ease, opacity 0.3s ease;
  opacity: 0.8;
  
  &.selected {
    opacity: 1;
    filter: brightness(0) saturate(100%); /* Makes the icon black when selected */
  }
}

.dropdown-arrow {
  margin-left: 4px;
  transition: transform 0.3s ease;
  height: 20px;
  display: flex;
  align-items: center;

  &.open {
    transform: rotate(180deg);
  }
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .nav-item {
    padding: 6px 8px;
    font-size: 14px;
  }
  
  .item-icon {
    width: 20px;
    height: 20px;
  }
  
  .nav-icon {
    width: 16px;
    height: 16px;
  }
} 