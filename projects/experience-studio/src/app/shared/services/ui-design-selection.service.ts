import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { createLogger } from '../utils/logger';

export interface SelectedNodeData {
  nodeId: string;
  fileName: string;
  htmlContent: string;
  rawContent: string;
  selectedImages?: string[]; // Array of selected image URLs/references within the node
  metadata?: {
    nodePosition: { x: number; y: number };
    nodeDimensions: { width: number; height: number };
    selectionTimestamp: number;
  };
}

// Multi-selection interfaces
export interface MultiSelectedNodeData {
  nodeId: string;
  fileName: string;
  htmlContent: string;
  rawContent: string;
  selectedImages?: string[];
  metadata?: {
    nodePosition: { x: number; y: number };
    nodeDimensions: { width: number; height: number };
    selectionTimestamp: number;
  };
}

export interface SelectionState {
  selectedNodes: MultiSelectedNodeData[];
  isMultiSelectMode: boolean;
  lastSelectedNodeId: string | null;
}

export interface EditRequest {
  code: Array<{
    fileName: string;
    content: string;
  }>;
  user_request: string;
}

export interface EditResponse extends Array<{
  fileName: string;
  content: string;
}> {}

@Injectable({
  providedIn: 'root'
})
export class UIDesignSelectionService {
  private readonly logger = createLogger('UIDesignSelectionService');

  // Legacy single selection state (maintained for backward compatibility)
  private readonly selectedNode$ = new BehaviorSubject<SelectedNodeData | null>(null);

  // Multi-selection state management
  private readonly selectedNodes$ = new BehaviorSubject<MultiSelectedNodeData[]>([]);
  private readonly isMultiSelectMode$ = new BehaviorSubject<boolean>(false);
  private readonly lastSelectedNodeId$ = new BehaviorSubject<string | null>(null);

  // Prompt bar and editing state
  private readonly isPromptBarEnabled$ = new BehaviorSubject<boolean>(false);
  private readonly isEditingInProgress$ = new BehaviorSubject<boolean>(false);

  // Tooltip and canvas state management
  private readonly showCanvasTooltip$ = new BehaviorSubject<boolean>(false);
  private readonly nodesCreated$ = new BehaviorSubject<boolean>(false);

  // Multi-image selection within selected node
  private readonly selectedImages$ = new BehaviorSubject<string[]>([]);

  constructor() {
    this.logger.info('🎯 UI Design Selection Service initialized with multi-selection support');
  }

  /**
   * Get selected node observable (legacy single selection)
   */
  get selectedNode(): Observable<SelectedNodeData | null> {
    return this.selectedNode$.asObservable();
  }

  /**
   * Get selected nodes observable (multi-selection)
   */
  get selectedNodes(): Observable<MultiSelectedNodeData[]> {
    return this.selectedNodes$.asObservable();
  }

  /**
   * Get multi-select mode observable
   */
  get isMultiSelectMode(): Observable<boolean> {
    return this.isMultiSelectMode$.asObservable();
  }

  /**
   * Get last selected node ID observable
   */
  get lastSelectedNodeId(): Observable<string | null> {
    return this.lastSelectedNodeId$.asObservable();
  }

  /**
   * Get prompt bar enabled state observable
   */
  get isPromptBarEnabled(): Observable<boolean> {
    return this.isPromptBarEnabled$.asObservable();
  }

  /**
   * Get editing in progress state observable
   */
  get isEditingInProgress(): Observable<boolean> {
    return this.isEditingInProgress$.asObservable();
  }

  /**
   * Get canvas tooltip visibility observable
   */
  get showCanvasTooltip(): Observable<boolean> {
    return this.showCanvasTooltip$.asObservable();
  }

  /**
   * Get nodes created state observable
   */
  get nodesCreated(): Observable<boolean> {
    return this.nodesCreated$.asObservable();
  }

  /**
   * Get selected images observable
   */
  get selectedImages(): Observable<string[]> {
    return this.selectedImages$.asObservable();
  }

  /**
   * Get current selected node data (legacy single selection)
   */
  getSelectedNode(): SelectedNodeData | null {
    return this.selectedNode$.value;
  }

  /**
   * Get current selected nodes data (multi-selection)
   */
  getSelectedNodes(): MultiSelectedNodeData[] {
    return this.selectedNodes$.value;
  }

  /**
   * Get selected nodes count
   */
  getSelectedNodesCount(): number {
    return this.selectedNodes$.value.length;
  }

  /**
   * Check if multi-select mode is enabled
   */
  getIsMultiSelectMode(): boolean {
    return this.isMultiSelectMode$.value;
  }

  /**
   * Get last selected node ID
   */
  getLastSelectedNodeId(): string | null {
    return this.lastSelectedNodeId$.value;
  }

  /**
   * Check if prompt bar is enabled
   */
  getIsPromptBarEnabled(): boolean {
    return this.isPromptBarEnabled$.value;
  }

  /**
   * Check if editing is in progress
   */
  getIsEditingInProgress(): boolean {
    return this.isEditingInProgress$.value;
  }

  /**
   * Check if canvas tooltip should be shown
   */
  getShowCanvasTooltip(): boolean {
    return this.showCanvasTooltip$.value;
  }

  /**
   * Check if nodes have been created
   */
  getNodesCreated(): boolean {
    return this.nodesCreated$.value;
  }

  /**
   * Get currently selected images
   */
  getSelectedImages(): string[] {
    return this.selectedImages$.value;
  }

  /**
   * Select a node for editing (enhanced with metadata and multi-image support)
   * Legacy method maintained for backward compatibility
   */
  selectNode(nodeData: SelectedNodeData): void {
    // Add metadata if not provided
    const enhancedNodeData: SelectedNodeData = {
      ...nodeData,
      selectedImages: nodeData.selectedImages || [],
      metadata: nodeData.metadata || {
        nodePosition: { x: 0, y: 0 },
        nodeDimensions: { width: 300, height: 200 },
        selectionTimestamp: Date.now()
      }
    };

    this.logger.info('🎯 Selecting node for editing (legacy single selection):', {
      nodeId: enhancedNodeData.nodeId,
      fileName: enhancedNodeData.fileName,
      contentLength: enhancedNodeData.htmlContent.length,
      selectedImagesCount: enhancedNodeData.selectedImages?.length || 0,
      metadata: enhancedNodeData.metadata
    });

    // Update legacy selection state
    this.selectedNode$.next(enhancedNodeData);

    // Also update multi-selection state for consistency
    const multiNodeData: MultiSelectedNodeData = { ...enhancedNodeData };
    this.selectedNodes$.next([multiNodeData]);
    this.lastSelectedNodeId$.next(enhancedNodeData.nodeId);
    this.isMultiSelectMode$.next(false);

    this.isPromptBarEnabled$.next(true);

    // Hide tooltip when node is selected
    this.showCanvasTooltip$.next(false);

    // Initialize selected images for this node
    this.selectedImages$.next(enhancedNodeData.selectedImages || []);

    this.logger.info('✅ Node selected successfully, prompt bar enabled, tooltip hidden');
  }

  /**
   * Select multiple nodes with multi-select support
   */
  selectMultipleNodes(nodeId: string, nodeData: MultiSelectedNodeData, multiSelect: boolean = false): void {
    const currentSelected = this.selectedNodes$.value;
    let newSelection: MultiSelectedNodeData[];

    if (multiSelect) {
      // Multi-select mode: add/remove from selection
      const existingIndex = currentSelected.findIndex(node => node.nodeId === nodeId);

      if (existingIndex >= 0) {
        // Node already selected, remove it
        newSelection = currentSelected.filter(node => node.nodeId !== nodeId);
        this.logger.info('🎯 Removing node from multi-selection:', nodeId);
      } else {
        // Add node to selection
        const enhancedNodeData: MultiSelectedNodeData = {
          ...nodeData,
          selectedImages: nodeData.selectedImages || [],
          metadata: nodeData.metadata || {
            nodePosition: { x: 0, y: 0 },
            nodeDimensions: { width: 300, height: 200 },
            selectionTimestamp: Date.now()
          }
        };
        newSelection = [...currentSelected, enhancedNodeData];
        this.logger.info('🎯 Adding node to multi-selection:', nodeId);
      }

      this.isMultiSelectMode$.next(true);
    } else {
      // Single select mode: replace selection
      const enhancedNodeData: MultiSelectedNodeData = {
        ...nodeData,
        selectedImages: nodeData.selectedImages || [],
        metadata: nodeData.metadata || {
          nodePosition: { x: 0, y: 0 },
          nodeDimensions: { width: 300, height: 200 },
          selectionTimestamp: Date.now()
        }
      };
      newSelection = [enhancedNodeData];
      this.isMultiSelectMode$.next(false);
      this.logger.info('🎯 Single selecting node:', nodeId);
    }

    // Update selection state
    this.selectedNodes$.next(newSelection);
    this.lastSelectedNodeId$.next(nodeId);

    // Update legacy single selection for backward compatibility
    if (newSelection.length === 1) {
      this.selectedNode$.next(newSelection[0] as SelectedNodeData);
    } else {
      this.selectedNode$.next(null);
    }

    // Update prompt bar state
    this.isPromptBarEnabled$.next(newSelection.length > 0);

    // Hide tooltip when nodes are selected
    this.showCanvasTooltip$.next(false);

    this.logger.info('✅ Multi-selection updated:', {
      selectedCount: newSelection.length,
      isMultiSelect: multiSelect,
      selectedNodeIds: newSelection.map(n => n.nodeId)
    });
  }

  /**
   * Clear node selection (enhanced with tooltip management)
   */
  clearSelection(): void {
    this.logger.info('🔄 Clearing node selection');

    // Clear both legacy and multi-selection state
    this.selectedNode$.next(null);
    this.selectedNodes$.next([]);
    this.isMultiSelectMode$.next(false);
    this.lastSelectedNodeId$.next(null);

    this.isPromptBarEnabled$.next(false);
    this.isEditingInProgress$.next(false);
    this.selectedImages$.next([]);

    // Show tooltip again if nodes exist
    if (this.nodesCreated$.value) {
      this.showCanvasTooltip$.next(true);
    }

    this.logger.info('✅ Selection cleared, prompt bar disabled, tooltip shown if nodes exist');
  }

  /**
   * Select all available nodes
   */
  selectAllNodes(allNodes: MultiSelectedNodeData[]): void {
    this.logger.info('🎯 Selecting all nodes:', allNodes.length);

    const enhancedNodes = allNodes.map(node => ({
      ...node,
      selectedImages: node.selectedImages || [],
      metadata: node.metadata || {
        nodePosition: { x: 0, y: 0 },
        nodeDimensions: { width: 300, height: 200 },
        selectionTimestamp: Date.now()
      }
    }));

    this.selectedNodes$.next(enhancedNodes);
    this.isMultiSelectMode$.next(true);
    this.lastSelectedNodeId$.next(enhancedNodes[enhancedNodes.length - 1]?.nodeId || null);

    // Clear legacy single selection when multiple nodes are selected
    this.selectedNode$.next(null);

    this.isPromptBarEnabled$.next(enhancedNodes.length > 0);
    this.showCanvasTooltip$.next(false);

    this.logger.info('✅ All nodes selected:', enhancedNodes.length);
  }

  /**
   * Check if a specific node is selected
   */
  isNodeSelected(nodeId: string): boolean {
    const selectedNodes = this.selectedNodes$.value;
    return selectedNodes.some(node => node.nodeId === nodeId);
  }

  /**
   * Check if a specific node is the last selected
   */
  isLastSelectedNode(nodeId: string): boolean {
    return this.lastSelectedNodeId$.value === nodeId;
  }

  /**
   * Get selection summary for prompt bar placeholder
   */
  getSelectionSummary(): string {
    const selectedNodes = this.selectedNodes$.value;
    const count = selectedNodes.length;

    if (count === 0) {
      return 'Select a page above to start editing';
    } else if (count === 1) {
      return `Edit ${selectedNodes[0].fileName} - Describe your changes...`;
    } else {
      return `Edit ${count} selected pages - Describe your changes...`;
    }
  }

  /**
   * Update selected node content after editing
   */
  updateSelectedNodeContent(newContent: string, newRawContent: string): void {
    const currentSelection = this.selectedNode$.value;

    if (!currentSelection) {
      this.logger.warn('⚠️ No node selected, cannot update content');
      return;
    }

    const updatedSelection: SelectedNodeData = {
      ...currentSelection,
      htmlContent: newContent,
      rawContent: newRawContent
    };

    this.logger.info('🔄 Updating selected node content:', {
      nodeId: currentSelection.nodeId,
      fileName: currentSelection.fileName,
      newContentLength: newContent.length
    });

    this.selectedNode$.next(updatedSelection);
    this.logger.info('✅ Selected node content updated successfully');
  }

  /**
   * Set editing in progress state
   */
  setEditingInProgress(inProgress: boolean): void {
    this.isEditingInProgress$.next(inProgress);
    this.logger.info(`🔄 Editing in progress: ${inProgress}`);
  }

  /**
   * Build edit API request payload (enhanced for multi-selection)
   */
  buildEditRequest(userPrompt: string): EditRequest | null {
    const selectedNodes = this.selectedNodes$.value;

    if (selectedNodes.length === 0) {
      this.logger.error('❌ Cannot build edit request: No nodes selected');
      return null;
    }

    const request: EditRequest = {
      code: selectedNodes.map(node => ({
        fileName: node.fileName,
        content: node.rawContent
      })),
      user_request: userPrompt
    };

    this.logger.info('📡 Built multi-selection edit API request:', {
      selectedCount: selectedNodes.length,
      fileNames: selectedNodes.map(node => node.fileName),
      userRequest: userPrompt,
      totalContentLength: selectedNodes.reduce((sum, node) => sum + node.rawContent.length, 0)
    });

    return request;
  }

  /**
   * Build edit API request payload (legacy single selection)
   */
  buildLegacyEditRequest(userPrompt: string): EditRequest | null {
    const selectedNode = this.selectedNode$.value;

    if (!selectedNode) {
      this.logger.error('❌ Cannot build legacy edit request: No node selected');
      return null;
    }

    const request: EditRequest = {
      code: [{
        fileName: selectedNode.fileName,
        content: selectedNode.rawContent
      }],
      user_request: userPrompt
    };

    this.logger.info('📡 Built legacy edit API request:', {
      fileName: selectedNode.fileName,
      userRequest: userPrompt,
      contentLength: selectedNode.rawContent.length
    });

    return request;
  }

  /**
   * Validate edit API response (enhanced for multi-selection)
   */
  validateEditResponse(response: any): EditResponse | null {
    let parsedResponse: any;

    // Handle stringified JSON response
    if (typeof response === 'string') {
      try {
        parsedResponse = JSON.parse(response);
        this.logger.info('📦 Parsed stringified JSON response for validation');
      } catch (error) {
        this.logger.error('❌ Failed to parse stringified response for validation:', error);
        return null;
      }
    } else {
      parsedResponse = response;
    }

    if (!Array.isArray(parsedResponse)) {
      this.logger.error('❌ Invalid edit response format: not an array', parsedResponse);
      return null;
    }

    if (parsedResponse.length === 0) {
      this.logger.error('❌ Invalid edit response format: empty array');
      return null;
    }

    const selectedNodes = this.selectedNodes$.value;
    if (selectedNodes.length === 0) {
      this.logger.error('❌ No nodes selected for response validation');
      return null;
    }

    // Validate that all selected files are present in response
    const expectedFileNames = selectedNodes.map(node => node.fileName);
    const responseFileNames = parsedResponse.map((file: any) => file.fileName);

    const missingFiles = expectedFileNames.filter(fileName =>
      !responseFileNames.includes(fileName)
    );

    if (missingFiles.length > 0) {
      this.logger.warn('⚠️ Some selected files missing from response:', {
        expectedFiles: expectedFileNames,
        responseFiles: responseFileNames,
        missingFiles: missingFiles
      });
      // Continue processing with available files instead of failing
    }

    // Validate file structures
    for (const file of parsedResponse) {
      if (typeof file.fileName !== 'string' || typeof file.content !== 'string') {
        this.logger.error('❌ Invalid file structure in response:', file);
        return null;
      }
    }

    this.logger.info('✅ Multi-selection edit response validated successfully:', {
      expectedCount: expectedFileNames.length,
      responseCount: parsedResponse.length,
      matchingFiles: parsedResponse.filter((file: any) =>
        expectedFileNames.includes(file.fileName)
      ).length
    });

    return parsedResponse as EditResponse;
  }

  /**
   * Set nodes created state and manage tooltip visibility (enhanced for multi-selection)
   */
  setNodesCreated(created: boolean): void {
    this.nodesCreated$.next(created);

    if (created && this.selectedNodes$.value.length === 0) {
      // Show tooltip only if nodes are created and no nodes are selected
      this.showCanvasTooltip$.next(true);
      this.logger.info('✅ Nodes created, tooltip shown');
    } else if (!created) {
      // Hide tooltip if no nodes exist
      this.showCanvasTooltip$.next(false);
      this.logger.info('🔄 Nodes cleared, tooltip hidden');
    }
  }

  /**
   * Add image to selected images within current node
   */
  addSelectedImage(imageUrl: string): void {
    const currentImages = this.selectedImages$.value;
    if (!currentImages.includes(imageUrl)) {
      const updatedImages = [...currentImages, imageUrl];
      this.selectedImages$.next(updatedImages);

      // Update selected node data
      const currentNode = this.selectedNode$.value;
      if (currentNode) {
        const updatedNode = {
          ...currentNode,
          selectedImages: updatedImages
        };
        this.selectedNode$.next(updatedNode);
      }

      this.logger.info('🖼️ Image added to selection:', imageUrl);
    }
  }

  /**
   * Remove image from selected images within current node
   */
  removeSelectedImage(imageUrl: string): void {
    const currentImages = this.selectedImages$.value;
    const updatedImages = currentImages.filter(img => img !== imageUrl);
    this.selectedImages$.next(updatedImages);

    // Update selected node data
    const currentNode = this.selectedNode$.value;
    if (currentNode) {
      const updatedNode = {
        ...currentNode,
        selectedImages: updatedImages
      };
      this.selectedNode$.next(updatedNode);
    }

    this.logger.info('🖼️ Image removed from selection:', imageUrl);
  }

  /**
   * Clear all selected images within current node
   */
  clearSelectedImages(): void {
    this.selectedImages$.next([]);

    // Update selected node data
    const currentNode = this.selectedNode$.value;
    if (currentNode) {
      const updatedNode = {
        ...currentNode,
        selectedImages: []
      };
      this.selectedNode$.next(updatedNode);
    }

    this.logger.info('🖼️ All selected images cleared');
  }

  /**
   * Reset service state (called when switching workflows)
   */
  reset(): void {
    this.logger.info('🔄 Resetting UI Design Selection Service');
    this.clearSelection();
    this.nodesCreated$.next(false);
    this.showCanvasTooltip$.next(false);
    this.selectedImages$.next([]);
    this.logger.info('✅ Service reset complete');
  }

  /**
   * Get selection state summary for debugging (enhanced with multi-selection)
   */
  getSelectionState(): {
    hasSelection: boolean;
    selectedNodeId: string | null;
    selectedFileName: string | null;
    selectedNodesCount: number;
    selectedNodeIds: string[];
    selectedFileNames: string[];
    isMultiSelectMode: boolean;
    lastSelectedNodeId: string | null;
    isPromptBarEnabled: boolean;
    isEditingInProgress: boolean;
  } {
    const selectedNode = this.selectedNode$.value;
    const selectedNodes = this.selectedNodes$.value;

    return {
      hasSelection: selectedNodes.length > 0,
      selectedNodeId: selectedNode?.nodeId || null,
      selectedFileName: selectedNode?.fileName || null,
      selectedNodesCount: selectedNodes.length,
      selectedNodeIds: selectedNodes.map(node => node.nodeId),
      selectedFileNames: selectedNodes.map(node => node.fileName),
      isMultiSelectMode: this.isMultiSelectMode$.value,
      lastSelectedNodeId: this.lastSelectedNodeId$.value,
      isPromptBarEnabled: this.isPromptBarEnabled$.value,
      isEditingInProgress: this.isEditingInProgress$.value
    };
  }
}
