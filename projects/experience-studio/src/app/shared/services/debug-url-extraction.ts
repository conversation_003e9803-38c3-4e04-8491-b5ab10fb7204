/**
 * Debug script to test URL extraction with the exact user response
 * This helps verify the implementation works correctly
 */

// Simple debug function that can be run in browser console
export function debugUrlExtraction() {
  // Debug test for URL extraction

  // Your exact response structure
  const userResponse = {
    status: "COMPLETED" as const,
    log: "Agent : Deployment SUCCESSFUL ",
    progress: "DEPLOY",
    progress_description: "Wohoo! The deployment is completed. Please check the preview screen for the output.",
    history: [],
    metadata: [
      {
        type: "ref_code" as const,
        data: "https://mlo-a774-5c9a.netlify.app"
      }
    ]
  };

  // Test URL validation
  let urlValidationResult = false;
  try {
    const testUrl = "https://mlo-a774-5c9a.netlify.app";
    const urlObject = new URL(testUrl);
    const isValidProtocol = urlObject.protocol === 'http:' || urlObject.protocol === 'https:';

    urlValidationResult = isValidProtocol && !!urlObject.hostname;
  } catch (error) {
    urlValidationResult = false;
  }

  // Test metadata extraction
  const metadata = userResponse.metadata;
  const refCodeMetadata = metadata.find(m => m.type === 'ref_code');

  const metadataValid = !!(refCodeMetadata && typeof refCodeMetadata.data === 'string');

  // Test state validation
  const isCorrectProgress = userResponse.progress === 'DEPLOY';
  const isCorrectStatus = userResponse.status === 'COMPLETED';
  const stateValid = isCorrectProgress && isCorrectStatus;

  return {
    response: userResponse,
    expectedUrl: "https://mlo-a774-5c9a.netlify.app",
    validationResults: {
      urlValid: urlValidationResult,
      metadataValid: metadataValid,
      stateValid: stateValid
    }
  };
}

// Export for use in browser console
(window as any).debugUrlExtraction = debugUrlExtraction;
