.breadcrumb-container {
  padding: 0 18px; 
  margin-top: 62px;
  margin-bottom: 20px;
  font-size: 14px;
  color: var(--text-secondary);
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  font-weight: 400;
}

.breadcrumb-label {
  color: var(--text-secondary);
  transition: color 0.2s ease;
}

.breadcrumb-link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
  
  &:hover {
    color: var(--dashboard-primary);
    text-decoration: underline;
  }
}

.breadcrumb-item.active .breadcrumb-label {
  color: var(--text-color);
  font-weight: 500;
}

.separator {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  margin: 0 8px;
  
  svg {
    color: var(--text-secondary);
  }
}

@media (max-width: 1200px) {
  .breadcrumb-container {
    padding: 0 36px;
  }
}

@media (max-width: 768px) {
  .breadcrumb-container {
    padding: 0 24px;
  }
}

@media (max-width: 480px) {
  .breadcrumb-container {
    padding: 0 16px;
  }
} 