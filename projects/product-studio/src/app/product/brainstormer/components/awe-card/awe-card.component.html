<div
  class="awe-card-wrapper {{ cardClass }}"
  [class.with-header-padding]="applyHeaderPadding"
  [class.with-body-padding]="applyBodyPadding"
  [class.with-footer-padding]="applyFooterPadding"
>
  <!-- HEADER - Fully Projectable -->
  <div *ngIf="showHeader" class="awe-card-header">
    <ng-content select="[awe-card-header-content]"></ng-content> <!-- Single slot for all header content -->
  </div>

  <!-- BODY -->
  <div *ngIf="showBody" class="awe-card-body">
    <ng-content></ng-content> <!-- Default slot for main body content -->
  </div>

  <!-- FOOTER -->
  <div *ngIf="showFooter" class="awe-card-footer">
    <ng-content select="[awe-card-footer-content]"></ng-content>
  </div>
</div>