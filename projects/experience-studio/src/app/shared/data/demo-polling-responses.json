{"demoDescription": "This file contains mock polling responses that demonstrate how the new polling system integrates with the code-window component. Each response shows different states and how the UI should react.", "responses": {"step1_project_overview_in_progress": {"progress": "project-overview", "status": "IN_PROGRESS", "log": "Analyzing project requirements and initializing workspace...", "progress_description": "Setting up project overview and analyzing requirements", "history": [], "metadata": [], "expectedUIBehavior": {"artifactsTab": "Should be enabled", "logsTab": "Should show new log entry", "previewTab": "Should show loading animation", "codeTab": "Should remain disabled"}}, "step2_project_overview_completed": {"progress": "project-overview", "status": "COMPLETED", "log": "Project overview completed successfully.", "progress_description": "Project overview completed", "history": [{"progress": "project-overview", "status": "IN_PROGRESS", "log": "Analyzing project requirements...", "progress_description": "Setting up project overview", "metadata": []}], "metadata": [{"type": "artifact", "data": {"type": "text", "value": "# Project Overview\n\nThis is a modern web application built with Angular and TypeScript. The application features:\n\n## Key Features\n- Responsive design\n- Modern UI components\n- Real-time data updates\n- User authentication\n- Dashboard analytics\n\n## Technology Stack\n- Frontend: Angular 17+\n- Styling: SCSS with CSS Grid\n- State Management: RxJS\n- Build Tool: Angular CLI\n\n## Architecture\nThe application follows a modular architecture with feature-based organization."}}], "expectedUIBehavior": {"artifactsTab": "Should show Project Overview content", "logsTab": "Should show completion log", "previewTab": "Should continue loading", "codeTab": "Should remain disabled"}}, "step3_layout_analyzed_in_progress": {"progress": "Layout Analyzed", "status": "IN_PROGRESS", "log": "Analyzing UI layout and component structure...", "progress_description": "Analyzing layout structure and identifying components", "history": [{"progress": "project-overview", "status": "COMPLETED", "log": "Project overview completed.", "progress_description": "Project overview completed", "metadata": []}], "metadata": [], "expectedUIBehavior": {"artifactsTab": "Should maintain Project Overview", "logsTab": "Should show layout analysis log", "previewTab": "Should show layout examples", "codeTab": "Should remain disabled"}}, "step4_layout_analyzed_completed": {"progress": "Layout Analyzed", "status": "COMPLETED", "log": "Layout analysis completed successfully.", "progress_description": "Layout analysis completed", "history": [{"progress": "project-overview", "status": "COMPLETED", "log": "Project overview completed.", "progress_description": "Project overview completed", "metadata": []}, {"progress": "Layout Analyzed", "status": "IN_PROGRESS", "log": "Analyzing UI layout...", "progress_description": "Analyzing layout structure", "metadata": []}], "metadata": [{"type": "artifact", "data": {"type": "string", "value": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="}}], "expectedUIBehavior": {"artifactsTab": "Should add Layout Analyzed artifact", "logsTab": "Should show completion log", "previewTab": "Should show identified layouts", "codeTab": "Should remain disabled"}}, "step5_design_system_in_progress": {"progress": "Design_System Analyzed", "status": "IN_PROGRESS", "log": "Extracting design tokens and analyzing design system...", "progress_description": "Analyzing design system and extracting tokens", "history": [{"progress": "project-overview", "status": "COMPLETED", "log": "Project overview completed.", "progress_description": "Project overview completed", "metadata": []}, {"progress": "Layout Analyzed", "status": "COMPLETED", "log": "Layout analysis completed.", "progress_description": "Layout analysis completed", "metadata": []}], "metadata": [], "expectedUIBehavior": {"artifactsTab": "Should maintain existing artifacts", "logsTab": "Should show design system analysis log", "previewTab": "Should continue showing layouts", "codeTab": "Should remain disabled"}}, "step6_design_system_completed": {"progress": "Design_System Analyzed", "status": "COMPLETED", "log": "Design system analysis completed successfully.", "progress_description": "Design system analysis completed", "history": [{"progress": "project-overview", "status": "COMPLETED", "log": "Project overview completed.", "progress_description": "Project overview completed", "metadata": []}, {"progress": "Layout Analyzed", "status": "COMPLETED", "log": "Layout analysis completed.", "progress_description": "Layout analysis completed", "metadata": []}, {"progress": "Design_System Analyzed", "status": "IN_PROGRESS", "log": "Extracting design tokens...", "progress_description": "Analyzing design system", "metadata": []}], "metadata": [{"type": "artifact", "data": {"type": "json", "value": {"colors": [{"value": "#007bff", "name": "Primary Blue", "type": "primary", "enabled": true}, {"value": "#6c757d", "name": "Secondary Gray", "type": "secondary", "enabled": true}, {"value": "#28a745", "name": "Success Green", "type": "success", "enabled": true}, {"value": "#dc3545", "name": "Danger Red", "type": "danger", "enabled": true}, {"value": "#ffc107", "name": "Warning Yellow", "type": "warning", "enabled": true}, {"value": "#17a2b8", "name": "<PERSON><PERSON><PERSON>", "type": "info", "enabled": true}], "fonts": [{"name": "Heading Large", "size": "32px", "weight": "700", "lineHeight": "1.2"}, {"name": "Heading Medium", "size": "24px", "weight": "600", "lineHeight": "1.3"}, {"name": "Body Text", "size": "16px", "weight": "400", "lineHeight": "1.5"}, {"name": "Caption", "size": "14px", "weight": "400", "lineHeight": "1.4"}], "buttons": [{"name": "Primary Button", "variant": "primary", "size": "medium"}, {"name": "Secondary Button", "variant": "secondary", "size": "medium"}, {"name": "Small Button", "variant": "primary", "size": "small"}]}}}], "expectedUIBehavior": {"artifactsTab": "Should add Design System artifact with tokens", "logsTab": "Should show completion log", "previewTab": "Should continue showing layouts", "codeTab": "Should remain disabled"}}, "step7_deployed_in_progress": {"progress": "deployed", "status": "IN_PROGRESS", "log": "Building and deploying application to preview environment...", "progress_description": "Deploying application for preview", "history": [{"progress": "project-overview", "status": "COMPLETED", "log": "Project overview completed.", "progress_description": "Project overview completed", "metadata": []}, {"progress": "Layout Analyzed", "status": "COMPLETED", "log": "Layout analysis completed.", "progress_description": "Layout analysis completed", "metadata": []}, {"progress": "Design_System Analyzed", "status": "COMPLETED", "log": "Design system analysis completed.", "progress_description": "Design system analysis completed", "metadata": []}], "metadata": [], "expectedUIBehavior": {"artifactsTab": "Should maintain all artifacts", "logsTab": "Should show deployment log", "previewTab": "Should show loading for deployment", "codeTab": "Should remain disabled"}}, "step8_deployed_completed": {"progress": "deployed", "status": "COMPLETED", "log": "Application deployed successfully to preview environment.", "progress_description": "Application deployment completed", "history": [{"progress": "project-overview", "status": "COMPLETED", "log": "Project overview completed.", "progress_description": "Project overview completed", "metadata": []}, {"progress": "Layout Analyzed", "status": "COMPLETED", "log": "Layout analysis completed.", "progress_description": "Layout analysis completed", "metadata": []}, {"progress": "Design_System Analyzed", "status": "COMPLETED", "log": "Design system analysis completed.", "progress_description": "Design system analysis completed", "metadata": []}, {"progress": "deployed", "status": "IN_PROGRESS", "log": "Building and deploying application...", "progress_description": "Deploying application", "metadata": []}], "metadata": [{"type": "ref_code", "data": "https://preview.example.com/app-12345"}, {"type": "files", "data": [{"path": "src/app/app.component.ts", "code": "import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  template: '<h1>Hello World</h1>',\n  styles: ['h1 { color: #007bff; }']\n})\nexport class AppComponent {}"}, {"path": "src/app/app.module.ts", "code": "import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { AppComponent } from './app.component';\n\n@NgModule({\n  declarations: [AppComponent],\n  imports: [BrowserModule],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule {}"}]}], "expectedUIBehavior": {"artifactsTab": "Should maintain all artifacts", "logsTab": "Should show deployment completion log", "previewTab": "Should show live preview iframe", "codeTab": "Should be enabled with generated code files"}}}, "uiStateTransitions": {"description": "Expected UI state changes throughout the polling sequence", "transitions": [{"step": 1, "trigger": "project-overview IN_PROGRESS", "changes": ["Enable Artifacts tab", "Add log entry", "Show loading in preview"]}, {"step": 2, "trigger": "project-overview COMPLETED", "changes": ["Add Project Overview to artifacts", "Update log", "Continue loading"]}, {"step": 3, "trigger": "Layout Analyzed IN_PROGRESS", "changes": ["Add layout analysis log", "Show layout examples in preview"]}, {"step": 4, "trigger": "Layout Analyzed COMPLETED", "changes": ["Add Layout Analyzed artifact", "Update preview with identified layouts"]}, {"step": 5, "trigger": "Design_System Analyzed IN_PROGRESS", "changes": ["Add design system analysis log"]}, {"step": 6, "trigger": "Design_System Analyzed COMPLETED", "changes": ["Add Design System artifact with tokens", "Show design tokens in artifacts"]}, {"step": 7, "trigger": "deployed IN_PROGRESS", "changes": ["Add deployment log", "Show deployment loading"]}, {"step": 8, "trigger": "deployed COMPLETED", "changes": ["Enable Code tab", "Show live preview", "Add code files", "Complete all tabs"]}]}}