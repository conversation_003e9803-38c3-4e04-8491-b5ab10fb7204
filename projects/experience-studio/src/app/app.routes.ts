import { Routes } from '@angular/router';
import { PromptSubmissionGuard } from './shared/guards/prompt-submission.guard';
import { CardSelectionGuard } from './shared/guards/card-selection.guard';

export const routes: Routes = [
  {
    path: 'experience',
    children: [
      {
        path: '',
        loadChildren: () => import('./pages/experience-routing').then(m => m.EXPERIENCE_ROUTES),
        data: { preload: true } 
      },
      // Legacy route - keep for backward compatibility
      {
        path: 'prompt',
        loadComponent: () =>
          import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
            m => m.PromptContentComponent
          ),
      },
      // Legacy route - keep for backward compatibility
      {
        path: 'code-preview',
        loadComponent: () =>
          import('./shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
        canActivate: [PromptSubmissionGuard],
      },
      // New routes for Generate Application
      {
        path: 'generate-application',
        children: [
          {
            path: 'prompt',
            loadComponent: () =>
              import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
                m => m.PromptContentComponent
              ),
            canActivate: [CardSelectionGuard],
            data: { cardType: 'Generate Application' }
          },
          {
            path: 'code-preview',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
            canActivate: [PromptSubmissionGuard],
            data: { cardType: 'Generate Application' }
          }
        ]
      },
      // New routes for Generate UI Design
      {
        path: 'generate-ui-design',
        children: [
          {
            path: 'prompt',
            loadComponent: () =>
              import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
                m => m.PromptContentComponent
              ),
            canActivate: [CardSelectionGuard],
            data: { cardType: 'Generate UI Design' }
          },
          {
            path: 'code-preview',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
            canActivate: [PromptSubmissionGuard],
            data: { cardType: 'Generate UI Design' }
          }
        ]
      }
    ]
  },
  {
    path: '',
    redirectTo: 'experience',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: 'experience',
  },
];
