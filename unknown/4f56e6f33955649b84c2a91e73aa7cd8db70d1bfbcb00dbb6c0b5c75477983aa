<div class="create-prompts-container">
  <form [formGroup]="promptForm">
    <div class="form-layout" [ngClass]="{'three-column-layout': isExecuteMode && showChatInterface}">
      <!-- Left Column -->
      <div class="left-column">
        <!-- Prompt Details Card -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <app-form-field
              label="Prompt Name"
              id="promptName"
              [control]="getControl('name')"
            ></app-form-field>

            <app-form-field
              label="Description"
              id="description"
              [control]="getControl('description')"
              type="textarea"
            ></app-form-field>
          </div>
        </app-card>

        <!-- Assign Filters Card -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <h3 class="section-title">Assign Filters</h3>

            <app-form-field
              label="Organization"
              id="organization"
              [control]="getControl('organization')"
            ></app-form-field>

            <app-form-field
              label="Domain"
              id="domain"
              [control]="getControl('domain')"
            ></app-form-field>

            <app-form-field
              label="Project"
              id="project"
              [control]="getControl('project')"
            ></app-form-field>

            <app-form-field
              label="Team"
              id="team"
              [control]="getControl('team')"
            ></app-form-field>
          </div>
        </app-card>
      </div>

      <!-- Middle Column (previously Right) -->
      <div class="middle-column">
        <!-- What do you want the prompt to do section -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content agent-task-container">
            <h3 class="section-title">What do you want the Prompt to do?</h3>

            <div class="input-with-actions">
              <div class="input-analyze-row">
                <app-form-field
                  placeholder="Please describe what you want the prompt to do..."
                  [control]="getControl('promptTask')"
                  type="textarea"
                  [showAttachIcon]="true"
                  attachIconTooltip="Attach a file or document"
                  (attachClick)="handleAttachment()"
                  class="workflow-textarea"
                ></app-form-field>

                <button
                  type="button"
                  class="analyze-button"
                  (click)="analyzePrompt()"
                >
                  <svg
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                  </svg>
                  Analyze
                </button>
              </div>
            </div>
          </div>
        </app-card>

        <!-- Prompt Configuration Card -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <div class="fields-row">
              <div class="field-col">
                <app-form-field
                  label="Role"
                  id="role"
                  [control]="getControl('role')"
                ></app-form-field>
              </div>

              <div class="field-col">
                <app-form-field
                  label="Goal"
                  id="goal"
                  [control]="getControl('goal')"
                ></app-form-field>
              </div>
            </div>

            <app-form-field
              label="Backstory"
              id="backstory"
              [control]="getControl('backstory')"
              type="textarea"
            ></app-form-field>

            <app-form-field
              label="Expected Output"
              id="expectedOutput"
              [control]="getControl('expectedOutput')"
              type="textarea"
            ></app-form-field>
          </div>
        </app-card>

        <!-- Optional Sections -->
        <div class="optional-sections">
          <!-- Examples Section -->
          <div class="accordion-section">
            <app-accordion>
              <app-accordion-item
                title="Add Examples (Optional)"
                [isOpen]="false"
              >
                <div class="example-content">
                  <app-form-field
                    label="Input"
                    id="example1Input"
                    [control]="getControl('example1Input')"
                    type="textarea"
                  ></app-form-field>

                  <app-form-field
                    label="Output"
                    id="example1Output"
                    [control]="getControl('example1Output')"
                    type="textarea"
                  ></app-form-field>
                </div>
              </app-accordion-item>
            </app-accordion>
          </div>

          <!-- Intermediate Steps Section -->
          <div class="accordion-section">
            <app-accordion>
              <app-accordion-item
                title="Add intermediate steps (Optional)"
                [isOpen]="false"
              >
                <div class="step-content">
                  <app-form-field
                    label="Instruction"
                    id="step1Instruction"
                    [control]="getControl('step1Instruction')"
                    type="textarea"
                  ></app-form-field>
                </div>
              </app-accordion-item>
            </app-accordion>
          </div>
        </div>

        <!-- Buttons at bottom of middle column -->
        <div class="middle-column-buttons">
          <button type="button" class="exit-button" (click)="onExit()">
            Exit
          </button>
          <!-- Show different buttons based on mode -->
          <ng-container *ngIf="!isEditMode">
            <button type="button" class="save-button" (click)="onSave()">
              Save
            </button>
          </ng-container>
          <ng-container *ngIf="isEditMode && !isExecuteMode">
            <button type="button" class="execute-button" (click)="onExecute()">
              Execute
            </button>
          </ng-container>
          <ng-container *ngIf="isEditMode && isExecuteMode">
            <button type="button" class="execute-button" (click)="onSave()">
              Update
            </button>
          </ng-container>
        </div>
      </div>

      <!-- Right Column (new) - Chat Interface -->
      <div class="chat-column" *ngIf="isExecuteMode && showChatInterface">
        <div class="chat-column-content">
          <app-card
            customClass="solid-card"
            [noPadding]="false"
            [isClickable]="false"
            [noHoverEffect]="true"
          >
            <div class="card-content">
              <h3 class="section-title">Playground</h3>
              <h4 class="playground-title">Prompt testing</h4>
              
              <!-- Chat Interface -->
              <div class="chat-container">
                <app-chat-interface
                  [messages]="chatMessages"
                  [isLoading]="isProcessingChat"
                  (messageSent)="handleChatMessage($event)">
                </app-chat-interface>
              </div>
            </div>
          </app-card>
        </div>
      </div>
    </div>
  </form>
</div>