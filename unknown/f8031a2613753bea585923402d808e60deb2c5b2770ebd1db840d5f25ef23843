import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PromptSubmissionService {
  private hasSubmittedPromptSubject = new BehaviorSubject<boolean>(false);
  public hasSubmittedPrompt$: Observable<boolean> = this.hasSubmittedPromptSubject.asObservable();

  constructor() {}

  /**
   * Mark that a prompt has been submitted
   */
  setPromptSubmitted(submitted: boolean = true): void {
    this.hasSubmittedPromptSubject.next(submitted);
  }

  /**
   * Check if a prompt has been submitted
   */
  hasPromptBeenSubmitted(): boolean {
    return this.hasSubmittedPromptSubject.getValue();
  }

  /**
   * Reset the submission state (e.g., when navigating away from the code-preview)
   */
  resetSubmissionState(): void {
    this.hasSubmittedPromptSubject.next(false);
  }
}
