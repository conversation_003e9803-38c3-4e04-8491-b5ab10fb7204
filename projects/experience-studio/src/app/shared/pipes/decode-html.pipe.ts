import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'decodeHtml',
  standalone: true
})
export class DecodeHtmlPipe implements PipeTransform {
  
  transform(value: string): string {
    if (!value) return '';
    
    // Create a temporary DOM element to decode HTML entities
    const textarea = document.createElement('textarea');
    textarea.innerHTML = value;
    let decodedContent = textarea.value;
    
    // Additional manual decoding for common entities that might not be handled
    decodedContent = decodedContent
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, '/')
      .replace(/&#10;/g, '\n')
      .replace(/&nbsp;/g, ' ');
    
    return decodedContent;
  }
}
