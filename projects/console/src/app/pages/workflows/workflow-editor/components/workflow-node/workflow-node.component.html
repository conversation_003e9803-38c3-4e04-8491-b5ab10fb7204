<div
  class="workflow-node"
  [class.selected]="selected"
  [attr.aria-label]="data.label + ' node'"
>
  <div class="node-header">
    <span class="node-title">{{ data.label }}</span>
    <button
      class="delete-btn"
      (click)="onDelete($event)"
      aria-label="Delete node"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        aria-hidden="true"
        focusable="false"
      >
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    </button>
  </div>
  <div class="node-body">
    <p *ngIf="data.description" class="node-description">
      {{ data.description }}
    </p>
  </div>
  <div class="node-handles">
    <div
      class="handle handle-left"
      role="button"
      aria-label="Connect from left"
    ></div>
    <div
      class="handle handle-right"
      role="button"
      aria-label="Connect from right"
    ></div>
  </div>
</div>
