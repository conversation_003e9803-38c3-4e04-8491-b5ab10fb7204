<div class="collaborative-agent-container">
  <!-- SVG Gradient Definition for icon strokes -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#6566CD" />
        <stop offset="100%" stop-color="#F96CAB" />
      </linearGradient>
    </defs>
  </svg>

  <form [formGroup]="agentForm" (ngSubmit)="onSubmit()" class="form-layout" [ngClass]="{'three-column-layout': isExecuteMode && showChatInterface}">
    <!-- Left Column -->
    <div class="left-column">
      <app-card>
        <div class="card-content">
          <app-form-field
            label="Name"
            placeholder="Enter agent name"
            [control]="nameControl"
          >
          </app-form-field>
          <app-form-field
            label="Description"
            placeholder="Enter agent description"
            [control]="descriptionControl"
            type="textarea"
          >
          </app-form-field>
        </div>
      </app-card>

      <app-card>
        <div class="card-content">
          <h3 class="section-title">Assign Filters</h3>
          <app-form-field
            label="Organization"
            placeholder="Select organization"
            [control]="organizationControl"
          >
          </app-form-field>

          <app-form-field
            label="Domain"
            placeholder="Select domain"
            [control]="domainControl"
          >
          </app-form-field>

          <app-form-field
            label="Project"
            placeholder="Select project"
            [control]="projectControl"
          >
          </app-form-field>

          <app-form-field
            label="Team"
            placeholder="Select team"
            [control]="teamControl"
          >
          </app-form-field>
        </div>
      </app-card>
    </div>

    <!-- Middle Column (previously Right) -->
    <div class="middle-column">
      <app-card>
        <div class="card-content agent-task-container">
          <h3 class="section-title">What do you want the Agent to do?</h3>

          <div class="input-with-actions">
            <div class="input-analyze-row">
              <div class="input-with-icon">
                <app-form-field
                  placeholder="Please describe what you want the agent to do..."
                  [control]="workflowControl"
                  type="text"
                  [showAttachIcon]="true"
                  attachIconTooltip="Attach a file or document"
                  (attachClick)="handleAttachment()"
                  class="workflow-input"
                >
                </app-form-field>
              </div>

              <button
                type="button"
                class="analyze-button"
                (click)="analyzeWorkflow()"
              >
                <span class="sparkle-emoji">✨</span>
                Analyze
              </button>
            </div>
          </div>
        </div>
      </app-card>

      <app-card *ngIf="!showSuggestedPrompt">
        <div class="card-content template-content">
          <div class="instruction-container">
            <p class="instruction-text">
              You can use the "Agent Description" section to describe the
              agent's role, goal, and expected output to automatically create
              agents.
            </p>
            <p class="instruction-text center-text">or</p>
            <div class="template-card" (click)="showTemplateOptions()">
              <h4 class="template-title">Start with a Template</h4>
            </div>
          </div>
        </div>
      </app-card>

      <!-- Suggested Prompt Card (shown after analysis) -->
      <app-card *ngIf="showSuggestedPrompt">
        <div class="card-content">
          <h3 class="section-title">Suggested Prompt</h3>

          <!-- Prompt Selection Row -->
          <div class="prompt-selection-row">
            <div class="select-container">
              <app-select-dropdown
                [options]="promptOptions"
                placeholder="Choose Prompt"
                [isMultiSelect]="false"
                (selectionChange)="onPromptChange($event)"
              ></app-select-dropdown>
            </div>

            <button class="edit-button" (click)="editPrompt()">
              <svg
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 20h9M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke="url(#gradient)"
                />
              </svg>
              <span>Edit</span>
            </button>
          </div>

          <!-- Role, Goal, and Description - Three columns -->
          <div class="three-column-row">
            <div class="column">
              <div class="field-container">
                <h4 class="field-title">Role</h4>
                <div class="field-content">
                  <app-form-field
                    [control]="roleControl"
                    placeholder="Role"
                    type="text"
                    class="short-field"
                  >
                  </app-form-field>
                </div>
              </div>
            </div>

            <div class="column">
              <div class="field-container">
                <h4 class="field-title">Goal</h4>
                <div class="field-content">
                  <app-form-field
                    [control]="goalControl"
                    placeholder="Goal"
                    type="text"
                    class="short-field"
                  >
                  </app-form-field>
                </div>
              </div>
            </div>

            <div class="column">
              <div class="field-container">
                <h4 class="field-title">Description</h4>
                <div class="field-content">
                  <app-form-field
                    [control]="descriptionPromptControl"
                    placeholder="Description"
                    type="text"
                    class="short-field"
                  >
                  </app-form-field>
                </div>
              </div>
            </div>
          </div>

          <!-- Backstory and Expected Output - Two columns -->
          <div class="two-column-row">
            <div class="column">
              <div class="field-container">
                <div class="field-header">
                  <h4 class="field-title">Backstory</h4>
                </div>
                <div class="field-content">
                  <app-form-field
                    [control]="backstoryControl"
                    placeholder="Backstory"
                    type="textarea"
                    class="tall-field"
                  >
                  </app-form-field>
                  <button class="enhance-button" (click)="enhanceBackstory()">
                    <svg
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 5V19M5 12H19"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke="url(#gradient)"
                      />
                    </svg>
                    <span>Enhance</span>
                  </button>
                </div>
              </div>
            </div>

            <div class="column">
              <div class="field-container">
                <div class="field-header">
                  <h4 class="field-title">Expected Output</h4>
                </div>
                <div class="field-content">
                  <app-form-field
                    [control]="expectedOutputControl"
                    placeholder="Expected Output"
                    type="textarea"
                    class="tall-field"
                  >
                  </app-form-field>
                  <button class="enhance-button" (click)="enhanceOutput()">
                    <svg
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 5V19M5 12H19"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke="url(#gradient)"
                      />
                    </svg>
                    <span>Enhance</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </app-card>

      <!-- Suggested Model Card - Separate Card -->
      <app-card *ngIf="showSuggestedPrompt">
        <div class="card-content">
          <h3 class="section-title">Suggested Model</h3>

          <!-- Model Selection Row -->
          <div class="prompt-selection-row">
            <div class="select-container">
              <app-select-dropdown
                [options]="modelOptions"
                placeholder="Choose Model"
                [isMultiSelect]="false"
                (selectionChange)="onModelChange($event)"
              ></app-select-dropdown>
            </div>

            <button
              class="edit-button"
              (click)="editModel()"
              *ngIf="selectedModel && selectedModel !== 'default'"
            >
              <svg
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 20h9M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke="url(#gradient)"
                />
              </svg>
              <span>Edit</span>
            </button>
          </div>

          <!-- Empty state -->
          <div class="empty-tools-state" *ngIf="!selectedModel">
            <p>No model selected. Please choose from the dropdown above.</p>
          </div>

          <!-- Model Configuration Parameters -->
          <div
            *ngIf="selectedModel && selectedModel !== 'default'"
            class="model-config-section"
          >
            <!-- Row 1: Temperature and Max Tokens -->
            <div class="config-row">
              <!-- Temperature Column -->
              <div class="config-column">
                <div class="config-label">Temperature</div>
                <div class="split-size-container">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    [value]="temperature"
                    (input)="onTemperatureChange($event)"
                    class="split-size-slider"
                  />
                  <input
                    type="text"
                    [value]="temperature"
                    class="split-size-input"
                    (input)="onTemperatureChange($event)"
                  />
                </div>
              </div>

              <!-- Max Token Column -->
              <div class="config-column">
                <div class="config-label">
                  Max Token
                  <span class="tokens-used"
                    >{{ maxTokenControl.value }}/4096 Tokens used</span
                  >
                </div>
                <app-form-field
                  [control]="maxTokenControl"
                  type="text"
                  placeholder="Max Tokens"
                >
                </app-form-field>
              </div>
            </div>

            <!-- Row 2: Top P and Max Iteration -->
            <div class="config-row">
              <!-- Top P Column -->
              <div class="config-column">
                <div class="config-label">Top P</div>
                <app-form-field
                  [control]="topPControl"
                  type="text"
                  placeholder="Top P"
                >
                </app-form-field>
              </div>

              <!-- Max Iteration Column -->
              <div class="config-column">
                <div class="config-label">Max Iteration</div>
                <app-form-field
                  [control]="maxIterationControl"
                  type="text"
                  placeholder="Max Iteration"
                >
                </app-form-field>
              </div>
            </div>

            <!-- Row 3: Max RPM and Max Execution Time -->
            <div class="config-row">
              <!-- Max RPM Column -->
              <div class="config-column">
                <div class="config-label">Max RPM</div>
                <app-form-field
                  [control]="maxRpmControl"
                  type="text"
                  placeholder="Max RPM"
                >
                </app-form-field>
              </div>

              <!-- Max Execution Time Column -->
              <div class="config-column">
                <div class="config-label">Max Execution Time</div>
                <app-form-field
                  [control]="maxExecutionTimeControl"
                  type="text"
                  placeholder="Max Execution Time"
                >
                </app-form-field>
              </div>
            </div>
          </div>
        </div>
      </app-card>

      <!-- Assign Knowledge base Card - Separate Card -->
      <app-card *ngIf="showSuggestedPrompt">
        <div class="card-content">
          <h3 class="section-title">Assign Knowledge base</h3>

          <!-- Knowledge Base Selection Row -->
          <div class="prompt-selection-row">
            <div class="select-container">
              <app-select-dropdown
                [options]="knowledgeBaseOptions"
                placeholder="Choose Knowledgebase"
                [isMultiSelect]="false"
                (selectionChange)="onKnowledgeBaseChange($event)"
              ></app-select-dropdown>
            </div>

            <button
              class="edit-button"
              type="button"
              (click)="addNewKnowledgeBase()"
            >
              <svg
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 5V19M5 12H19"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke="url(#gradient)"
                />
              </svg>
              <span>Add New</span>
            </button>
          </div>

          <!-- Empty state -->
          <div class="empty-tools-state" *ngIf="!selectedKnowledgeBase">
            <p>
              No knowledge base selected. Please choose from the dropdown above.
            </p>
          </div>

          <!-- Knowledge Base Configuration (only shows when a knowledge base is selected) -->
          <div
            *ngIf="selectedKnowledgeBase"
            class="knowledge-base-config-section"
          >
            <!-- Retriever Selection -->
            <div class="section">
              <h3 class="section-title">Select Retriever</h3>
              <div class="retriever-options">
                <button
                  *ngFor="let retriever of retrieverOptions"
                  class="retriever-button"
                  [class.selected]="selectedRetriever === retriever"
                  (click)="selectRetriever(retriever)"
                >
                  {{ retriever }}
                </button>
              </div>
            </div>

            <!-- Split Size Configuration for Default Retriever -->
            <div class="section" *ngIf="selectedRetriever === 'Default'">
              <h3 class="section-title">Split Size</h3>
              <div class="split-size-container">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  [value]="splitSize"
                  (input)="onSplitSizeChange($event)"
                  class="split-size-slider"
                />
                <input
                  type="text"
                  [value]="splitSize"
                  class="split-size-input"
                  (input)="onSplitSizeChange($event)"
                />
              </div>
            </div>

            <!-- Parent Doc Split Size Configuration -->
            <div class="section" *ngIf="selectedRetriever === 'Parent Doc'">
              <div class="split-section">
                <h3 class="section-title">Parent Split Size</h3>
                <div class="split-size-container">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    [value]="parentSplitSize"
                    (input)="onParentSplitSizeChange($event)"
                    class="split-size-slider"
                  />
                  <input
                    type="text"
                    [value]="parentSplitSize"
                    class="split-size-input"
                    (input)="onParentSplitSizeChange($event)"
                  />
                </div>
              </div>

              <div class="split-section">
                <h3 class="section-title">Child Split Size</h3>
                <div class="split-size-container">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    [value]="childSplitSize"
                    (input)="onChildSplitSizeChange($event)"
                    class="split-size-slider"
                  />
                  <input
                    type="text"
                    [value]="childSplitSize"
                    class="split-size-input"
                    (input)="onChildSplitSizeChange($event)"
                  />
                </div>
              </div>
            </div>

            <!-- File Upload Section -->
            <div class="section">
              <h3 class="section-title">Uploaded Knowledgebase</h3>

              <!-- File Table -->
              <div class="file-table-container">
                <table class="file-table">
                  <thead>
                    <tr>
                      <th>File Name</th>
                      <th>File Size</th>
                      <th>Upload Date</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let file of uploadedFiles">
                      <td>
                        <div class="file-info">
                          <svg
                            *ngIf="file.type === 'docx'"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="#2B579A"
                          >
                            <path
                              d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
                            />
                          </svg>
                          <svg
                            *ngIf="file.type === 'pdf'"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="#FF0000"
                          >
                            <path
                              d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
                            />
                          </svg>
                          <svg
                            *ngIf="file.type === 'pptx'"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="#D24726"
                          >
                            <path
                              d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
                            />
                          </svg>
                          {{ file.name }}
                        </div>
                      </td>
                      <td>{{ file.size }}</td>
                      <td>{{ file.date }}</td>
                      <td>
                        <button
                          class="delete-button"
                          (click)="deleteFile(file)"
                        >
                          <svg
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          >
                            <path d="M3 6h18"></path>
                            <path
                              d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"
                            ></path>
                            <line x1="10" y1="11" x2="10" y2="17"></line>
                            <line x1="14" y1="11" x2="14" y2="17"></line>
                          </svg>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- File Upload Area -->
              <div class="file-upload-area">
                <div class="upload-info">
                  <p class="upload-text">
                    Note: upload only .pdf,.txt,.docx,.pptx,.html,.xlsx file
                  </p>
                </div>
                <div
                  class="drag-drop-area"
                  (dragover)="$event.preventDefault()"
                  (drop)="onFileDrop($event)"
                >
                  <p>Drag and Drop your File(s) Or Upload File(s)</p>
                  <input
                    type="file"
                    id="fileUpload"
                    multiple
                    class="file-input"
                    (change)="onFileSelected($event)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </app-card>

      <!-- Select Tools Card - Separate Card -->
      <app-card *ngIf="showSuggestedPrompt">
        <div class="card-content">
          <h3 class="section-title">Select Tools</h3>

          <!-- Tools Selection Row -->
          <div class="prompt-selection-row">
            <div class="select-container">
              <app-select-dropdown
                [options]="toolOptions"
                placeholder="Choose Tool"
                [isMultiSelect]="true"
                (selectionChange)="onToolSelect($event)"
                dropdownDirection="auto"
              ></app-select-dropdown>
            </div>

            <button class="edit-button" type="button" (click)="addNewTool()">
              <svg
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 5V19M5 12H19"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke="url(#gradient)"
                />
              </svg>
              <span>Add New</span>
            </button>
          </div>

          <!-- Selected Tools Cards -->
          <div
            class="selected-tools-section"
            *ngIf="selectedTools && selectedTools.length > 0"
          >
            <h4 class="sub-section-title">Selected Tools</h4>

            <div class="tools-cards-container">
              <app-data-card
                *ngFor="let tool of selectedTools"
                [data]="convertToolToCardData(tool)"
                (actionClicked)="removeTool($event)"
              >
              </app-data-card>
            </div>
          </div>

          <!-- Empty state -->
          <div
            class="empty-tools-state"
            *ngIf="!selectedTools || selectedTools.length === 0"
          >
            <p>No tools selected. Please choose from the dropdown above.</p>
          </div>
        </div>
      </app-card>

      <!-- Form Actions Card - Separate Card -->
      <div class="form-actions">
        <button type="button" class="exit-button" (click)="onExit()">Exit</button>
        <ng-container *ngIf="!isExecuteMode">
          <button type="button" class="preview-button" (click)="onExecute()">Execute</button>
        </ng-container>
        <ng-container *ngIf="isExecuteMode">
          <button type="submit" class="preview-button">Preview</button>
        </ng-container>
      </div>
    </div>

    <!-- Right Column (new) - Chat Interface -->
    <div class="chat-column" *ngIf="isExecuteMode && showChatInterface">
      <div class="chat-column-content">
        <app-card>
          <div class="card-content">
            <h3 class="section-title">Playground</h3>
            <h4 class="playground-title">Individual Agent Testing</h4>
            
            <!-- Chat Interface -->
            <div class="chat-container">
              <app-chat-interface
                [messages]="chatMessages"
                [isLoading]="isProcessingChat"
                (messageSent)="handleChatMessage($event)">
              </app-chat-interface>
            </div>
          </div>
        </app-card>
      </div>
    </div>
  </form>
</div>
