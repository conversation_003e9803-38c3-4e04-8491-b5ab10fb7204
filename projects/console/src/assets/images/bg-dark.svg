<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Rich deep space background with depth -->
  <rect width="1920" height="1080" fill="#08080E"/>
  
  <!-- Nebula effect layers -->
  <path d="M0 0 H1920 V1080 H0 V0 Z" fill="url(#deepNebulaGradient)" fill-opacity="0.85"/>
  <path d="M0 0 H1920 V1080 H0 V0 Z" fill="url(#cosmicDustGradient)" fill-opacity="0.7"/>
  
  <!-- Dramatic corner glows -->
  <circle cx="-100" cy="-100" r="600" fill="url(#topLeftGlow)" fill-opacity="0.7"/>
  <circle cx="2020" cy="-100" r="550" fill="url(#topRightGlow)" fill-opacity="0.6"/>
  <circle cx="-100" cy="1180" r="500" fill="url(#bottomLeftGlow)" fill-opacity="0.5"/>
  <circle cx="2020" cy="1180" r="600" fill="url(#bottomRightGlow)" fill-opacity="0.6"/>
  
  <!-- Vibrant stellar artifacts -->
  <circle cx="960" cy="540" r="800" fill="url(#centerGalaxyGlow)" fill-opacity="0.15"/>

  <!-- Dramatic light paths -->
  <path d="M0 400C400 450 800 350 1200 500C1600 650 1920 550 1920 550" 
        stroke="url(#lightPath1)" stroke-opacity="0.45" stroke-width="3" stroke-linecap="round"/>
  <path d="M0 600C500 500 1000 650 1500 450C1800 350 1920 400 1920 400" 
        stroke="url(#lightPath2)" stroke-opacity="0.4" stroke-width="2.5" stroke-linecap="round"/>
  
  <!-- Subtle grid overlay with depth effect -->
  <path d="M0 40H1920M0 120H1920M0 200H1920M0 280H1920M0 360H1920M0 440H1920M0 520H1920M0 600H1920M0 680H1920M0 760H1920M0 840H1920M0 920H1920M0 1000H1920M0 1080H1920M40 0V1080M120 0V1080M200 0V1080M280 0V1080M360 0V1080M440 0V1080M520 0V1080M600 0V1080M680 0V1080M760 0V1080M840 0V1080M920 0V1080M1000 0V1080M1080 0V1080M1160 0V1080M1240 0V1080M1320 0V1080M1400 0V1080M1480 0V1080M1560 0V1080M1640 0V1080M1720 0V1080M1800 0V1080M1880 0V1080" 
        stroke="url(#gridGradient)" stroke-opacity="0.1" stroke-width="0.5" stroke-linecap="round"/>
  
  <!-- Secondary grid pattern with staggered effect -->
  <path d="M0 80H1920M0 160H1920M0 240H1920M0 320H1920M0 400H1920M0 480H1920M0 560H1920M0 640H1920M0 720H1920M0 800H1920M0 880H1920M0 960H1920M0 1040H1920" 
        stroke="url(#secondaryGridGradient)" stroke-opacity="0.05" stroke-width="0.3" stroke-dasharray="8 12"/>
  <path d="M80 0V1080M160 0V1080M240 0V1080M320 0V1080M400 0V1080M480 0V1080M560 0V1080M640 0V1080M720 0V1080M800 0V1080M880 0V1080M960 0V1080M1040 0V1080" 
        stroke="url(#secondaryGridGradient)" stroke-opacity="0.05" stroke-width="0.3" stroke-dasharray="8 12"/>
        
  <!-- Dramatic energy waves -->
  <path d="M0 300C200 340 400 270 600 330C800 390 1000 280 1200 340C1400 400 1600 290 1800 350C1920 380 1920 380 1920 380" 
        stroke="url(#energyWave1)" stroke-opacity="0.35" stroke-width="2" stroke-linecap="round"/>
  <path d="M0 750C250 700 450 800 700 750C950 700 1200 820 1450 770C1700 720 1850 820 1920 800" 
        stroke="url(#energyWave2)" stroke-opacity="0.3" stroke-width="2" stroke-linecap="round"/>
  <path d="M0 500C300 450 600 550 900 500C1200 450 1500 550 1800 500C1920 480 1920 480 1920 480" 
        stroke="url(#energyWave3)" stroke-opacity="0.25" stroke-width="1.5" stroke-linecap="round"/>
        
  <!-- Stellar objects with pulse animations -->
  <!-- High-intensity main stars -->
  <circle cx="480" cy="320" r="5" fill="#8B65F7" fill-opacity="0.9">
    <animate attributeName="r" values="5;7;5" dur="6s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.9;1;0.9" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1200" cy="440" r="6" fill="#F96CAB" fill-opacity="0.95">
    <animate attributeName="r" values="6;8;6" dur="8s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.95;1;0.95" dur="5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="840" cy="680" r="6" fill="#6566CD" fill-opacity="0.95">
    <animate attributeName="r" values="6;8;6" dur="7s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.95;1;0.95" dur="5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1400" cy="200" r="5" fill="#E84393" fill-opacity="0.9">
    <animate attributeName="r" values="5;7;5" dur="9s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.9;1;0.9" dur="6s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Medium-intensity stars -->
  <circle cx="360" cy="840" r="4" fill="#A55EEA" fill-opacity="0.85">
    <animate attributeName="r" values="4;5.5;4" dur="7s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.85;1;0.85" dur="5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1600" cy="600" r="5" fill="#0984E3" fill-opacity="0.85">
    <animate attributeName="r" values="5;6.5;5" dur="8s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.85;1;0.85" dur="6s" repeatCount="indefinite"/>
  </circle>
  <circle cx="760" cy="920" r="3.5" fill="#54A0FF" fill-opacity="0.8">
    <animate attributeName="r" values="3.5;5;3.5" dur="10s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.8;1;0.8" dur="7s" repeatCount="indefinite"/>
  </circle>
  <circle cx="120" cy="200" r="4" fill="#FD79A8" fill-opacity="0.85">
    <animate attributeName="r" values="4;5.5;4" dur="9s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.85;1;0.85" dur="6s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Subtle background stars -->
  <circle cx="1800" cy="160" r="3" fill="#FFFFFF" fill-opacity="0.7">
    <animate attributeName="r" values="3;3.8;3" dur="12s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.7;0.9;0.7" dur="8s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1040" cy="960" r="2.5" fill="#FFFFFF" fill-opacity="0.65">
    <animate attributeName="r" values="2.5;3.2;2.5" dur="15s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.65;0.85;0.65" dur="10s" repeatCount="indefinite"/>
  </circle>
  <circle cx="280" cy="560" r="2" fill="#FFFFFF" fill-opacity="0.6">
    <animate attributeName="r" values="2;2.5;2" dur="18s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.6;0.8;0.6" dur="12s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1680" cy="840" r="2.2" fill="#FFFFFF" fill-opacity="0.65">
    <animate attributeName="r" values="2.2;2.8;2.2" dur="16s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.65;0.85;0.65" dur="11s" repeatCount="indefinite"/>
  </circle>
  <circle cx="520" cy="100" r="1.8" fill="#FFFFFF" fill-opacity="0.55">
    <animate attributeName="r" values="1.8;2.3;1.8" dur="20s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.55;0.75;0.55" dur="15s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1520" cy="380" r="1.5" fill="#FFFFFF" fill-opacity="0.5">
    <animate attributeName="r" values="1.5;2;1.5" dur="22s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.5;0.7;0.5" dur="17s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Micro stars (dozens scattered) -->
  <g fill="#FFFFFF">
    <circle cx="150" cy="350" r="1" fill-opacity="0.6"/>
    <circle cx="320" cy="780" r="1.2" fill-opacity="0.65"/>
    <circle cx="480" cy="120" r="0.8" fill-opacity="0.55"/>
    <circle cx="650" cy="480" r="1.1" fill-opacity="0.6"/>
    <circle cx="780" cy="220" r="0.9" fill-opacity="0.57"/>
    <circle cx="950" cy="720" r="1.3" fill-opacity="0.67"/>
    <circle cx="1100" cy="150" r="0.7" fill-opacity="0.52"/>
    <circle cx="1220" cy="620" r="1.2" fill-opacity="0.63"/>
    <circle cx="1350" cy="380" r="0.8" fill-opacity="0.55"/>
    <circle cx="1480" cy="850" r="1.1" fill-opacity="0.6"/>
    <circle cx="1600" cy="230" r="0.9" fill-opacity="0.58"/>
    <circle cx="1720" cy="560" r="1" fill-opacity="0.61"/>
    <circle cx="1850" cy="420" r="0.8" fill-opacity="0.54"/>
    <circle cx="250" cy="620" r="1.2" fill-opacity="0.64"/>
    <circle cx="420" cy="950" r="0.8" fill-opacity="0.57"/>
    <circle cx="580" cy="280" r="1.1" fill-opacity="0.61"/>
    <circle cx="710" cy="530" r="0.7" fill-opacity="0.53"/>
    <circle cx="880" cy="170" r="1.2" fill-opacity="0.66"/>
    <circle cx="1030" cy="750" r="0.8" fill-opacity="0.56"/>
    <circle cx="1150" cy="320" r="1" fill-opacity="0.59"/>
    <circle cx="1290" cy="880" r="0.9" fill-opacity="0.58"/>
    <circle cx="1420" cy="180" r="1.1" fill-opacity="0.62"/>
    <circle cx="1550" cy="650" r="0.8" fill-opacity="0.55"/>
    <circle cx="1680" cy="440" r="1.2" fill-opacity="0.65"/>
    <circle cx="1800" cy="920" r="0.9" fill-opacity="0.57"/>
  </g>
  
  <!-- Star cluster areas - dense star collections -->
  <circle cx="400" cy="250" r="120" fill="url(#starCluster1)" fill-opacity="0.25"/>
  <circle cx="1600" cy="750" r="150" fill="url(#starCluster2)" fill-opacity="0.2"/>
  <circle cx="900" cy="800" r="100" fill="url(#starCluster3)" fill-opacity="0.15"/>
  
  <!-- Interactive cosmic dust particles -->
  <g fill="#FFFFFF" fill-opacity="0.6">
    <circle cx="200" cy="300" r="0.5">
      <animate attributeName="cy" values="300;305;300;295;300" dur="30s" repeatCount="indefinite"/>
      <animate attributeName="fill-opacity" values="0.6;0.8;0.6" dur="15s" repeatCount="indefinite"/>
    </circle>
    <circle cx="500" cy="600" r="0.6">
      <animate attributeName="cy" values="600;610;600;590;600" dur="35s" repeatCount="indefinite"/>
      <animate attributeName="fill-opacity" values="0.6;0.8;0.6" dur="20s" repeatCount="indefinite"/>
    </circle>
    <circle cx="800" cy="400" r="0.4">
      <animate attributeName="cy" values="400;405;400;395;400" dur="40s" repeatCount="indefinite"/>
      <animate attributeName="fill-opacity" values="0.6;0.8;0.6" dur="25s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1100" cy="700" r="0.7">
      <animate attributeName="cy" values="700;710;700;690;700" dur="38s" repeatCount="indefinite"/>
      <animate attributeName="fill-opacity" values="0.6;0.8;0.6" dur="22s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1400" cy="300" r="0.5">
      <animate attributeName="cy" values="300;305;300;295;300" dur="42s" repeatCount="indefinite"/>
      <animate attributeName="fill-opacity" values="0.6;0.8;0.6" dur="28s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1700" cy="600" r="0.6">
      <animate attributeName="cy" values="600;610;600;590;600" dur="36s" repeatCount="indefinite"/>
      <animate attributeName="fill-opacity" values="0.6;0.8;0.6" dur="18s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Definitions for all gradients -->
  <defs>
    <!-- Rich background gradients -->
    <linearGradient id="deepNebulaGradient" x1="0" y1="0" x2="1920" y2="1080" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#13132F"/>
      <stop offset="0.35" stop-color="#1A1A45"/>
      <stop offset="0.65" stop-color="#171736"/>
      <stop offset="1" stop-color="#0F0F24"/>
    </linearGradient>
    
    <radialGradient id="cosmicDustGradient" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(960 540) scale(1200)">
      <stop offset="0" stop-color="#2B1257" stop-opacity="0.15"/>
      <stop offset="0.5" stop-color="#2D0845" stop-opacity="0.1"/>
      <stop offset="1" stop-color="#08080E" stop-opacity="0"/>
    </radialGradient>
    
    <!-- Corner glows -->
    <radialGradient id="topLeftGlow" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-100 -100) scale(600)">
      <stop offset="0" stop-color="#6566CD" stop-opacity="0.5"/>
      <stop offset="0.5" stop-color="#6566CD" stop-opacity="0.2"/>
      <stop offset="1" stop-color="#08080E" stop-opacity="0"/>
    </radialGradient>
    
    <radialGradient id="topRightGlow" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(2020 -100) scale(550)">
      <stop offset="0" stop-color="#F96CAB" stop-opacity="0.45"/>
      <stop offset="0.5" stop-color="#F96CAB" stop-opacity="0.2"/>
      <stop offset="1" stop-color="#08080E" stop-opacity="0"/>
    </radialGradient>
    
    <radialGradient id="bottomLeftGlow" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-100 1180) scale(500)">
      <stop offset="0" stop-color="#A55EEA" stop-opacity="0.4"/>
      <stop offset="0.5" stop-color="#A55EEA" stop-opacity="0.15"/>
      <stop offset="1" stop-color="#08080E" stop-opacity="0"/>
    </radialGradient>
    
    <radialGradient id="bottomRightGlow" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(2020 1180) scale(600)">
      <stop offset="0" stop-color="#54A0FF" stop-opacity="0.45"/>
      <stop offset="0.5" stop-color="#54A0FF" stop-opacity="0.15"/>
      <stop offset="1" stop-color="#08080E" stop-opacity="0"/>
    </radialGradient>
    
    <!-- Center galaxy effect -->
    <radialGradient id="centerGalaxyGlow" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(960 540) scale(800)">
      <stop offset="0" stop-color="#9B66E5" stop-opacity="0.2"/>
      <stop offset="0.4" stop-color="#6566CD" stop-opacity="0.15"/>
      <stop offset="0.7" stop-color="#54A0FF" stop-opacity="0.1"/>
      <stop offset="1" stop-color="#08080E" stop-opacity="0"/>
    </radialGradient>
    
    <!-- Grid gradients -->
    <linearGradient id="gridGradient" x1="0" y1="0" x2="1920" y2="1080" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#6566CD" stop-opacity="0.3"/>
      <stop offset="0.5" stop-color="#8B65F7" stop-opacity="0.5"/>
      <stop offset="1" stop-color="#6566CD" stop-opacity="0.3"/>
    </linearGradient>
    
    <linearGradient id="secondaryGridGradient" x1="1920" y1="0" x2="0" y2="1080" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F96CAB" stop-opacity="0.3"/>
      <stop offset="0.5" stop-color="#FD79A8" stop-opacity="0.5"/>
      <stop offset="1" stop-color="#F96CAB" stop-opacity="0.3"/>
    </linearGradient>
    
    <!-- Energy wave gradients -->
    <linearGradient id="energyWave1" x1="0" y1="300" x2="1920" y2="300" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#6566CD"/>
      <stop offset="0.3" stop-color="#8B65F7"/>
      <stop offset="0.7" stop-color="#A55EEA"/>
      <stop offset="1" stop-color="#FD79A8"/>
    </linearGradient>
    
    <linearGradient id="energyWave2" x1="0" y1="750" x2="1920" y2="750" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F96CAB"/>
      <stop offset="0.3" stop-color="#FD79A8"/>
      <stop offset="0.7" stop-color="#E84393"/>
      <stop offset="1" stop-color="#6566CD"/>
    </linearGradient>
    
    <linearGradient id="energyWave3" x1="0" y1="500" x2="1920" y2="500" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#54A0FF"/>
      <stop offset="0.4" stop-color="#0984E3"/>
      <stop offset="0.7" stop-color="#6566CD"/>
      <stop offset="1" stop-color="#8B65F7"/>
    </linearGradient>
    
    <!-- Light path gradients -->
    <linearGradient id="lightPath1" x1="0" y1="400" x2="1920" y2="550" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#6566CD"/>
      <stop offset="0.25" stop-color="#8B65F7"/>
      <stop offset="0.5" stop-color="#A55EEA"/>
      <stop offset="0.75" stop-color="#FD79A8"/>
      <stop offset="1" stop-color="#F96CAB"/>
    </linearGradient>
    
    <linearGradient id="lightPath2" x1="0" y1="600" x2="1920" y2="400" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#F96CAB"/>
      <stop offset="0.25" stop-color="#E84393"/>
      <stop offset="0.5" stop-color="#A55EEA"/>
      <stop offset="0.75" stop-color="#8B65F7"/>
      <stop offset="1" stop-color="#6566CD"/>
    </linearGradient>
    
    <!-- Star cluster glows -->
    <radialGradient id="starCluster1" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(400 250) scale(120)">
      <stop offset="0" stop-color="#8B65F7" stop-opacity="0.3"/>
      <stop offset="0.7" stop-color="#6566CD" stop-opacity="0.15"/>
      <stop offset="1" stop-color="#08080E" stop-opacity="0"/>
    </radialGradient>
    
    <radialGradient id="starCluster2" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1600 750) scale(150)">
      <stop offset="0" stop-color="#F96CAB" stop-opacity="0.3"/>
      <stop offset="0.7" stop-color="#E84393" stop-opacity="0.15"/>
      <stop offset="1" stop-color="#08080E" stop-opacity="0"/>
    </radialGradient>
    
    <radialGradient id="starCluster3" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(900 800) scale(100)">
      <stop offset="0" stop-color="#54A0FF" stop-opacity="0.25"/>
      <stop offset="0.7" stop-color="#0984E3" stop-opacity="0.12"/>
      <stop offset="1" stop-color="#08080E" stop-opacity="0"/>
    </radialGradient>
  </defs>
</svg> 