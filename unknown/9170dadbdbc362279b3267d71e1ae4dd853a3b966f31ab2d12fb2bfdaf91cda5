.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--modal-backdrop);
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
  padding: 20px;
  animation: fadeIn 0.25s ease-out;
  overflow: auto;
}

.modal-content {
  background: var(--modal-bg);
  border-radius: 12px;
  border: 1px solid var(--modal-border);
  box-shadow: 0 8px 32px var(--modal-shadow);
  animation: slideDown 0.3s ease-out;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 1051;
  color: var(--text-color);
  transition: background 0.3s ease, border 0.3s ease, box-shadow 0.3s ease;
  
  /* Glass effect enhancement */
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: -1;
    background: var(--modal-bg);
    opacity: 0.95;
    border-radius: inherit;
  }
}

// Modal sizes
.modal-small {
  width: 400px;
  max-width: 100%;
  max-height: 90vh;
}

.modal-medium {
  width: 600px;
  max-width: 100%;
  max-height: 90vh;
}

.modal-large {
  width: 800px;
  max-width: 100%;
  max-height: 90vh;
}

.modal-fullscreen {
  width: 90vw;
  height: 90vh;
  max-width: 100%;
  max-height: 90vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--modal-header-border);
  margin-bottom: 0;
}

.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: var(--text-color);
}

.close-button {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  width: 32px;
  height: 32px;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: var(--nav-hover);
    color: var(--text-color);
    transform: rotate(90deg);
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--input-focus-shadow);
  }
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--modal-footer-border);
}

.no-padding {
  .modal-body {
    padding: 0;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-small {
    width: 100%;
  }
  
  .modal-medium {
    width: 100%;
  }
  
  .modal-large {
    width: 100%;
  }
  
  .modal-fullscreen {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
  }
  
  .modal-container {
    padding: 10px;
  }
  
  .modal-header {
    padding: 16px 20px;
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .modal-footer {
    padding: 16px 20px;
  }
} 