import { CardData } from '../models/card.model';

export const MOCK_MODELS: CardData[] = [
  {
    id: '1',
    title: 'GPT-4o',
    tags: [
      { label: 'LLM' },
      { label: 'OpenAI' },
      { label: 'Proprietary' },
      { label: 'General Purpose' }
    ],
    createdDate: '4/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'openai',
    department: 'llm',
    role: 'general-purpose',
    project: 'gpt-series',
    category: 'commercial'
  },
  {
    id: '2',
    title: 'Claude 3 Opus',
    tags: [
      { label: 'LLM' },
      { label: 'Anthropic' },
      { label: 'Proprietary' },
      { label: 'General Purpose' }
    ],
    createdDate: '3/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'anthropic',
    department: 'llm',
    role: 'general-purpose',
    project: 'claude-series',
    category: 'commercial'
  },
  {
    id: '3',
    title: 'Llama 3 70B',
    tags: [
      { label: 'LLM' },
      { label: 'Meta' },
      { label: 'Open Source' },
      { label: 'General Purpose' }
    ],
    createdDate: '2/28/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'community',
    client: 'meta',
    department: 'llm',
    role: 'general-purpose',
    project: 'llama-series',
    category: 'open-source'
  },
  {
    id: '4',
    title: 'Stable Diffusion 3',
    tags: [
      { label: 'Image Generation' },
      { label: 'Stability AI' },
      { label: 'Open Source' },
      { label: 'Multimodal' }
    ],
    createdDate: '3/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'community',
    client: 'stability-ai',
    department: 'computer-vision',
    role: 'image-generation',
    project: 'stable-diffusion',
    category: 'open-source'
  },
  {
    id: '5',
    title: 'DALL-E 3',
    tags: [
      { label: 'Image Generation' },
      { label: 'OpenAI' },
      { label: 'Proprietary' },
      { label: 'Multimodal' }
    ],
    createdDate: '1/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'openai',
    department: 'computer-vision',
    role: 'image-generation',
    project: 'dall-e',
    category: 'commercial'
  },
  {
    id: '6',
    title: 'Gemini Pro 1.5',
    tags: [
      { label: 'LLM' },
      { label: 'Google' },
      { label: 'Proprietary' },
      { label: 'Multimodal' }
    ],
    createdDate: '2/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'google',
    department: 'llm',
    role: 'multimodal',
    project: 'gemini-series',
    category: 'commercial'
  },
  {
    id: '7',
    title: 'Mistral 8x7B',
    tags: [
      { label: 'LLM' },
      { label: 'Mistral AI' },
      { label: 'Open Source' },
      { label: 'General Purpose' }
    ],
    createdDate: '1/25/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'community',
    client: 'mistral-ai',
    department: 'llm',
    role: 'general-purpose',
    project: 'mistral-series',
    category: 'open-source'
  },
  {
    id: '8',
    title: 'Whisper v3',
    tags: [
      { label: 'Speech-to-Text' },
      { label: 'OpenAI' },
      { label: 'Open Source' },
      { label: 'Audio Processing' }
    ],
    createdDate: '3/12/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'community',
    client: 'openai',
    department: 'audio',
    role: 'speech-to-text',
    project: 'whisper',
    category: 'open-source'
  },
  {
    id: '9',
    title: 'Sora',
    tags: [
      { label: 'Video Generation' },
      { label: 'OpenAI' },
      { label: 'Proprietary' },
      { label: 'Multimodal' }
    ],
    createdDate: '2/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'openai',
    department: 'computer-vision',
    role: 'video-generation',
    project: 'sora',
    category: 'commercial'
  },
  {
    id: '10',
    title: 'BERT Large',
    tags: [
      { label: 'NLP' },
      { label: 'Google' },
      { label: 'Open Source' },
      { label: 'Text Processing' }
    ],
    createdDate: '1/18/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'community',
    client: 'google',
    department: 'nlp',
    role: 'text-processing',
    project: 'bert',
    category: 'open-source'
  },
  {
    id: '11',
    title: 'MusicGen',
    tags: [
      { label: 'Audio Generation' },
      { label: 'Meta' },
      { label: 'Open Source' },
      { label: 'Audio Processing' }
    ],
    createdDate: '3/8/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'community',
    client: 'meta',
    department: 'audio',
    role: 'music-generation',
    project: 'musicgen',
    category: 'open-source'
  },
  {
    id: '12',
    title: 'YOLO v8',
    tags: [
      { label: 'Object Detection' },
      { label: 'Ultralytics' },
      { label: 'Open Source' },
      { label: 'Computer Vision' }
    ],
    createdDate: '2/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'community',
    client: 'ultralytics',
    department: 'computer-vision',
    role: 'object-detection',
    project: 'yolo',
    category: 'open-source'
  },
  {
    id: '13',
    title: 'GPT-4 Vision',
    tags: [
      { label: 'LLM' },
      { label: 'OpenAI' },
      { label: 'Proprietary' },
      { label: 'Multimodal' }
    ],
    createdDate: '1/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'openai',
    department: 'llm',
    role: 'multimodal',
    project: 'gpt-series',
    category: 'commercial'
  },
  {
    id: '14',
    title: 'Claude 3 Sonnet',
    tags: [
      { label: 'LLM' },
      { label: 'Anthropic' },
      { label: 'Proprietary' },
      { label: 'General Purpose' }
    ],
    createdDate: '3/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'anthropic',
    department: 'llm',
    role: 'general-purpose',
    project: 'claude-series',
    category: 'commercial'
  },
  {
    id: '15',
    title: 'Llama 3 8B',
    tags: [
      { label: 'LLM' },
      { label: 'Meta' },
      { label: 'Open Source' },
      { label: 'General Purpose' }
    ],
    createdDate: '2/22/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'community',
    client: 'meta',
    department: 'llm',
    role: 'general-purpose',
    project: 'llama-series',
    category: 'open-source'
  },
  {
    id: '16',
    title: 'Midjourney v6',
    tags: [
      { label: 'Image Generation' },
      { label: 'Midjourney' },
      { label: 'Proprietary' },
      { label: 'Multimodal' }
    ],
    createdDate: '1/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'midjourney',
    department: 'computer-vision',
    role: 'image-generation',
    project: 'midjourney',
    category: 'commercial'
  },
  {
    id: '17',
    title: 'Gemini Ultra 1.0',
    tags: [
      { label: 'LLM' },
      { label: 'Google' },
      { label: 'Proprietary' },
      { label: 'Multimodal' }
    ],
    createdDate: '2/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'google',
    department: 'llm',
    role: 'multimodal',
    project: 'gemini-series',
    category: 'commercial'
  },
  {
    id: '18',
    title: 'Mistral 7B v0.3',
    tags: [
      { label: 'LLM' },
      { label: 'Mistral AI' },
      { label: 'Open Source' },
      { label: 'General Purpose' }
    ],
    createdDate: '1/30/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'community',
    client: 'mistral-ai',
    department: 'llm',
    role: 'general-purpose',
    project: 'mistral-series',
    category: 'open-source'
  },
  {
    id: '19',
    title: 'ResNet-152',
    tags: [
      { label: 'Image Classification' },
      { label: 'Microsoft' },
      { label: 'Open Source' },
      { label: 'Computer Vision' }
    ],
    createdDate: '3/1/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'community',
    client: 'microsoft',
    department: 'computer-vision',
    role: 'image-classification',
    project: 'resnet',
    category: 'open-source'
  },
  {
    id: '20',
    title: 'CLIP',
    tags: [
      { label: 'Multimodal' },
      { label: 'OpenAI' },
      { label: 'Open Source' },
      { label: 'Embedding' }
    ],
    createdDate: '2/25/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Model' },

      { icon: 'delete', action: 'delete', tooltip: 'Delete Model' }
    ],
    // Filter properties
    userType: 'community',
    client: 'openai',
    department: 'multimodal',
    role: 'embedding',
    project: 'clip',
    category: 'open-source'
  }
]; 