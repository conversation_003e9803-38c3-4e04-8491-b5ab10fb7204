/**
 * New polling API response interfaces for the updated API structure
 *
 * Updated to match the exact structure provided in the new polling response:
 * {
 *   "status_code": 200,
 *   "details": {
 *     "status": "IN_PROGRESS",
 *     "log": " Agent : Code Agent | Understanding the project requirements.\n Action:  Understanding the project requirements. ",
 *     "progress": "OVERVIEW",
 *     "progress_description": "What an exciting idea! This dashboard centers around lead management and sales tracking...",
 *     "history": [
 *       {
 *         "log": " Agent : Code Agent | Understanding the project requirements.\n Action:  Understanding the project requirements. ",
 *         "status": "IN_PROGRESS",
 *         "metadata": [
 *           {
 *             "data": {
 *               "data": "What an exciting idea! This dashboard centers around lead management...",
 *               "type": "text"
 *             },
 *             "type": "artifact"
 *           },
 *           {
 *             "data": "LeadPulse Dashboard",
 *             "type": "ref_code"
 *           }
 *         ],
 *         "progress": "OVERVIEW",
 *         "progress_description": "What an exciting idea! This dashboard centers around lead management..."
 *       }
 *     ],
 *     "metadata": [
 *       {
 *         "type": "artifact",
 *         "data": {
 *           "type": "text",
 *           "data": "What an exciting idea! This dashboard centers around lead management..."
 *         }
 *       },
 *       {
 *         "type": "ref_code",
 *         "data": "LeadPulse Dashboard"
 *       }
 *     ]
 *   }
 * }
 */

/**
 * Enhanced metadata item interface with strict typing for ref_code URLs
 *
 * **REQUIREMENTS**:
 * - ref_code type must contain string data with deployed URL
 * - Supports all existing metadata types for backward compatibility
 * - Provides type safety for URL extraction and validation
 */
export interface MetadataItem {
  type: 'files' | 'ref_code' | 'artifact' | 'fileNames';
  data: string | any | NestedArtifactData; // Can be string, JSON object, or nested object with type and data
}

/**
 * Specific interface for ref_code metadata containing deployed URLs
 * Used for type safety when extracting URLs from DEPLOY state metadata
 */
export interface RefCodeMetadata extends MetadataItem {
  type: 'ref_code';
  data: string; // Must be a string containing the deployed URL
}

/**
 * Interface for nested artifact data structure
 * Used when metadata type is 'artifact' and contains nested data with type and data fields
 */
export interface NestedArtifactData {
  type: 'text' | 'json' | 'string' | 'files' | 'ref_code';
  data: string | any;
}

export interface HistoryItem {
  progress: string;
  status: string;
  log: string;
  progress_description: string;
  metadata: MetadataItem[];
}

export interface NewPollingResponseWithPrevMetadata extends NewPollingResponse {
  prev_metadata?: MetadataItem[];
}

export interface NewPollingResponse {
  progress: string;
  status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  log: string;
  progress_description: string;
  history: HistoryItem[];
  metadata: MetadataItem[];
}

/**
 * Project information extracted from ref_code
 */
export interface ProjectInfo {
  name: string;
  url: string;
  id: string;
}

/**
 * Artifact data structures for different progress states
 */
export interface ArtifactData {
  type: 'text' | 'string' | 'json';
  value: string | any;
}

export interface DesignTokensData {
  colors: Array<{
    value: string;
    name: string;
    type: string;
    enabled: boolean;
  }>;
  fonts: Array<{
    name: string;
    size: string;
    weight: string;
    lineHeight: string;
  }>;
  buttons: Array<{
    name: string;
    variant: string;
    size: string;
    style: any;
  }>;
}

export interface FileData {
  path: string;
  code: string;
}

/**
 * Progress state types for type safety
 * Updated to include all stepper states and new API states
 */
export type ProgressState =
  | 'OVERVIEW'
  | 'SEED_PROJECT_INITIALIZED'
  | 'FILE_QUEUE'
  | 'DESIGN_SYSTEM_MAPPED'
  | 'COMPONENTS_CREATED'
  | 'LAYOUT_ANALYZED'
  | 'PAGES_GENERATED'
  | 'BUILD_STARTED'
  | 'BUILD_SUCCEEDED'
  | 'BUILD_FAILED'
  | 'BUILD'
  | 'FILES_GENERATED'
  | 'DEPLOYED'
  // New API states as specified in requirements
  | 'project-overview'
  | 'Layout Analyzed'
  | 'Design_System Analyzed'
  | 'DEPLOY'
  // Legacy states for backward compatibility
  | 'deployed';

/**
 * Status types for type safety
 */
export type StatusType = 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';

/**
 * Metadata type for type safety
 */
export type MetadataType = 'files' | 'ref_code' | 'artifact' | 'fileNames';
