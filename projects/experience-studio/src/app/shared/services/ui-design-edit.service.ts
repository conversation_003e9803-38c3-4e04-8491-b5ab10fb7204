import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { createLogger } from '../utils/logger';
import { EditRequest, EditResponse } from './ui-design-selection.service';
import { environment } from '../../../environments/environment';
import { ApiRetryService } from './api-retry.service';

@Injectable({
  providedIn: 'root'
})
export class UIDesignEditService {
  private readonly logger = createLogger('UIDesignEditService');
  private readonly http = inject(HttpClient);
  private readonly apiRetryService = inject(ApiRetryService);

  // API configuration
  private readonly baseUrl = environment.apiUrl;
  private readonly editEndpoint = 'wireframe-generation/regenerate';
  // Removed requestTimeout to prevent 499 client errors
  constructor() {
    this.logger.info('🔧 UI Design Edit Service initialized');
  }

  /**
   * Edit UI Design page content
   * 🚨 CRITICAL: NO TIMEOUT - wireframe-generation/regenerate can take longer than 4 minutes
   * Removed all client-side timeouts to prevent 499 errors
   */
  editUIDesignPage(request: EditRequest): Observable<EditResponse> {
    const url = `${this.baseUrl}/${this.editEndpoint}`;

    this.logger.info('📡 Calling wireframe-generation/regenerate API with NO CLIENT-SIDE TIMEOUT:', {
      endpoint: url,
      fileName: this.extractFileNameFromRequest(request),
      userRequest: this.extractUserRequestFromRequest(request),
      requestSize: JSON.stringify(request).length
    });

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
      // Removed 'Connection' and 'Keep-Alive' headers as they are forbidden by browsers
      // Browser automatically manages connection keep-alive for optimal performance
    });

    // Create the HTTP request observable
    const httpRequest = this.http.post<any>(url, request, {
      headers,
      observe: 'response'
    }).pipe(
      // 🛡️ CRITICAL: NO TIMEOUT to prevent 499 client errors
      // Let the server determine when to timeout, not the client
      // timeout() operator removed completely to prevent client-side timeouts
      map(response => {
        this.logger.info('✅ Edit API response received:', {
          status: response.status,
          statusText: response.statusText,
          responseSize: JSON.stringify(response.body).length
        });

        // Validate response structure
        if (!this.isValidEditResponse(response.body)) {
          throw new Error('Invalid response format from edit API');
        }

        return response.body as EditResponse;
      }),
      catchError(error => {
        this.logger.error('❌ Edit API request failed:', {
          error: error.message,
          status: error.status,
          statusText: error.statusText,
          url: error.url
        });

        // Enhanced 499 error handling
        if (error.status === 499) {
          this.logger.error('🚫 499 Client Closed Request - connection was closed before completion');
          this.logger.error('This typically happens when the request takes longer than browser/network timeout');
          return throwError(() => new Error('Request was cancelled due to timeout. The operation may still be processing on the server.'));
        }

        // Handle specific error cases
        if (error.status === 0) {
          this.logger.error('🌐 Network error - connection failed or was interrupted');
          return throwError(() => new Error('Network error. Please check your connection.'));
        }

        if (error.status >= 400 && error.status < 500) {
          return throwError(() => new Error('Invalid request. Please check your input and try again.'));
        }

        if (error.status >= 500) {
          return throwError(() => new Error('Server error. Please try again later.'));
        }

        return throwError(() => new Error(error.message || 'An unexpected error occurred'));
      })
    );

    // Apply retry logic for 499 errors and network failures
    return this.apiRetryService.withRetry(httpRequest, this.apiRetryService.getEditRetryConfig());
  }

  /**
   * Validate edit API response structure (handles both array and stringified array formats)
   */
  private isValidEditResponse(response: any): boolean {
    let parsedResponse: any;

    // Handle stringified JSON response
    if (typeof response === 'string') {
      try {
        parsedResponse = JSON.parse(response);
        this.logger.info('📦 Parsed stringified JSON response');
      } catch (error) {
        this.logger.error('❌ Failed to parse stringified response:', error);
        return false;
      }
    } else {
      parsedResponse = response;
    }

    if (!Array.isArray(parsedResponse)) {
      this.logger.error('❌ Response is not an array:', parsedResponse);
      return false;
    }

    if (parsedResponse.length === 0) {
      this.logger.error('❌ Response array is empty');
      return false;
    }

    // Validate each code item in the array
    for (const item of parsedResponse) {
      if (!item || typeof item !== 'object') {
        this.logger.error('❌ Response item is not an object:', item);
        return false;
      }

      if (typeof item.fileName !== 'string' || !item.fileName.trim()) {
        this.logger.error('❌ Response item missing valid fileName:', item);
        return false;
      }

      if (typeof item.content !== 'string') {
        this.logger.error('❌ Response item missing valid content:', item);
        return false;
      }
    }

    this.logger.info('✅ Edit response validation passed');
    return true;
  }

  /**
   * Extract fileName from request for logging
   */
  private extractFileNameFromRequest(request: EditRequest): string {
    try {
      if (request.code?.[0]?.fileName) {
        return request.code[0].fileName;
      }
    } catch (error) {
      this.logger.warn('⚠️ Could not extract fileName from request');
    }

    return 'unknown';
  }

  /**
   * Extract user request from request for logging
   */
  private extractUserRequestFromRequest(request: EditRequest): string {
    try {
      if (request.user_request) {
        return request.user_request;
      }
    } catch (error) {
      this.logger.warn('⚠️ Could not extract user_request from request');
    }

    return 'unknown';
  }

  /**
   * Build edit request with proper format
   */
  buildEditRequest(fileName: string, content: string, userRequest: string): EditRequest {
    const request: EditRequest = {
      code: [{
        fileName: fileName,
        content: content
      }],
      user_request: userRequest
    };

    this.logger.info('🔧 Built edit request:', {
      fileName: fileName,
      userRequest: userRequest,
      contentLength: content.length
    });

    return request;
  }

  /**
   * Extract updated content from edit response (handles both array and stringified array formats)
   */
  extractUpdatedContent(response: EditResponse | string, fileName: string): string | null {
    let parsedResponse: EditResponse;

    // Handle stringified JSON response
    if (typeof response === 'string') {
      try {
        parsedResponse = JSON.parse(response);
        this.logger.info('📦 Parsed stringified JSON response for content extraction');
      } catch (error) {
        this.logger.error('❌ Failed to parse stringified response for content extraction:', error);
        return null;
      }
    } else {
      parsedResponse = response;
    }

    const matchingFile = parsedResponse.find((file: { fileName: string; content: string }) =>
      file.fileName === fileName
    );

    if (!matchingFile) {
      this.logger.error('❌ No matching file found in response:', {
        expectedFileName: fileName,
        availableFiles: parsedResponse.map((f: { fileName: string; content: string }) => f.fileName)
      });
      return null;
    }

    this.logger.info('✅ Extracted updated content:', {
      fileName: matchingFile.fileName,
      contentLength: matchingFile.content.length
    });

    return matchingFile.content;
  }

  /**
   * Get service status for debugging
   */
  getServiceStatus(): {
    baseUrl: string;
    editEndpoint: string;
    fullUrl: string;
    timeout: string;
  } {
    return {
      baseUrl: this.baseUrl,
      editEndpoint: this.editEndpoint,
      fullUrl: `${this.baseUrl}/${this.editEndpoint}`,
      timeout: 'No timeout (prevents 499 errors)'
    };
  }
}
