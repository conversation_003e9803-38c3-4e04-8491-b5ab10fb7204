import { Component, ContentChildren, QueryList, AfterContentInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccordionItemComponent } from './accordion-item/accordion-item.component';

@Component({
  selector: 'app-accordion',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './accordion.component.html',
  styleUrl: './accordion.component.scss'
})
export class AccordionComponent implements AfterContentInit {
  @ContentChildren(AccordionItemComponent) items!: QueryList<AccordionItemComponent>;

  ngAfterContentInit() {
    // You can implement multi-select or single-select behavior here
    // For single-select accordion (only one open at a time), uncomment this code:
    /*
    this.items.forEach(item => {
      item.toggle.subscribe(() => {
        this.items.forEach(otherItem => {
          if (otherItem !== item && otherItem.isOpen) {
            otherItem.isOpen = false;
          }
        });
      });
    });
    */
  }
} 