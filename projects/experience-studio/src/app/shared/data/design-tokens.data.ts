/**
 * Design Tokens Data Store
 *
 * This file contains all the design tokens currently being displayed in the frontend
 * including colors, typography, and button tokens with their corresponding data.
 *
 * Last Updated: 2024-01-15
 * Total Tokens: 14 (5 colors + 4 typography + 5 buttons)
 */

/**
 * Interface for design tokens that can be edited
 */
export interface DesignToken {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'size';
  category: string;   
  editable: boolean;
  description?: string;
  usage?: string[];
}

/**
 * Interface for color tokens
 */
export interface ColorToken {
  name: string;
  value: string;
  hexCode: string;
  description?: string;
  usage?: string[];
}

/**
 * Interface for font style tokens
 */
export interface FontStyleToken {
  name: string;
  size: string;
  weight: string;
  lineHeight: string;
  description?: string;
  usage?: string[];
}

/**
 * Interface for button tokens
 */
export interface ButtonToken {
  label: string;
  variant: string;
  hasIcon?: boolean;
  description?: string;
  usage?: string[];
}

/**
 * Default Color Tokens (5 tokens)
 * These are the color tokens currently displayed in the Design System artifacts view
 */
export const DEFAULT_COLOR_TOKENS: ColorToken[] = [
  {
    name: 'D_Yellow',
    value: '#E48900',
    hexCode: '#E48900',
    description: 'Primary yellow color for highlights and accents',
    usage: ['buttons', 'highlights', 'warnings', 'call-to-action']
  },
  {
    name: 'Lemon',
    value: '#FFA826',
    hexCode: '#FFA826',
    description: 'Secondary yellow color for lighter accents',
    usage: ['secondary-buttons', 'hover-states', 'badges', 'notifications']
  },
  {
    name: 'Black',
    value: '#212121',
    hexCode: '#212121',
    description: 'Primary text color and dark elements',
    usage: ['text', 'headers', 'borders', 'icons']
  },
  {
    name: 'D_Grey',
    value: '#4D4D4D',
    hexCode: '#4D4D4D',
    description: 'Secondary text color and medium contrast elements',
    usage: ['secondary-text', 'placeholders', 'disabled-states', 'subtle-borders']
  },
  {
    name: 'Silver',
    value: '#F5F7FA',
    hexCode: '#F5F7FA',
    description: 'Light background color and subtle surfaces',
    usage: ['backgrounds', 'cards', 'input-fields', 'dividers']
  }
];

/**
 * Default Typography Tokens (4 tokens)
 * These are the typography tokens currently displayed in the Design System artifacts view
 */
export const DEFAULT_TYPOGRAPHY_TOKENS: FontStyleToken[] = [
  {
    name: 'Headline 1',
    size: '64/76',
    weight: 'Semi Bold',
    lineHeight: '1.2',
    description: 'Main page headlines and hero text',
    usage: ['page-titles', 'hero-sections', 'main-headings']
  },
  {
    name: 'Headline 2',
    size: '36/44',
    weight: 'Semi Bold',
    lineHeight: '1.2',
    description: 'Section headers and important subheadings',
    usage: ['section-headers', 'card-titles', 'modal-headers']
  },
  {
    name: 'Headline 3',
    size: '28/36',
    weight: 'Semi Bold',
    lineHeight: '1.3',
    description: 'Subsection headers and component titles',
    usage: ['subsection-headers', 'component-titles', 'form-labels']
  },
  {
    name: 'Headline 4',
    size: '20/28',
    weight: 'Semi Bold',
    lineHeight: '1.4',
    description: 'Small headers and emphasized text',
    usage: ['small-headers', 'emphasized-text', 'button-labels']
  }
];

/**
 * Default Button Tokens (5 tokens)
 * These are the button tokens currently displayed in the Design System artifacts view
 */
export const DEFAULT_BUTTON_TOKENS: ButtonToken[] = [
  {
    label: 'Label',
    variant: 'primary',
    hasIcon: false,
    description: 'Primary action button with solid background',
    usage: ['submit-forms', 'main-actions', 'call-to-action']
  },
  {
    label: 'Label',
    variant: 'primary',
    hasIcon: true,
    description: 'Primary action button with icon',
    usage: ['submit-with-icon', 'download-buttons', 'action-with-context']
  },
  {
    label: 'Label',
    variant: 'secondary',
    hasIcon: false,
    description: 'Secondary action button with subtle styling',
    usage: ['cancel-actions', 'secondary-options', 'navigation']
  },
  {
    label: 'Label',
    variant: 'outline',
    hasIcon: false,
    description: 'Outlined button for less prominent actions',
    usage: ['filters', 'toggles', 'optional-actions']
  },
  {
    label: 'Label',
    variant: 'text',
    hasIcon: false,
    description: 'Text-only button for minimal actions',
    usage: ['links', 'minimal-actions', 'inline-actions']
  }
];

/**
 * Complete Design Tokens Array (14 tokens total)
 * This combines all token types into a single array matching the frontend structure
 */
export const ALL_DESIGN_TOKENS: DesignToken[] = [
  // Color Tokens (5)
  ...DEFAULT_COLOR_TOKENS.map((color, index) => ({
    id: `color-${index}`,
    name: color.name,
    value: color.value,
    type: 'color' as const,
    category: 'Colors',
    editable: true,
    description: color.description,
    usage: color.usage
  })),

  // Typography Tokens (4)
  ...DEFAULT_TYPOGRAPHY_TOKENS.map((font, index) => ({
    id: `typography-${index}`,
    name: font.name,
    value: `${font.weight}, ${font.size}`,
    type: 'typography' as const,
    category: 'Font Style Desktop',
    editable: true,
    description: font.description,
    usage: font.usage
  })),

  // Button Tokens (5)
  ...DEFAULT_BUTTON_TOKENS.map((button, index) => ({
    id: `button-${index}`,
    name: button.label,
    value: button.variant,
    type: 'size' as const,
    category: 'Buttons',
    editable: true,
    description: button.description,
    usage: button.usage
  }))
];

/**
 * Token Statistics
 */
export const DESIGN_TOKENS_STATS = {
  total: ALL_DESIGN_TOKENS.length,
  colors: DEFAULT_COLOR_TOKENS.length,
  typography: DEFAULT_TYPOGRAPHY_TOKENS.length,
  buttons: DEFAULT_BUTTON_TOKENS.length,
  editable: ALL_DESIGN_TOKENS.filter(token => token.editable).length,
  categories: [...new Set(ALL_DESIGN_TOKENS.map(token => token.category))],
  types: [...new Set(ALL_DESIGN_TOKENS.map(token => token.type))]
};

/**
 * Helper function to get tokens by category
 */
export function getTokensByCategory(category: string): DesignToken[] {
  return ALL_DESIGN_TOKENS.filter(token => token.category === category);
}

/**
 * Helper function to get tokens by type
 */
export function getTokensByType(type: string): DesignToken[] {
  return ALL_DESIGN_TOKENS.filter(token => token.type === type);
}

/**
 * Helper function to get token by ID
 */
export function getTokenById(id: string): DesignToken | undefined {
  return ALL_DESIGN_TOKENS.find(token => token.id === id);
}

/**
 * Helper function to validate color token
 */
export function isValidHexColor(color: string): boolean {
  const hexRegex = /^#?([0-9A-F]{3}|[0-9A-F]{6})$/i;
  return hexRegex.test(color);
}

/**
 * Export for easy import in components
 */
export default {
  ALL_DESIGN_TOKENS,
  DEFAULT_COLOR_TOKENS,
  DEFAULT_TYPOGRAPHY_TOKENS,
  DEFAULT_BUTTON_TOKENS,
  DESIGN_TOKENS_STATS,
  getTokensByCategory,
  getTokensByType,
  getTokenById,
  isValidHexColor
};
