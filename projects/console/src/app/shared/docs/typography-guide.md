# Typography Guide

This guide outlines the typography system used in the Console application, based on the Mulish font family.

## Font Family

The Console application uses **Mulish** as its primary font. Mulish is a minimalist sans serif typeface designed for both display and text typography with 9 weights and matching italics.

## Font Weights

The following font weights are available as CSS variables:

```scss
--font-weight-light: 300;
--font-weight-regular: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
--font-weight-extrabold: 800;
```

## Heading Styles

The application includes standardized heading styles:

```html
<h1>Heading 1 - 36px</h1>
<h2>Heading 2 - 28px</h2>
<h3>Heading 3 - 24px</h3>
<h4>Heading 4 - 20px</h4>
<h5>Heading 5 - 18px</h5>
<h6>Heading 6 - 16px</h6>
```

You can also use class-based styling:

```html
<div class="h1">Heading 1 Style</div>
<div class="h2">Heading 2 Style</div>
<!-- etc. -->
```

## Body Text

Standard body text uses the following styles:

```html
<p>Regular paragraph text is 16px with a line height of 1.5.</p>
<div class="body-text">This has the same styling as paragraphs.</div>
<div class="body-text-small">Smaller body text at 14px.</div>
<div class="body-text-extra-small">Extra small text at 12px.</div>
```

## Text Utilities

Various text utility classes are available:

```html
<span class="text-light">Light weight text (300)</span>
<span class="text-regular">Regular weight text (400)</span>
<span class="text-medium">Medium weight text (500)</span>
<span class="text-semibold">Semi-bold text (600)</span>
<span class="text-bold">Bold text (700)</span>
<span class="text-extrabold">Extra bold text (800)</span>

<!-- Text colors -->
<span class="text-primary">Primary colored text</span>
<span class="text-secondary">Secondary colored text</span>
<span class="text-success">Success colored text</span>
<span class="text-warning">Warning colored text</span>
<span class="text-error">Error colored text</span>
<span class="text-info">Info colored text</span>

<!-- Special text styles -->
<span class="caption">Caption text</span>
<span class="overline">Overline text (all caps)</span>
<span class="label">Label text</span>
```

## Using in SCSS

When working with component SCSS files, always use the CSS variables for font properties:

```scss
.my-custom-element {
  font-family: var(--font-family);
  font-weight: var(--font-weight-medium);
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-color);
}
```

## Responsive Typography

The typography system automatically adjusts font sizes on smaller screens:

- For screens under 768px, heading sizes are reduced
- For screens under 576px, heading sizes are further reduced

## Code Text

For code snippets, a monospace font is used:

```html
<pre class="code">This will use a monospace font</pre>
<code>Inline code uses monospace too</code>
```

## Best Practices

1. **Use Semantic Markup**: Prefer `<h1>` - `<h6>` for headings and `<p>` for paragraphs
2. **Maintain Hierarchy**: Follow a clear heading hierarchy (h1 → h2 → h3...)
3. **Consistent Spacing**: Use consistent margins between text elements
4. **Readability**: Ensure adequate contrast between text and background colors
5. **Responsive Design**: Test text rendering on various screen sizes
6. **Accessibility**: Keep text resizable and ensure minimum font size of 14px for body text 