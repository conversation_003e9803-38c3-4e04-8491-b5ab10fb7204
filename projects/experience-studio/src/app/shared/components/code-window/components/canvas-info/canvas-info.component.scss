// ===== ENHANCED CANVAS SHORTCUTS PANEL =====

// Info Ball Toggle Button
.info-ball-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1001;
}

.info-ball {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--info-ball-bg, #ffffff);
  border: 2px solid var(--info-ball-border, #e5e7eb);
  color: var(--info-ball-color, #374151);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  box-shadow: var(--info-ball-shadow, 0 4px 16px rgba(0, 0, 0, 0.1));
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  position: relative;

  // Add subtle inner shadow for depth
  &::before {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: 50%;
    background: var(--info-ball-inner-highlight, rgba(0, 0, 0, 0.03));
    pointer-events: none;
  }

  &:hover {
    transform: scale(1.1);
    box-shadow: var(--info-ball-hover-shadow, 0 6px 24px rgba(0, 0, 0, 0.15));
    background: var(--info-ball-hover-bg, #f9fafb);
    border-color: var(--info-ball-hover-border, #d1d5db);
  }

  &:active {
    transform: scale(0.95);
  }

  &.active {
    background: var(--info-ball-active-bg, #f3f4f6);
    border-color: var(--info-ball-active-border, #9ca3af);
    box-shadow: var(--info-ball-active-shadow, 0 6px 24px rgba(0, 0, 0, 0.2));
    color: var(--info-ball-active-color, #111827);
  }

  &:focus-visible {
    outline: 2px solid var(--info-ball-focus, rgba(59, 130, 246, 0.5));
    outline-offset: 2px;
  }

  // SVG Icon Container
  app-info-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none; // Let button handle clicks
  }
}

// Enhanced Shortcuts Panel
.canvas-shortcuts-panel {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 420px;
  max-height: 70vh;
  background: var(--shortcuts-panel-bg, rgba(255, 255, 255, 0.98));
  border: 1px solid var(--shortcuts-panel-border, #e1e5e9);
  border-radius: 16px;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px) scale(0.95);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
  }

  .info-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: var(--debug-header-bg, #f8fafc);
    border-bottom: 1px solid var(--debug-header-border, #e5e7eb);
    cursor: pointer;
    user-select: none;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--debug-header-color, #1f2937);
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: var(--debug-icon-color, #3b82f6);
      }
    }

    .collapse-btn {
      background: transparent;
      border: none;
      color: var(--debug-collapse-color, #6b7280);
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background: var(--debug-collapse-hover-bg, #e5e7eb);
        color: var(--debug-collapse-hover-color, #374151);
      }

      i {
        font-size: 14px;
      }
    }
  }

// Panel Header (New Enhanced Version)
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  background: var(--panel-header-bg, #f8fafc);
  border-bottom: 1px solid var(--panel-header-border, #e1e5e9);

  h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--panel-header-color, #1f2937);
    display: flex;
    align-items: center;
    gap: 10px;

    i {
      color: var(--panel-icon-color, #3b82f6);
      font-size: 18px;
    }
  }

  .close-btn {
    background: none;
    border: none;
    color: var(--panel-close-color, #6b7280);
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;

    &:hover {
      background: var(--panel-close-hover-bg, #f3f4f6);
      color: var(--panel-close-hover-color, #374151);
    }

    &:focus-visible {
      outline: 2px solid var(--panel-focus, rgba(59, 130, 246, 0.5));
      outline-offset: 2px;
    }

    i {
      font-size: 16px;
    }
  }
}

// Panel Content
.panel-content {
  padding: 0 24px 24px;
  max-height: calc(70vh - 80px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb, #cbd5e1) var(--scrollbar-track, transparent);

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb, #cbd5e1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover, #94a3b8);
  }
}

// Shortcuts Sections
.shortcuts-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-header {
    margin-bottom: 12px;

    h5 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--section-header-color, #374151);
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: var(--section-icon-color, #6b7280);
        font-size: 14px;
      }
    }
  }
}

// Shortcuts Grid
.shortcuts-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--shortcut-item-bg, #f8fafc);
  border: 1px solid var(--shortcut-item-border, #e2e8f0);
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--shortcut-item-hover-bg, #f1f5f9);
    border-color: var(--shortcut-item-hover-border, #cbd5e1);
  }

  .shortcut-label {
    font-size: 13px;
    font-weight: 500;
    color: var(--shortcut-label-color, #374151);
    flex: 1;
  }

  .shortcut-keys {
    display: flex;
    align-items: center;
    gap: 4px;
    margin: 0 12px;

    .key {
      background: var(--key-bg, #ffffff);
      border: 1px solid var(--key-border, #d1d5db);
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 11px;
      font-weight: 600;
      color: var(--key-color, #374151);
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      min-width: 20px;
      text-align: center;
    }

    .key-separator {
      font-size: 10px;
      color: var(--key-separator-color, #9ca3af);
      font-weight: 500;
    }
  }

  .shortcut-description {
    font-size: 11px;
    color: var(--shortcut-description-color, #6b7280);
    font-style: italic;
    flex: 1;
    text-align: right;
  }
}

  .info-content {
    max-height: calc(80vh - 60px);
    overflow-y: auto;
    padding: 20px;

    .info-section {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .info-group {
      background: var(--debug-group-bg, #f9fafb);
      border: 1px solid var(--debug-group-border, #f3f4f6);
      border-radius: 8px;
      padding: 16px;

      h5 {
        margin: 0 0 12px 0;
        font-size: 13px;
        font-weight: 600;
        color: var(--debug-group-title-color, #374151);
        display: flex;
        align-items: center;
        gap: 6px;

        i {
          color: var(--debug-group-icon-color, #6b7280);
          font-size: 12px;
        }
      }

      .info-grid {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px 0;

          .label {
            font-weight: 500;
            color: var(--debug-label-color, #6b7280);
            min-width: 100px;
          }

          .value {
            font-weight: 600;
            color: var(--debug-value-color, #1f2937);
            font-family: 'SF Mono', monospace;
            background: var(--debug-value-bg, #ffffff);
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid var(--debug-value-border, #e5e7eb);
          }
        }
      }
    }

    .refresh-info {
      text-align: center;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid var(--debug-refresh-border, #f3f4f6);

      small {
        color: var(--debug-refresh-color, #9ca3af);
        font-size: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        i {
          color: var(--tip-icon-color, #f59e0b);
        }
      }
    }
  }
}

// ===== LIGHT THEME DEFAULTS =====

:host {
  // Info Ball Light Theme - White/Gray theme for better integration
  .info-ball {
    --info-ball-bg: #ffffff;
    --info-ball-border: #e5e7eb;
    --info-ball-color: #374151;
    --info-ball-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    --info-ball-hover-bg: #f9fafb;
    --info-ball-hover-border: #d1d5db;
    --info-ball-hover-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
    --info-ball-active-bg: #f3f4f6;
    --info-ball-active-border: #9ca3af;
    --info-ball-active-color: #111827;
    --info-ball-active-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
    --info-ball-focus: rgba(59, 130, 246, 0.5);
    --info-ball-inner-highlight: rgba(0, 0, 0, 0.03);
  }
}

// ===== RESPONSIVE DESIGN =====

@media (max-width: 768px) {
  .canvas-shortcuts-panel {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
    bottom: 80px;
  }

  .info-ball {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .canvas-shortcuts-panel {
    width: calc(100vw - 20px);
    right: 10px;
    left: 10px;
    bottom: 70px;
    max-height: 60vh;
  }

  .info-ball-container {
    bottom: 15px;
    right: 15px;
  }

  .info-ball {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .panel-header {
    padding: 16px 20px 12px;

    h4 {
      font-size: 15px;
    }
  }

  .panel-content {
    padding: 0 20px 20px;
  }

  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;

    .shortcut-keys {
      margin: 0;
    }

    .shortcut-description {
      text-align: left;
      margin-top: 4px;
    }
  }
}

// ===== ENHANCED DARK THEME SUPPORT =====

:host-context(.dark-theme) {
  // Info Ball Dark Theme - Black/Gray theme for dark mode
  .info-ball {
    --info-ball-bg: #1f2937;
    --info-ball-border: #374151;
    --info-ball-color: #e5e7eb;
    --info-ball-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    --info-ball-hover-bg: #111827;
    --info-ball-hover-border: #4b5563;
    --info-ball-hover-shadow: 0 6px 24px rgba(0, 0, 0, 0.5);
    --info-ball-active-bg: #0f172a;
    --info-ball-active-border: #6b7280;
    --info-ball-active-color: #f9fafb;
    --info-ball-active-shadow: 0 6px 24px rgba(0, 0, 0, 0.6);
    --info-ball-focus: rgba(59, 130, 246, 0.6);
    --info-ball-inner-highlight: rgba(255, 255, 255, 0.05);
  }

  // Shortcuts Panel Dark Theme
  .canvas-shortcuts-panel {
    --shortcuts-panel-bg: rgba(30, 41, 59, 0.98);
    --shortcuts-panel-border: #334155;
    --panel-header-bg: #1e293b;
    --panel-header-border: #334155;
    --panel-header-color: #f1f5f9;
    --panel-icon-color: #60a5fa;
    --panel-close-color: #94a3b8;
    --panel-close-hover-bg: #334155;
    --panel-close-hover-color: #e2e8f0;
    --panel-focus: rgba(59, 130, 246, 0.5);
    --scrollbar-thumb: #475569;
    --scrollbar-thumb-hover: #64748b;
  }

  // Section Headers Dark Theme
  .section-header h5 {
    --section-header-color: #e2e8f0;
    --section-icon-color: #94a3b8;
  }

  // Shortcut Items Dark Theme
  .shortcut-item {
    --shortcut-item-bg: #1e293b;
    --shortcut-item-border: #334155;
    --shortcut-item-hover-bg: #334155;
    --shortcut-item-hover-border: #475569;
    --shortcut-label-color: #e2e8f0;
    --shortcut-description-color: #94a3b8;
  }

  // Key Styling Dark Theme
  .shortcut-keys .key {
    --key-bg: #0f172a;
    --key-border: #475569;
    --key-color: #e2e8f0;
  }

  .shortcut-keys .key-separator {
    --key-separator-color: #64748b;
  }

  // Legacy Panel Dark Theme (for backward compatibility)
  .canvas-info-panel {
    --debug-panel-bg: rgba(30, 41, 59, 0.98);
    --debug-panel-border: #334155;
    --debug-header-bg: #1e293b;
    --debug-header-border: #334155;
    --debug-header-color: #f1f5f9;
    --debug-icon-color: #60a5fa;
    --debug-collapse-color: #94a3b8;
    --debug-collapse-hover-bg: #334155;
    --debug-collapse-hover-color: #e2e8f0;
    --debug-group-bg: #1e293b;
    --debug-group-border: #334155;
    --debug-group-title-color: #e2e8f0;
    --debug-group-icon-color: #94a3b8;
    --debug-label-color: #94a3b8;
    --debug-value-color: #e2e8f0;
    --debug-value-bg: #0f172a;
    --debug-value-border: #334155;
    --debug-refresh-border: #334155;
    --debug-refresh-color: #64748b;
    --tip-icon-color: #f59e0b;
  }
}




