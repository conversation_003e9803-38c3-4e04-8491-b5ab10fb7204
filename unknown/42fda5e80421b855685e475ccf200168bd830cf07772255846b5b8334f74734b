<div class="suggested-prompt-container">
  <app-card>
    <div class="card-content">
      <h3 class="section-title">Suggested Prompt</h3>

      <!-- Prompt Selection Row -->
      <div class="prompt-selection-row">
        <div class="select-container">
          <app-select-dropdown
            [options]="promptOptions"
            placeholder="Choose Prompt"
            [isMultiSelect]="false"
            (selectionChange)="onPromptChange($event)"
          ></app-select-dropdown>
        </div>

        <button class="edit-button" (click)="onEdit()">
          <span>Edit</span>
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M12 20h9"></path>
            <path
              d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"
            ></path>
          </svg>
        </button>
      </div>

      <!-- Role and Goal -->
      <div class="two-column-row">
        <div class="column">
          <div class="field-container">
            <h4 class="field-title">Role</h4>
            <div class="field-content">
              <p>Senior Ruby Developer</p>
            </div>
          </div>
        </div>

        <div class="column">
          <div class="field-container">
            <h4 class="field-title">Goal</h4>
            <div class="field-content">
              <p>
                Analyze dependencies and create a comprehensive dependency graph
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Backstory -->
      <div class="field-container">
        <div class="field-header">
          <h4 class="field-title">Backstory</h4>
          <button class="enhance-button" (click)="onEnhanceBackstory()">
            <span>Enhance</span>
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"
              ></path>
              <polyline points="15 3 21 3 21 9"></polyline>
              <line x1="10" y1="14" x2="21" y2="3"></line>
            </svg>
          </button>
        </div>
        <div class="field-content">
          <p>
            Understanding the dependency structure of a Rails project is crucial
            for maintaining, optimizing, and scaling the application. A clear
            dependency graph helps developers identify potential bottlenecks,
            circular dependencies, and opportunities for code refactoring,
            ultimately leading to a more efficient and maintainable codebase.
          </p>
        </div>
      </div>

      <!-- Expected Output -->
      <div class="field-container">
        <div class="field-header">
          <h4 class="field-title">Expected Output</h4>
          <button class="enhance-button" (click)="onEnhanceOutput()">
            <span>Enhance</span>
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"
              ></path>
              <polyline points="15 3 21 3 21 9"></polyline>
              <line x1="10" y1="14" x2="21" y2="3"></line>
            </svg>
          </button>
        </div>
        <div class="field-content">
          <p>
            Only a JSON file containing the complete dependency graph for all
            Ruby on Rails files in the project.
          </p>
        </div>
      </div>
    </div>
  </app-card>
</div>
