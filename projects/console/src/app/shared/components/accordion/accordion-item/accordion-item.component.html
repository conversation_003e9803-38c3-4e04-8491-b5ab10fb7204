<div class="accordion-item">
  <div class="accordion-header" (click)="toggleAccordion()">
    <h3 class="accordion-title">{{ title }}</h3>
    <button class="toggle-button" [class.open]="isOpen">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <defs>
          <linearGradient
            id="accordionGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="0%"
          >
            <stop offset="0%" stop-color="var(--button-start-gradient)" />
            <stop offset="100%" stop-color="var(--button-end-gradient)" />
          </linearGradient>
        </defs>
        <polyline
          points="6 9 12 15 18 9"
          stroke="url(#accordionGradient)"
        ></polyline>
      </svg>
    </button>
  </div>
  <div class="accordion-content" [class.open]="isOpen">
    <div class="content-inner">
      <ng-content></ng-content>
    </div>
  </div>
</div>
