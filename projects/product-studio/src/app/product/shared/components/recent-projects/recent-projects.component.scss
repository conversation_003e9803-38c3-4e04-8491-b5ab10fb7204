/* recent-creation.component.scss */
.recent-creation-wrapper {
  display: flex;
  max-width: 1776px;
  width: 100%;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  margin: 0 auto;
  padding: 20px;
}

/* Toggle buttons styling */
.toggle-layout {
  display: flex;
  width: 280px;
  height: 60px;
  padding: 8px;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(7.5px);
  transition: all 0.3s ease;
  border: 1px solid rgba(155, 155, 155, 0.2);
  background: linear-gradient(
    102deg,
    rgba(240, 240, 245, 0.24) 2.05%,
    rgba(240, 240, 245, 0.24) 100%
  );

  &:hover {
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
  }
}

.toogle-container {
  display: flex;
  width: 100%;
  justify-content: space-around;
  align-items: center;
}

.toggle-btn {
  padding: 8px 24px;
  border: none;
  background: transparent;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: opacity 0.3s ease;
  border-radius: 6px;
  position: relative;
  color: #6e7191;

  &.active {
    background: linear-gradient(90deg, #b68bf8 0%, #f96cab 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    transition: none;
  }

  &:hover:not(.active) {
    opacity: 0.8;
  }
}

/* Cards container and grid layout */
.cards-container {
  width: 100%;
  max-width: 1776px;
  overflow: hidden;
  position: relative;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  width: 100%;
  padding: 0 20px;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &.slide-recent {
    transform: translateX(0);
    animation: slideInFromLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &.slide-all {
    transform: translateX(0);
    animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  @media (min-width: 1440px) {
    grid-template-columns: repeat(4, 1fr);
  }

  @media (max-width: 1439px) and (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 1023px) and (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 767px) {
    grid-template-columns: 1fr;
    padding: 0 16px;
  }
}

.card-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 0; // Prevents overflow in grid
  height: 100%;
  transition: all 0.3s ease;
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-20%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(20%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Card content styling */
.card-content {
  display: flex;
  height: 100%;
  min-height: 175px;
  padding: 16px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;
  transition: all 0.2s ease;
  position: relative;

  @media (max-width: 1024px) {
    min-height: 145px;
    padding: 14px;
  }

  @media (max-width: 767px) {
    min-height: 140px;
    padding: 12px;
    gap: 6px;
  }
}

/* Skeleton loader styles */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.skeleton-card {
  pointer-events: none;
}

.skeleton-title {
  width: 70%;
  height: 24px;
  margin-bottom: 12px;
  border-radius: 4px;
  background: linear-gradient(to right, #f0f0f0 8%, #e0e0e0 18%, #f0f0f0 33%);
  background-size: 800px 104px;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-text {
  width: 100%;
  height: 16px;
  margin-bottom: 8px;
  border-radius: 4px;
  background: linear-gradient(to right, #f0f0f0 8%, #e0e0e0 18%, #f0f0f0 33%);
  background-size: 800px 104px;
  animation: shimmer 1.5s infinite linear;

  &:last-of-type {
    width: 80%;
  }
}

.action-timestamp-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  position: absolute;
  bottom: 16px;
  left: 16px;
  right: 16px;
  padding-top: 8px;
}

.card-action-btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60%;

  /* Default styles - will be overridden by theme-specific styles */
  background-color: #fcc1dc;
  color: #33364d;

  &:hover {
    background-color: #f9b0d1;
  }
}

/* Card footer styling */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 2px;
  margin-top: 6px;
}

.creator-name {
  color: var(--recent-creation-heading-text);
  font-size: 14px;
  font-weight: 500;
}

.timestamp {
  color: #a1a1aa;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 40%;
  text-align: right;
}

/* Pagination styling */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
}

.pagination-text {
  color: #a1a1aa;
  font-size: 14px;
}

/* Card styling with ::ng-deep */
:host ::ng-deep awe-cards {
  width: 100%;
  height: 100%;
  display: block;

  &.selected {
    .awe-card {
      border-color: #6366f1 !important;
    }
  }

  .awe-card {
    width: 100%;
    // height: 100%;
    min-height: 150px;
    padding: 0 !important;
    background: transparent !important;
    border: 1px solid var(--recent-creation-card-border-color) !important;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05) !important;
    border-radius: 12px !important;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;

    &:hover {
      border-color: #6366f1 !important;
    }
  }
}

/* Dark theme styling */
:host-context(.dark-theme) {
  /* Dark theme skeleton styles */
  .skeleton-title,
  .skeleton-text,
  .skeleton-button,
  .skeleton-timestamp {
    background: linear-gradient(to right, #333 8%, #444 18%, #333 33%);
    background-size: 800px 104px;
    animation: shimmer 1.5s infinite linear;
  }
  ::ng-deep awe-heading {
    color: #ffffff !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 1 !important;
    line-clamp: 1 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
  }

  ::ng-deep awe-body-text {
    color: #a1a1aa !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .toggle-layout {
    border: 1px solid #292c3d;
    background: linear-gradient(102deg, rgba(20, 27, 31, 0.24) 2.05%, rgba(20, 27, 31, 0.24) 100%);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(7.5px);

    &:hover {
      box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1);
    }
  }

  .toggle-btn {
    color: rgba(255, 255, 255, 0.6);

    &:hover:not(.active) {
      color: rgba(255, 255, 255, 0.8);
    }

    &.active {
      background: linear-gradient(90deg, #b68bf8 0%, #f96cab 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      color: transparent;
      transition: none;
    }
  }

  ::ng-deep awe-cards .awe-card {
    background: linear-gradient(
      102deg,
      rgba(20, 27, 31, 0.24) 2.05%,
      rgba(20, 27, 31, 0.24) 100%
    ) !important;
    border: 2px solid #292c3d !important;
    box-shadow: 0px 2px 4px 0px rgba(20, 22, 31, 0.08) !important;
    backdrop-filter: blur(7.5px);
    transition: all 0.3s ease !important;
  }

  ::ng-deep awe-cards .awe-card:hover {
    border-color: #f63b8f !important;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1) !important;
  }

  .card-content {
    background: transparent;
  }

  .card-action-btn {
    background-color: #33364d;
    color: white;
    font-weight: 500;
    border: none;

    &:hover {
      background-color: #3e4154;
    }
  }

  .creator-name {
    color: #ffffff;
  }

  .timestamp {
    color: #a1a1aa;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 40%;
    text-align: right;
  }
}

/* Light theme styling */
:host-context(.light-theme) {
  ::ng-deep awe-heading {
    color: #14161f !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 1 !important;
    line-clamp: 1 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
  }

  ::ng-deep awe-body-text {
    color: #666d99 !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .toggle-layout {
    border: 1px solid #f0f0f5;
    background: linear-gradient(
      102deg,
      rgba(240, 240, 245, 0.24) 2.05%,
      rgba(240, 240, 245, 0.24) 100%
    );
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(7.5px);
  }

  .toggle-btn {
    color: rgba(20, 22, 31, 0.6);

    &:hover:not(.active) {
      color: rgba(20, 22, 31, 0.8);
    }

    &.active {
      background: linear-gradient(90deg, #b68bf8 0%, #f96cab 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      color: transparent;
      transition: none;
    }
  }

  ::ng-deep awe-cards .awe-card {
    background: linear-gradient(
      102deg,
      rgba(240, 240, 245, 0.24) 2.05%,
      rgba(240, 240, 245, 0.24) 100%
    ) !important;
    border: 2px solid #f0f0f5 !important;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05) !important;
    backdrop-filter: blur(7.5px);
    transition: all 0.3s ease !important;
  }

  ::ng-deep awe-cards .awe-card:hover {
    border-color: #f63b8f !important;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08) !important;
  }

  .card-content {
    background: transparent;
  }

  .card-action-btn {
    background-color: #fcc1dc;
    color: #33364d;
    font-weight: 500;
    border: none;

    &:hover {
      background-color: #f9b0d1;
    }
  }

  .creator-name {
    color: #14161f;
  }

  .timestamp {
    color: #666d99;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 40%;
    text-align: right;
  }
}
@media (hover: hover) {
  button:hover:not(:disabled) {
    transform: none !important;
  }
}

::ng-deep p {
  margin: 0px !important;
}
