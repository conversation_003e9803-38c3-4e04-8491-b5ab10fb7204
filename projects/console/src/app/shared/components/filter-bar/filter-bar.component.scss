.filter-bar {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  gap: 8px;
  margin-bottom: 20px;
  background: var(--glass-bg);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 12px;
  box-shadow: 0 4px 12px var(--glass-shadow);
  padding: 12px;
  border: 1px solid var(--glass-border);
  position: relative;
  z-index: 200;
}

.filter-item {
  position: relative;
  flex: 1;
  background-color: var(--dropdown-bg);
  border: 1px solid var(--dropdown-border);
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 1px 3px var(--glass-shadow);
  
  &:hover {
    background-color: var(--dropdown-hover-bg);
    border-color: var(--dropdown-border);
  }
  
  &.active {
    background-color: var(--dropdown-bg);
    border: 1px solid var(--dropdown-focus-border);
    
    .dropdown-arrow {
      transform: rotate(180deg);
    }
  }
  
  &.selected {
    background-color: var(--dropdown-bg);
    border: 1px solid var(--dropdown-border);
    
    .filter-label span {
      color: var(--dropdown-text);
      font-weight: 500;
    }
  }
}

.filter-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: var(--dropdown-text);
  
  span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
  }
}

.dropdown-arrow {
  margin-left: 6px;
  color: var(--dropdown-text);
  transition: transform 0.2s ease;
}

.clear-button {
  background: none;
  border: none;
  color: var(--dropdown-text);
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0;
  margin-left: 4px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  
  &:hover {
    background-color: var(--dropdown-hover-bg);
    color: var(--dropdown-text);
  }
}

.filter-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  width: 100%;
  background: var(--dropdown-bg);
  border-radius: 8px;
  box-shadow: 0 8px 24px var(--dropdown-shadow);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--dropdown-border);
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-options {
  padding: 6px 0;
}

.dropdown-option {
  padding: 10px 16px;
  font-size: 14px;
  color: var(--dropdown-text);
  cursor: pointer;
  text-align: left;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: var(--dropdown-hover-bg);
  }
  
  &.selected {
    background-color: var(--dropdown-selected-bg);
    color: var(--dropdown-selected-text);
    font-weight: 500;
  }
}

@media (max-width: 1024px) {
  .filter-bar {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
  
  .filter-item {
    border-right: 1px solid var(--dropdown-border);
    border-bottom: 1px solid var(--dropdown-border);
    
    &:nth-child(3n) {
      border-right: none;
    }
    
    &:nth-last-child(-n+3) {
      border-bottom: none;
    }
    
    &:last-child {
      border-bottom: none;
    }
  }
}

@media (max-width: 768px) {
  .filter-bar {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filter-item {
    &:nth-child(3n) {
      border-right: 1px solid var(--dropdown-border);
    }
    
    &:nth-child(2n) {
      border-right: none;
    }
    
    &:nth-last-child(-n+3) {
      border-bottom: 1px solid var(--dropdown-border);
    }
    
    &:nth-last-child(-n+2) {
      border-bottom: none;
    }
    
    &:last-child {
      border-bottom: none;
    }
  }
}

@media (max-width: 480px) {
  .filter-bar {
    grid-template-columns: 1fr;
    padding: 8px;
  }
  
  .filter-item {
    border-right: none;
    border-bottom: 1px solid var(--dropdown-border);
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .filter-dropdown {
    position: fixed;
    top: auto;
    bottom: 0;
    left: 0;
    width: 100%;
    border-radius: 12px 12px 0 0;
    max-height: 50vh;
    animation: slideUp 0.3s ease-out;
  }
  
  @keyframes slideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
} 