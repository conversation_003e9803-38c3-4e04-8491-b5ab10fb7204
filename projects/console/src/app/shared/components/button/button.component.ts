import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-button',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss']
})
export class ButtonComponent {
  @Input() type: 'button' | 'submit' | 'reset' = 'button';
  @Input() variant: 'primary' | 'secondary' | 'outline' | 'text' | 'danger' = 'primary';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() fullWidth: boolean = false;
  @Input() disabled: boolean = false;
  @Input() loading: boolean = false;
  @Input() icon: string = '';
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() ariaLabel: string = '';
  
  @Output() buttonClick = new EventEmitter<MouseEvent>();
  
  /**
   * Handle button click event
   * @param event The mouse event
   */
  onClick(event: MouseEvent): void {
    if (!this.disabled && !this.loading) {
      this.buttonClick.emit(event);
    }
  }
  
  /**
   * Get CSS classes for the button based on inputs
   */
  get buttonClasses(): string {
    return `
      btn 
      btn-${this.variant} 
      btn-${this.size} 
      ${this.fullWidth ? 'btn-full-width' : ''} 
      ${this.loading ? 'btn-loading' : ''} 
      ${this.icon ? 'btn-with-icon' : ''} 
      ${this.icon && !this.iconPosition ? 'icon-left' : 'icon-' + this.iconPosition}
    `;
  }
} 