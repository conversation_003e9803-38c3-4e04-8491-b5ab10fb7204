<div class="create-guardrails-container">
  <form [formGroup]="guardrailForm">
    <div class="form-layout" [ngClass]="{'three-column-layout': isExecuteMode && showChatInterface}">
      <!-- Left Column -->
      <div class="left-column">
        <!-- Guardrail Details Card -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <app-form-field
              label="Guardrail Name"
              id="guardrailName"
              [control]="getControl('name')"
            ></app-form-field>

            <app-form-field
              label="Description"
              id="description"
              [control]="getControl('description')"
              type="textarea"
            ></app-form-field>
          </div>
        </app-card>

        <!-- Assign Filters Card -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <h3 class="section-title">Assign Filters</h3>

            <app-form-field
              label="Organization"
              id="organization"
              [control]="getControl('organization')"
            ></app-form-field>

            <app-form-field
              label="Domain"
              id="domain"
              [control]="getControl('domain')"
            ></app-form-field>

            <app-form-field
              label="Project"
              id="project"
              [control]="getControl('project')"
            ></app-form-field>

            <app-form-field
              label="Team"
              id="team"
              [control]="getControl('team')"
            ></app-form-field>
          </div>
        </app-card>
      </div>

      <!-- Middle Column (previously Right) -->
      <div class="middle-column">
        <!-- Content wrapper to match left column height -->
        <div class="middle-column-content">
          <!-- Code Format Card -->
          <app-card
            customClass="solid-card"
            [noPadding]="false"
            [isClickable]="false"
            [noHoverEffect]="true"
          >
            <div class="card-content">
              <h3 class="section-title">Code Format</h3>

              <div class="code-format-buttons">
                <button
                  type="button"
                  class="format-button"
                  [ngClass]="{ active: selectedCodeFormat === 'Colang' }"
                  (click)="setCodeFormat('Colang')"
                >
                  Colang
                </button>
                <button
                  type="button"
                  class="format-button"
                  [ngClass]="{ active: selectedCodeFormat === 'YML' }"
                  (click)="setCodeFormat('YML')"
                >
                  YML
                </button>
              </div>

              <!-- Code Editor Area -->
              <div class="code-editor-container">
                <app-form-field
                  id="codeContent"
                  [control]="getControl('codeContent')"
                  type="textarea"
                  [placeholder]="
                    'Enter your ' + selectedCodeFormat + ' code here...'
                  "
                  class="code-editor-field"
                ></app-form-field>
              </div>

              <!-- Buttons at bottom of middle column -->
              <div class="middle-column-buttons">
                <button type="button" class="exit-button" (click)="onExit()">
                  Exit
                </button>
                <!-- Show different buttons based on mode -->
                <ng-container *ngIf="!isEditMode">
                  <button type="button" class="save-button" (click)="onSave()">
                    Save
                  </button>
                </ng-container>
                <ng-container *ngIf="isEditMode && !isExecuteMode">
                  <button type="button" class="execute-button" (click)="onExecute()">
                    Execute
                  </button>
                </ng-container>
                <ng-container *ngIf="isEditMode && isExecuteMode">
                  <button type="button" class="execute-button" (click)="onSave()">
                    Update
                  </button>
                </ng-container>
              </div>
            </div>
          </app-card>
        </div>
      </div>

      <!-- Right Column (new) - Chat Interface -->
      <div class="chat-column" *ngIf="isExecuteMode && showChatInterface">
        <div class="chat-column-content">
          <app-card
            customClass="solid-card"
            [noPadding]="false"
            [isClickable]="false"
            [noHoverEffect]="true"
          >
            <div class="card-content">
              <h3 class="section-title">Playground</h3>
              <h4 class="playground-title">Guardrail testing prompt</h4>
              
              <!-- Chat Interface -->
              <div class="chat-container">
                <app-chat-interface
                  [messages]="chatMessages"
                  [isLoading]="isProcessingChat"
                  (messageSent)="handleChatMessage($event)">
                </app-chat-interface>
              </div>
            </div>
          </app-card>
        </div>
      </div>
    </div>
  </form>
</div>
