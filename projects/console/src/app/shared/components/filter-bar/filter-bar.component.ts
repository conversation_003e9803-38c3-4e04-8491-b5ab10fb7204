import { Component, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FilterConfig, FilterGroup, FilterOption } from '../../models/filter.model';

@Component({
  selector: 'app-filter-bar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './filter-bar.component.html',
  styleUrls: ['./filter-bar.component.scss']
})
export class FilterBarComponent implements OnInit {
  @Input() filterConfig!: FilterConfig;
  @Output() filterChange = new EventEmitter<{[key: string]: string}>();
  
  activeFilterId: string | null = null;
  selectedFilters: {[key: string]: string} = {};

  constructor() { }

  ngOnInit(): void {
    // Initialize selected filters from config if any are pre-selected
    this.filterConfig.filterGroups.forEach(group => {
      const selectedOption = group.options.find(option => option.selected);
      if (selectedOption) {
        this.selectedFilters[group.id] = selectedOption.value;
        group.selectedOption = selectedOption;
      }
    });
  }

  toggleDropdown(filterId: string): void {
    this.activeFilterId = this.activeFilterId === filterId ? null : filterId;
  }

  isDropdownOpen(filterId: string): boolean {
    return this.activeFilterId === filterId;
  }

  selectOption(group: FilterGroup, option: FilterOption): void {
    // Update the selected option in the group
    group.selectedOption = option;
    
    // Update the selectedFilters map
    this.selectedFilters[group.id] = option.value;
    
    // Close the dropdown
    this.activeFilterId = null;
    
    // Emit the change event with all current filters
    this.filterChange.emit(this.selectedFilters);
  }

  getLabelForSelectedOption(group: FilterGroup): string {
    return group.selectedOption ? group.selectedOption.label : group.label;
  }

  clearFilter(group: FilterGroup, event: Event): void {
    event.stopPropagation();
    
    // Clear the selected option
    group.selectedOption = null;
    
    // Remove from selectedFilters map
    delete this.selectedFilters[group.id];
    
    // Emit the change event with updated filters
    this.filterChange.emit(this.selectedFilters);
  }

  // Close dropdown when clicking outside
  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    const clickedInsideDropdown = !!target.closest('.filter-dropdown');
    const clickedFilterToggle = !!target.closest('.filter-item');
    
    if (!clickedInsideDropdown && !clickedFilterToggle) {
      this.activeFilterId = null;
    }
  }
} 