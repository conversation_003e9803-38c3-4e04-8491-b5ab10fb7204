import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface StepperStep {
  id: string;
  label: string;
  state: 'completed' | 'active' | 'inactive';
  component?: string;
  route?: string;
  icon?: string;
  // description?: string;
}

@Injectable({
  providedIn: 'root'
})
export class StepperService {
  private readonly steps: StepperStep[] = [
    {
      id: 'understanding',
      label: 'Understanding',
      state: 'active',
      component: 'understanding',
      route: '/product-studio/brainstormer/understanding',
      // description: 'Understand the problem and requirements'
    },
    {
      id: 'persona',
      label: 'User Persona',
      state: 'inactive',
      component: 'persona',
      route: '/product-studio/brainstormer/persona',
      // description: 'Define user personas and target audience'
    },
    {
      id: 'features',
      label: 'Feature List',
      state: 'inactive',
      component: 'features',
      route: '/product-studio/brainstormer/feature-list',
      // description: 'List and prioritize product features'
    },
    {
      id: 'swot',
      label: 'SWOT Analysis',
      state: 'inactive',
      component: 'swot',
      route: '/product-studio/brainstormer/swot',
      // description: 'Analyze strengths, weaknesses, opportunities, and threats'
    },
    {
      id: 'roadmap',
      label: 'Product Roadmap',
      state: 'inactive',
      component: 'roadmap',
      route: '/product-studio/brainstormer/roadmap',
      // description: 'Create product development roadmap'
    }
  ];

  private currentStepIndexSubject = new BehaviorSubject<number>(0);
  private stepsSubject = new BehaviorSubject<StepperStep[]>([...this.steps]);

  constructor() {
    this.updateStepStates();
  }

  // Observables for components to subscribe to
  get currentStepIndex$(): Observable<number> {
    return this.currentStepIndexSubject.asObservable();
  }

  get steps$(): Observable<StepperStep[]> {
    return this.stepsSubject.asObservable();
  }

  get currentStep$(): Observable<StepperStep> {
    return new Observable(observer => {
      this.currentStepIndex$.subscribe(index => {
        observer.next(this.steps[index]);
      });
    });
  }

  // Getters
  get currentStepIndex(): number {
    return this.currentStepIndexSubject.value;
  }

  get currentStep(): StepperStep {
    return this.steps[this.currentStepIndex];
  }

  get allSteps(): StepperStep[] {
    return [...this.steps];
  }

  get totalSteps(): number {
    return this.steps.length;
  }

  // Navigation methods
  nextStep(): boolean {
    if (this.canGoNext()) {
      this.currentStepIndexSubject.next(this.currentStepIndex + 1);
      this.updateStepStates();
      return true;
    }
    return false;
  }

  previousStep(): boolean {
    if (this.canGoPrevious()) {
      this.currentStepIndexSubject.next(this.currentStepIndex - 1);
      this.updateStepStates();
      return true;
    }
    return false;
  }

  goToStep(index: number): boolean {
    if (index >= 0 && index < this.steps.length) {
      this.currentStepIndexSubject.next(index);
      this.updateStepStates();
      return true;
    }
    return false;
  }

  goToStepById(stepId: string): boolean {
    const index = this.steps.findIndex(step => step.id === stepId);
    return this.goToStep(index);
  }

  // Validation methods
  canGoNext(): boolean {
    return this.currentStepIndex < this.steps.length - 1;
  }

  canGoPrevious(): boolean {
    return this.currentStepIndex > 0;
  }

  // Step state management
  markStepAsCompleted(index: number): void {
    if (index >= 0 && index < this.steps.length) {
      this.steps[index].state = 'completed';
      this.stepsSubject.next([...this.steps]);
    }
  }

  markCurrentStepAsCompleted(): void {
    this.markStepAsCompleted(this.currentStepIndex);
  }

  resetSteps(): void {
    this.steps.forEach((step, index) => {
      step.state = index === 0 ? 'active' : 'inactive';
    });
    this.currentStepIndexSubject.next(0);
    this.stepsSubject.next([...this.steps]);
  }

  private updateStepStates(): void {
    this.steps.forEach((step, index) => {
      if (index < this.currentStepIndex) {
        step.state = 'completed';
      } else if (index === this.currentStepIndex) {
        step.state = 'active';
      } else {
        step.state = 'inactive';
      }
    });
    this.stepsSubject.next([...this.steps]);
  }

  // Utility methods
  getStepByIndex(index: number): StepperStep | null {
    return this.steps[index] || null;
  }

  getStepById(stepId: string): StepperStep | null {
    return this.steps.find(step => step.id === stepId) || null;
  }

  getStepIndexById(stepId: string): number {
    return this.steps.findIndex(step => step.id === stepId);
  }

  isFirstStep(): boolean {
    return this.currentStepIndex === 0;
  }

  isLastStep(): boolean {
    return this.currentStepIndex === this.steps.length - 1;
  }

  getProgress(): number {
    return Math.round(((this.currentStepIndex + 1) / this.steps.length) * 100);
  }
}
