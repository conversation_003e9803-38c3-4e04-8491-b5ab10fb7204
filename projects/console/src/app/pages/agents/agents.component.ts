import { Component, OnInit } from '@angular/core';
import { CommonModule } from "@angular/common";
import { Router } from '@angular/router';
import SearchBar from '../../shared/components/search-bar/search-bar.component';
import { CreateCardComponent } from '../../shared/components/create-card/create-card.component';
import { DataCardComponent } from '../../shared/components/data-card/data-card.component';
import { CardData } from '../../shared/models/card.model';
import { MOCK_AGENTS } from '../../shared/mock-data/agent-mock-data';
import { FilterBarComponent } from '../../shared/components/filter-bar/filter-bar.component';
import { FilterService } from '../../shared/services/filter.service';
import { FilterConfig } from '../../shared/models/filter.model';
import { PageFooterComponent } from '../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../shared/services/pagination.service';

@Component({
  selector: 'app-agents',
  standalone: true,
  imports: [
    CommonModule, 
    SearchBar, 
    CreateCardComponent, 
    DataCardComponent, 
    FilterBarComponent,
    PageFooterComponent
  ],
  templateUrl: './agents.component.html',
  styleUrl: './agents.component.scss'
})
export class AgentsComponent implements OnInit {
  // Get agents from mock data
  allAgents: CardData[] = MOCK_AGENTS;
  filteredAgents: CardData[] = [];
  displayedAgents: CardData[] = [];
  agentFilterConfig!: FilterConfig;
  
  // Track filter bar visibility
  isFilterBarVisible: boolean = false;
  
  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 12; // Show 12 cards per page
  totalPages: number = 1;
  
  // Map filter IDs to CardData properties
  private filterPropertyMap: {[key: string]: string} = {
    'agentType': 'userType',
    'organization': 'client',
    'domain': 'department',
    'project': 'role',
    'team': 'project',
    'relevance': 'createdDate'
  };

  constructor(
    private filterService: FilterService,
    private paginationService: PaginationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Get the filter configuration for agents
    this.agentFilterConfig = this.filterService.getFilterConfig('agents');
    this.filteredAgents = [...this.allAgents];
    this.updateDisplayedAgents();
  }

  updateDisplayedAgents(): void {
    // Use the pagination service to get displayed items and total pages
    const result = this.paginationService.getPaginatedItems(
      this.filteredAgents,
      this.currentPage,
      this.itemsPerPage
    );
    
    this.displayedAgents = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreateAgent(): void {
    console.log('Create Agent clicked');
    this.router.navigate(['/launch/agents/create']);
  }

  onCardClicked(agentId: string): void {
    console.log(`Agent card clicked: ${agentId}`);
    
    // Find the agent to determine its type
    const agent = this.allAgents.find(a => a.id === agentId);
    if (agent) {
      // Check if it's a collaborative agent
      const isCollaborative = agent.tags.some(tag => tag.label === 'Collaborative');
      
      // Navigate to the appropriate agent page
      const route = isCollaborative ? '/launch/agents/collaborative' : '/launch/agents/individual';
      this.router.navigate([route], {
        queryParams: { id: agentId }
      });
    }
  }

  onActionClicked(event: {action: string, cardId: string}): void {
    console.log(`Action ${event.action} clicked for agent: ${event.cardId}`);
    
    if (event.action === 'execute') {
      // Find the agent to determine its type
      const agent = this.allAgents.find(a => a.id === event.cardId);
      if (agent) {
        // Check if it's a collaborative agent
        const isCollaborative = agent.tags.some(tag => tag.label === 'Collaborative');
        
        // Navigate to the appropriate agent page in execute mode
        const route = isCollaborative ? '/launch/agents/collaborative' : '/launch/agents/individual';
        this.router.navigate([route], {
          queryParams: { 
            id: event.cardId,
            execute: 'true'
          }
        });
      }
    } else if (event.action === 'clone') {
      // Handle clone action
      console.log(`Cloning agent: ${event.cardId}`);
    } else if (event.action === 'delete') {
      // Handle delete action
      console.log(`Deleting agent: ${event.cardId}`);
    }
  }

  toggleFilterBar(): void {
    this.isFilterBarVisible = !this.isFilterBarVisible;
    console.log('Filter bar visibility:', this.isFilterBarVisible);
  }

  onFilterChange(filters: {[key: string]: string}): void {
    // Apply filters to agents
    if (Object.keys(filters).length === 0) {
      this.filteredAgents = [...this.allAgents];
    } else {
      this.filteredAgents = this.filterService.filterData(
        this.allAgents, 
        filters, 
        this.filterPropertyMap
      );
    }
    
    // Reset to first page when filters change
    this.currentPage = 1;
    this.updateDisplayedAgents();
    
    console.log('Applied filters:', filters);
    console.log('Filtered agents count:', this.filteredAgents.length);
  }
  
  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedAgents();
  }
  
  // Helper to check if we should show the create card (only on first page)
  get showCreateCard(): boolean {
    return this.currentPage === 1;
  }
}
