@import '../../../shared/styles/header.scss';
@import '../../../shared/styles/cards.scss';

.prompts-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 15px 0;
  position: relative;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
  overflow: hidden;
}

.prompts-header {
  @extend .component-header;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 24px;
  position: relative;
  z-index: 100;
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 300px);
  padding-bottom: 20px;
}

app-create-card, app-data-card {
  height: 100%;
  display: block;
}

@media (max-width: 1200px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 992px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 576px) {
  .cards-container {
    grid-template-columns: 1fr;
  }
}

.no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 20px;
  background-color: var(--glass-bg);
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  color: var(--dashboard-text-secondary);
  font-size: 16px;
}

app-page-footer {
  position: sticky;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 200;
  margin-top: auto;
  flex-shrink: 0;
}
