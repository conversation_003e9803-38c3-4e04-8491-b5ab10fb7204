import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  NewPollingResponse,
  NewPollingResponseWithPrevMetadata,
  MetadataItem,
  RefCodeMetadata,
  ProgressState,
  StatusType,
  ArtifactData,
  DesignTokensData,
  FileData,
  ProjectInfo,
  HistoryItem
} from '../models/polling-response.interface';
import { StepperState, StepperStateDisplayTitles } from '../models/stepper-states.enum';
import { createLogger } from '../utils/logger';

/**
 * Service to process new polling API response format and provide state-based UI data
 */
@Injectable({
  providedIn: 'root'
})
export class NewPollingResponseProcessorService {
  private logger = createLogger('NewPollingResponseProcessor');

  // Subjects for different UI components
  private currentProgressSubject = new BehaviorSubject<ProgressState | null>(null);
  private currentStatusSubject = new BehaviorSubject<StatusType | null>(null);
  private progressDescriptionSubject = new BehaviorSubject<string>('');
  private logContentSubject = new BehaviorSubject<string>('');
  private artifactDataSubject = new BehaviorSubject<any>(null);
  private fileListSubject = new BehaviorSubject<string[]>([]);
  private codeFilesSubject = new BehaviorSubject<FileData[]>([]);
  private previewUrlSubject = new BehaviorSubject<string>('');
  private projectInfoSubject = new BehaviorSubject<ProjectInfo | null>(null);

  // Duplicate prevention for artifacts
  private processedArtifacts = new Set<string>();

  // Duplicate prevention for logs
  private processedLogs = new Set<string>();
  private accumulatedLogContent = '';

  // State change tracking for logs - only show logs when progress state changes
  private lastProcessedProgress: ProgressState | null = null;
  private lastProcessedStatus: StatusType | null = null;
  private processedLogStates = new Set<string>();
  private processedArtifactStates = new Set<string>(); // Track processed artifacts to prevent duplicates // Track processed state combinations

  // Store last processed response for fallback operations
  private lastProcessedResponse: NewPollingResponseWithPrevMetadata | null = null;

  // ENHANCED: Layout storage for delayed display
  private storedLayoutKey: string | null = null;
  private storedLayoutData: any = null;

  // Observables for components to subscribe to
  public currentProgress$ = this.currentProgressSubject.asObservable();
  public currentStatus$ = this.currentStatusSubject.asObservable();
  public progressDescription$ = this.progressDescriptionSubject.asObservable();
  public logContent$ = this.logContentSubject.asObservable();
  public artifactData$ = this.artifactDataSubject.asObservable();
  public fileList$ = this.fileListSubject.asObservable();
  public codeFiles$ = this.codeFilesSubject.asObservable();
  public previewUrl$ = this.previewUrlSubject.asObservable();
  projectInfo$ = this.projectInfoSubject.asObservable();

  constructor() {}

  /**
   * Process a new polling response and update all relevant subjects
   * FIXED: Added URL extraction for DEPLOY+COMPLETED states
   */
  processResponse(response: NewPollingResponse): void {

    this.logger.info('Processing new polling response (nested format):', response);

    // Check if this response has prev_metadata (cast to extended type)
    const responseWithPrevMetadata = response as any;
    if (responseWithPrevMetadata.prev_metadata && Array.isArray(responseWithPrevMetadata.prev_metadata)) {

      this.processNewWorkflowResponse(responseWithPrevMetadata);
      return; // Use the enhanced processor for prev_metadata handling
    }

    // Check if this is a state change before processing logs
    const isStateChange = this.isProgressStateChange(response.progress as ProgressState, response.status);

    // Update basic progress information
    this.currentProgressSubject.next(response.progress as ProgressState);
    this.currentStatusSubject.next(response.status);
    this.progressDescriptionSubject.next(response.progress_description);

    // Extract and process project information
    this.processProjectInfo(response);

    // Process log content ONLY from direct response (not from metadata)
    if (response.log && response.log.trim() !== '') {

      this.processDirectLogContent(response.log);
    }

    // Process metadata based on progress state

    this.processMetadata(response);

    // **CRITICAL FIX**: Add URL extraction for DEPLOY+COMPLETED states
    // This was missing from the nested format processing
    if (response.progress === 'DEPLOY' && response.status === 'COMPLETED') {

      this.logger.info('DEPLOY+COMPLETED detected in nested format, processing URL extraction');
      this.processDeployedPreviewNew(response.metadata || []);
    }

    this.logger.info('Response processing completed (nested format)');
  }

  /**
   * Enhanced polling response processor for the new API structure
   * Handles the exact structure as specified in requirements
   */
  processEnhancedResponse(response: NewPollingResponse): void {
    // this.logger.info('Processing enhanced polling response:', response);

    // Check if this is a state change before processing logs
    const isStateChange = this.isProgressStateChange(response.progress as ProgressState, response.status);

    // Update basic progress information
    this.currentProgressSubject.next(response.progress as ProgressState);
    this.currentStatusSubject.next(response.status);
    this.progressDescriptionSubject.next(response.progress_description);

    // Process log content ONLY from direct response (not from metadata)
    if (response.log && response.log.trim() !== '') {

      this.processDirectLogContent(response.log);
    }

    // Process metadata based on progress state with enhanced logic
    this.processEnhancedMetadata(response);

    this.processNewWorkflowResponse(response);

    // Extract and process project information
    this.processProjectInfo(response);

    // this.logger.info('Enhanced response processing completed');
  }

  /**
   * Process response with new workflow requirements
   * Handles metadata retrieval from history and prev_metadata for missed states
   */
  processNewWorkflowResponse(response: NewPollingResponseWithPrevMetadata): void {

    this.logger.info('Processing new workflow response:', response);
    this.logger.info('🔍 Response details:', {
      progress: response.progress,
      status: response.status,
      metadataCount: response.metadata?.length || 0,
      metadataTypes: response.metadata?.map(m => m.type) || []
    });

    // Store the response for fallback operations
    this.lastProcessedResponse = response;

    // CRITICAL DEBUG: Check if this will be processed
    const currentProgress = response.progress as ProgressState;
    const currentStatus = response.status;
    const willProcess = this.isProgressStateChange(currentProgress, currentStatus);

    this.logger.debug('Processing decision', {
      willProcess: willProcess,
      currentProgress: currentProgress,
      currentStatus: currentStatus
    });

    if (!willProcess) {
      this.logger.debug('Response will be skipped - state change check failed, URL extraction will not run');
    }

    // Update basic progress information
    this.currentProgressSubject.next(response.progress as ProgressState);
    this.currentStatusSubject.next(response.status);
    this.progressDescriptionSubject.next(response.progress_description);

    // ENHANCED: Check if stored layout should be processed
    this.processStoredLayoutIfReady(response.progress);

    // Process log content ONLY from direct response (not from metadata)
    if (response.log && response.log.trim() !== '') {

      this.processDirectLogContent(response.log);
    }

    // Process metadata with enhanced logic including history and prev_metadata
    this.processNewWorkflowMetadata(response);

    // Extract and process project information
    this.processProjectInfo(response);

    this.logger.info('New workflow response processing completed');
  }

  /**
   * Process log content ONLY from direct response (not from metadata)
   * This ensures logs come only from response.log and not from any metadata sources
   * UPDATED: Only process direct response logs to prevent duplicates from metadata
   * ENHANCED: Filter out empty actions and redundant entries
   */
  private processDirectLogContent(logContent: string): void {
    if (logContent && logContent.trim() !== '') {

      // Generate unique key for this log content
      const logKey = this.generateLogKey(logContent);

      // Check if this log has already been processed
      if (this.processedLogs.has(logKey)) {

        return;
      }

      // Mark this log as processed
      this.processedLogs.add(logKey);

      // Split new log into individual lines and filter meaningful content
      const newLogLines = logContent.split('\n').filter(line => line.trim() !== '');
      const filteredLines = this.filterMeaningfulLogLines(newLogLines);
      const uniqueNewLines: string[] = [];

      // Check each filtered line against our accumulated content
      filteredLines.forEach(line => {
        const trimmedLine = line.trim();
        if (trimmedLine && !this.accumulatedLogContent.includes(trimmedLine)) {
          uniqueNewLines.push(line);
        }
      });

      if (uniqueNewLines.length > 0) {
        // Add only unique, meaningful lines to accumulated content
        const newContent = uniqueNewLines.join('\n');
        this.accumulatedLogContent = this.accumulatedLogContent
          ? `${this.accumulatedLogContent}\n${newContent}`
          : newContent;

        this.logContentSubject.next(this.accumulatedLogContent);
        this.logger.info('✅ Direct log content updated with deduplication and filtering');
      } else {

      }
    }
  }

  /**
   * Filter log lines to remove empty actions and redundant entries
   * This ensures only meaningful log content is displayed
   */
  private filterMeaningfulLogLines(logLines: string[]): string[] {
    const meaningfulLines: string[] = [];
    let lastActionLine = '';

    for (let i = 0; i < logLines.length; i++) {
      const line = logLines[i].trim();

      // Skip completely empty lines
      if (!line) {
        continue;
      }

      // Check if this is an "Action:" line
      if (line.startsWith('Action:')) {
        const actionContent = line.substring(7).trim(); // Remove "Action:" prefix

        // Skip if it's just "Action:" with no content
        if (!actionContent) {

          continue;
        }

        // Skip if it's the same as the last action line (duplicate)
        if (line === lastActionLine) {

          continue;
        }

        lastActionLine = line;
      }

      // Check for other redundant patterns
      if (this.isRedundantLogLine(line)) {

        continue;
      }

      // Add meaningful line
      meaningfulLines.push(logLines[i]); // Keep original formatting

    }

    return meaningfulLines;
  }

  /**
   * Check if a log line is redundant and should be filtered out
   */
  private isRedundantLogLine(line: string): boolean {
    const trimmedLine = line.trim().toLowerCase();

    // Filter out common redundant patterns
    const redundantPatterns = [
      /^action:\s*$/, // Empty action lines
      /^agent:\s*code agent\s*\|\s*understanding the project requirements\.\s*$/, // Repetitive agent lines
      /^action:\s*understanding the project requirements\.\s*$/ // Repetitive action lines
    ];

    return redundantPatterns.some(pattern => pattern.test(trimmedLine));
  }

  /**
   * Generate a unique key for log content deduplication
   * Uses log content hash to create a unique identifier
   */
  private generateLogKey(logContent: string): string {
    // Create a simple hash of the log content for uniqueness
    const logHash = this.simpleHash(logContent);
    return `log_${logHash}`;
  }

  /**
   * Check if the current response represents a progress state change
   * Only return true if the progress or status has actually changed
   * FIXED: Allow DEPLOY+COMPLETED to be processed multiple times for URL extraction
   */
  private isProgressStateChange(currentProgress: ProgressState, currentStatus: StatusType): boolean {
    const stateKey = `${currentProgress}-${currentStatus}`;

    this.logger.debug('State change check', {
      currentState: stateKey,
      alreadyProcessed: this.processedLogStates.has(stateKey),
      isDeployCompleted: currentProgress === 'DEPLOY' && currentStatus === 'COMPLETED'
    });

    // CRITICAL FIX: Always allow DEPLOY+COMPLETED to be processed for URL extraction
    // This ensures URL extraction works even if the state was processed before
    if (currentProgress === 'DEPLOY' && currentStatus === 'COMPLETED') {
      this.logger.info('🎯 DEPLOY+COMPLETED state - allowing processing for URL extraction');
      return true;
    }

    // Check if we've already processed this exact state combination
    if (this.processedLogStates.has(stateKey)) {
      this.logger.debug('State already processed - skipping');
      return false;
    }

    // Check if progress or status has changed from last processed
    const progressChanged = this.lastProcessedProgress !== currentProgress;
    const statusChanged = this.lastProcessedStatus !== currentStatus;

    if (progressChanged || statusChanged) {
      // Update tracking variables
      this.lastProcessedProgress = currentProgress;
      this.lastProcessedStatus = currentStatus;
      this.processedLogStates.add(stateKey);

      this.logger.info(`State change detected: Progress: ${this.lastProcessedProgress} -> ${currentProgress}, Status: ${this.lastProcessedStatus} -> ${currentStatus}`);
      return true;
    }

    this.logger.debug('No state change - skipping');
    return false;
  }

  /**
   * Extract project information from ref_code metadata
   * Updated to only extract project name from project-overview state, not from DEPLOY state
   */
  private processProjectInfo(response: NewPollingResponse): void {
    // Only process project info from project-overview state, not from DEPLOY state
    const currentProgress = response.progress as ProgressState;
    if (currentProgress !== 'OVERVIEW' && currentProgress.toUpperCase() !== 'OVERVIEW') {
      // this.logger.info('Skipping project info processing for non-overview state:', currentProgress);
      return;
    }

    // this.logger.info('Processing project info from project-overview metadata');

    const refCodeMetadata = response.metadata.find(m => m.type === 'ref_code');
    if (refCodeMetadata && typeof refCodeMetadata.data === 'string') {
      const refCode = refCodeMetadata.data;
      // this.logger.info('Found ref_code metadata in project-overview:', refCode);

      // Extract project name from ref_code
      let projectName = 'Unknown Project';
      let projectUrl = '';
      let projectId = '';

      if (refCode.includes('http')) {
        // If ref_code is a URL, extract project name from URL
        try {
          const url = new URL(refCode);
          const hostname = url.hostname;
          const subdomain = hostname.split('.')[0];
          projectName = this.formatProjectName(subdomain);
          projectUrl = refCode;
          projectId = subdomain;
        } catch (error) {
          // this.logger.warn('Failed to parse ref_code URL:', error);
        }
      } else {
        // In the new format, ref_code directly contains the project name
        // Use it as is, just clean it up if needed
        projectName = refCode.trim();
        projectId = refCode.toLowerCase().replace(/[^a-z0-9]/g, '-');

        // If the project name looks like it needs formatting, format it
        if (projectName.includes('-') || projectName.includes('_')) {
          projectName = this.formatProjectName(projectName);
        }
      }

      const projectInfo: ProjectInfo = {
        name: projectName,
        url: projectUrl,
        id: projectId
      };

      this.projectInfoSubject.next(projectInfo);
      // this.logger.info('Project info extracted from project-overview:', projectInfo);
    } else {
      // this.logger.info('No ref_code metadata found in project-overview state');
    }
  }

  /**
   * Format project name from ref_code string
   */
  private formatProjectName(refCode: string): string {
    return refCode
      .replace(/[-_]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .trim();
  }

  /**
   * Process log content based on the current progress state and available metadata
   */
  private processLogContent(response: NewPollingResponse): void {
    let logContent = response.log;

    // Add file names to logs if available
    const fileNamesMetadata = response.metadata.find(m => m.type === 'fileNames');
    if (fileNamesMetadata) {
      try {
        const fileList = typeof fileNamesMetadata.data === 'string'
          ? JSON.parse(fileNamesMetadata.data)
          : fileNamesMetadata.data;

        if (Array.isArray(fileList)) {
          logContent += `\n\nGenerated files:\n${fileList.map(file => `- ${file}`).join('\n')}`;
          this.fileListSubject.next(fileList);
        }
      } catch (error) {
        // this.logger.warn('Failed to parse fileNames metadata:', error);
      }
    }

    // Add intermediate code snippets to logs if available
    const filesMetadata = response.metadata.find(m => m.type === 'files');
    if (filesMetadata) {
      try {
        const files = typeof filesMetadata.data === 'string'
          ? JSON.parse(filesMetadata.data)
          : filesMetadata.data;

        if (Array.isArray(files)) {
          // For IN_PROGRESS and FAILED states, show code snippets in logs
          if (response.status === 'IN_PROGRESS' || response.status === 'FAILED') {
            logContent += '\n\nCode snippets:\n';
            files.forEach(file => {
              const fileName = file.fileName || file.path || 'Unknown file';
              const code = file.code || '';
              logContent += `\n--- ${fileName} ---\n${code.substring(0, 200)}${code.length > 200 ? '...' : ''}\n`;
            });
          }

          // For COMPLETED state, prepare files for code viewer
          if (response.status === 'COMPLETED') {
            const fileData: FileData[] = files.map(file => ({
              path: file.fileName || file.path || 'Unknown file',
              code: file.code || ''
            }));
            this.codeFilesSubject.next(fileData);
          }
        }
      } catch (error) {
        // this.logger.warn('Failed to parse files metadata:', error);
      }
    }

    this.logContentSubject.next(logContent);
  }

  /**
   * Enhanced log content processing for the new API structure
   * Handles log messages, fileNames, and intermediate code files separately
   */
  private processEnhancedLogContent(response: NewPollingResponse): void {
    let logContent = response.log;

    // Add fileNames to logs if available (as specified in requirements)
    const fileNamesMetadata = response.metadata.find(m => m.type === 'fileNames');
    if (fileNamesMetadata) {
      try {
        const fileList = typeof fileNamesMetadata.data === 'string'
          ? JSON.parse(fileNamesMetadata.data)
          : fileNamesMetadata.data;

        if (Array.isArray(fileList)) {
          logContent += `\n\n📁 Generated files:\n${fileList.map(file => `  • ${file}`).join('\n')}`;
          this.fileListSubject.next(fileList);
        }
      } catch (error) {
        // this.logger.warn('Failed to parse fileNames metadata:', error);
      }
    }

    // Add intermediate code files to logs for IN_PROGRESS and FAILED states
    const filesMetadata = response.metadata.find(m => m.type === 'files');
    if (filesMetadata && (response.status === 'IN_PROGRESS' || response.status === 'FAILED')) {
      try {
        const files = typeof filesMetadata.data === 'string'
          ? JSON.parse(filesMetadata.data)
          : filesMetadata.data;

        if (Array.isArray(files)) {
          logContent += '\n\n💻 Code snippets:\n';
          files.forEach(file => {
            const fileName = file.fileName || file.path || 'Unknown file';
            const code = file.code || '';
            logContent += `\n--- ${fileName} ---\n${code.substring(0, 200)}${code.length > 200 ? '...' : ''}\n`;
          });
        }
      } catch (error) {
        // this.logger.warn('Failed to parse files metadata for logs:', error);
      }
    }

    // For COMPLETED state, prepare files for code viewer instead of logs
    if (filesMetadata && response.status === 'COMPLETED') {
      try {
        const files = typeof filesMetadata.data === 'string'
          ? JSON.parse(filesMetadata.data)
          : filesMetadata.data;

        if (Array.isArray(files)) {
          const fileData: FileData[] = files.map(file => ({
            path: file.fileName || file.path || 'Unknown file',
            code: file.code || ''
          }));
          this.codeFilesSubject.next(fileData);
          // this.logger.info('Code files prepared for code viewer:', fileData.length, 'files');
        }
      } catch (error) {
        // this.logger.warn('Failed to parse files metadata for code viewer:', error);
      }
    }

    this.logContentSubject.next(logContent);
  }

  /**
   * Process metadata based on progress state
   */
  private processMetadata(response: NewPollingResponse): void {
    const progress = response.progress as ProgressState;
    const progressUpper = progress.toUpperCase();

    switch (progressUpper) {
      case 'OVERVIEW':
      case 'PROJECT_OVERVIEW':
        this.processProjectOverviewMetadata(response.metadata);
        break;
      case 'LAYOUT_ANALYZED':
        this.processLayoutAnalyzedMetadata(response.metadata);
        break;
      case 'DESIGN_SYSTEM_MAPPED':
      case 'DESIGN_SYSTEM_ANALYZED':
        this.processDesignSystemMetadata(response.metadata);
        break;
      case 'DEPLOYED':
        this.processDeployedMetadata(response.metadata);
        break;
      case 'BUILD':
        this.processBuildMetadata(response.metadata, response.status);
        break;
      case 'SEED_PROJECT_INITIALIZED':
      case 'FILE_QUEUE':
      case 'COMPONENTS_CREATED':
      case 'PAGES_GENERATED':
      case 'BUILD_STARTED':
      case 'BUILD_SUCCEEDED':
      case 'BUILD_FAILED':
      case 'FILES_GENERATED':
      case 'COMPLETED':
        this.processGeneralMetadata(response.metadata);
        break;
      default:
        // Handle legacy states
        const progressLower = progress.toLowerCase();
        if (progressLower.includes('project-overview') || progressLower.includes('overview')) {
          this.processProjectOverviewMetadata(response.metadata);
        } else if (progressLower.includes('layout') && progressLower.includes('analyzed')) {
          this.processLayoutAnalyzedMetadata(response.metadata);
        } else if (progressLower.includes('design') && progressLower.includes('system')) {
          this.processDesignSystemMetadata(response.metadata);
        } else if (progressLower.includes('deployed')) {
          this.processDeployedMetadata(response.metadata);
        } else {
          this.processGeneralMetadata(response.metadata);
        }
    }
  }

  /**
   * Enhanced metadata processing for the new API structure
   * Handles the exact requirements for each progress state
   */
  private processEnhancedMetadata(response: NewPollingResponse): void {
    const progress = response.progress as ProgressState;
    const status = response.status;

    this.logger.info(`Processing enhanced metadata for progress: ${progress}, status: ${status}`);

    // Handle artifact data for specific states when COMPLETED
    if (status === 'COMPLETED') {
      switch (progress) {
        case 'project-overview':
          this.processProjectOverviewArtifactEnhanced(response.metadata);
          break;
        case 'Layout Analyzed':
          this.processLayoutAnalyzedArtifactEnhanced(response.metadata);
          break;
        case 'Design_System Analyzed':
          this.processDesignSystemArtifactEnhanced(response.metadata);
          break;
        case 'DEPLOYED':
          this.processDeployedArtifactEnhanced(response.metadata);
          break;
        default:
          // Handle uppercase variants
          const progressUpper = progress.toUpperCase();
          if (progressUpper === 'OVERVIEW') {
            this.processProjectOverviewArtifactEnhanced(response.metadata);
          } else if (progressUpper === 'LAYOUT_ANALYZED') {
            this.processLayoutAnalyzedArtifactEnhanced(response.metadata);
          } else if (progressUpper === 'DESIGN_SYSTEM_MAPPED') {
            this.processDesignSystemArtifactEnhanced(response.metadata);
          } else if (progressUpper === 'DEPLOYED') {
            this.processDeployedArtifactEnhanced(response.metadata);
          }
          break;
      }
    }

    // Always process files metadata for code viewer when status is COMPLETED
    if (status === 'COMPLETED') {
      this.processFilesForCodeViewerEnhanced(response.metadata);
    }

    // Always process preview URL for deployed state when COMPLETED
    if ((progress === 'DEPLOYED' || progress.toUpperCase() === 'DEPLOYED') && status === 'COMPLETED') {
      this.processPreviewUrlEnhanced(response.metadata);
    }
  }

  /**
   * Process metadata with new workflow requirements
   * Handles metadata retrieval from history and prev_metadata for missed states
   */
  private processNewWorkflowMetadata(response: NewPollingResponseWithPrevMetadata): void {
    const progress = response.progress as ProgressState;
    const status = response.status;

    this.logger.info(`Processing new workflow metadata for progress: ${progress}, status: ${status}`);

    // Get metadata for current state
    let currentMetadata = response.metadata || [];

    // Check if we need to retrieve metadata from prev_metadata or history
    const retrievedMetadata = this.retrieveMissedMetadata(response, progress, status);
    if (retrievedMetadata.length > 0) {

      this.logger.info('Retrieved missed metadata:', retrievedMetadata.length, 'items');
      currentMetadata = [...currentMetadata, ...retrievedMetadata];
    }

    // ENHANCED: Process artifacts with priority: prev_metadata FIRST, then history if needed

    this.processArtifactsWithPriority(response);

    // Process metadata based on the new workflow requirements

    this.processMetadataByNewWorkflow(progress, status, currentMetadata);
  }

  /**
   * Process metadata based on new workflow requirements with enhanced artifact parsing
   */
  private processMetadataByNewWorkflow(progress: ProgressState, status: StatusType, metadata: MetadataItem[]): void {
    this.logger.info(`Processing metadata by new workflow for ${progress} - ${status}:`, metadata);

    // Process based on progress state and status
    switch (progress) {
      case 'OVERVIEW':
        if (status === 'COMPLETED') {
          this.processProjectOverviewArtifactNew(metadata);
        }
        break;
      case 'SEED_PROJECT_INITIALIZED':
      case 'FILE_QUEUE':
      case 'COMPONENTS_CREATED':
      case 'PAGES_GENERATED':
        // These states don't have metadata artifacts according to requirements
        // this.logger.info(`State ${progress} does not have artifact metadata`);
        break;
      case 'Design_System Analyzed':
        // Always show the dummy design system page for Design_System Analyzed state regardless of status
        this.logger.info('Design_System Analyzed state detected - showing dummy design system page');
        this.processDesignSystemFallback();
        break;
      case 'DESIGN_SYSTEM_MAPPED':
        // Always show the dummy design system page for DESIGN_SYSTEM_MAPPED state regardless of status
        this.logger.info('DESIGN_SYSTEM_MAPPED state detected - showing dummy design system page');
        this.processDesignSystemFallback();
        break;
      case 'Layout Analyzed':
      case 'LAYOUT_ANALYZED':
        if (status === 'COMPLETED') {
          this.logger.info('🎯 LAYOUT_ANALYZED state detected - processing layout artifact');
          this.processLayoutAnalyzedArtifactNew(metadata);
        }
        break;
      case 'BUILD':
        if (status === 'IN_PROGRESS' || status === 'COMPLETED') {
          this.processBuildCodeFilesNew(metadata);
        }
        break;
      case 'DEPLOY':
        this.logger.info('🎯 DEPLOY state detected with status:', status);
        this.logger.info('📋 Metadata available:', metadata.length, 'items');

        if (status === 'COMPLETED') {
          this.logger.info('✅ DEPLOY + COMPLETED detected - processing deployed preview');
          this.clearPreviousDeploymentState();
          this.processDeployedPreviewNew(metadata);
        } else if (status === 'FAILED') {
          this.logger.info('❌ DEPLOY + FAILED detected - processing failed deployment');
          this.processFailedDeployment();
        } else {
          this.logger.info('⏳ DEPLOY state with status:', status, '- not processing preview yet');
        }
        break;
      default:
        // this.logger.warn(`Unknown progress state for new workflow: ${progress}`);
    }
  }

  /**
   * Process metadata for project-overview state
   * Updated to handle new polling response format with proper validation
   */
  private processProjectOverviewMetadata(metadata: MetadataItem[]): void {
    // this.logger.info('🔍 Processing project overview metadata (legacy method)');

    // STEP 1: Find metadata item with type === "artifact"
    const artifactMetadata = metadata.find(m => m.type === 'artifact');
    if (!artifactMetadata) {
      // this.logger.warn('❌ No artifact metadata found (legacy method)');
      return;
    }

    if (!artifactMetadata.data) {
      // this.logger.warn('❌ Artifact metadata has no data (legacy method)');
      return;
    }

    try {
      // Handle both old and new formats
      let artifactData;
      if (typeof artifactMetadata.data === 'string') {
        // Old format: data is a JSON string
        // this.logger.info('📜 Processing old format (JSON string)');
        artifactData = JSON.parse(artifactMetadata.data);
      } else {
        // New format: data is already an object
        // this.logger.info('📦 Processing new format (object)');
        artifactData = artifactMetadata.data;
      }

      // STEP 2: Check if inner data has type === "text"
      if (!artifactData.type) {
        // this.logger.warn('❌ Artifact data has no type property (legacy method)');
        return;
      }

      // this.logger.info('🏷️ Artifact data type:', artifactData.type);

      if (artifactData.type !== 'text') {
        // this.logger.warn('❌ Artifact data type is not "text":', artifactData.type);
        return;
      }

      // STEP 3: Get content from either 'value' (old format) or 'data' (new format)
      const content = artifactData.value || artifactData.data;

      if (!content) {
        // this.logger.warn('❌ No content found in artifact data (legacy method)');
        return;
      }

      // this.logger.info('📄 Content extracted, length:', content?.length || 0);

      // Display README format content in artifacts window (replace existing, don't add new)
      const currentArtifact = this.artifactDataSubject.value;
      if (currentArtifact && currentArtifact.type === 'readme') {
        // this.logger.info('🔄 Updating existing Project Overview artifact content (legacy method)');
        // Update existing artifact content
        // this.artifactDataSubject.next({
        //   ...currentArtifact,
        //   content: content
        // });
      } else {
        // this.logger.info('🆕 Creating new Project Overview artifact (legacy method)');
        // Create new artifact only if none exists
        // this.artifactDataSubject.next({
        //   type: 'readme',
        //   content: content
        // });
      }
      // this.logger.info('✅ Project overview metadata processed (legacy method)');
    } catch (error) {
      // this.logger.error('💥 Failed to parse project overview artifact (legacy method):', error);
    }
  }

  /**
   * Process metadata for Layout Analyzed state
   */
  private processLayoutAnalyzedMetadata(metadata: MetadataItem[]): void {
    const artifactMetadata = metadata.find(m => m.type === 'artifact');
    if (artifactMetadata && artifactMetadata.data) {
      try {
        const artifactData = typeof artifactMetadata.data === 'string'
          ? JSON.parse(artifactMetadata.data)
          : artifactMetadata.data;

        if (artifactData.type === 'string') {
          // Display layout as image in artifacts window
          this.artifactDataSubject.next({
            type: 'layout',
            layoutCode: artifactData.value
          });
        }
      } catch (error) {
        // this.logger.warn('Failed to parse layout analyzed artifact:', error);
      }
    }
  }

  /**
   * Process metadata for Design_System Analyzed state
   */
  private processDesignSystemMetadata(metadata: MetadataItem[]): void {
    const artifactMetadata = metadata.find(m => m.type === 'artifact');
    if (artifactMetadata && artifactMetadata.data) {
      try {
        const artifactData = typeof artifactMetadata.data === 'string'
          ? JSON.parse(artifactMetadata.data)
          : artifactMetadata.data;

        if (artifactData.type === 'json') {
          // Display design tokens in artifacts window
          const designTokens = artifactData.value as DesignTokensData;
          this.artifactDataSubject.next({
            type: 'design-tokens',
            tokens: designTokens
          });
        }
      } catch (error) {
        // this.logger.warn('Failed to parse design system artifact:', error);
      }
    }
  }

  /**
   * Process metadata for deployed state
   */
  private processDeployedMetadata(metadata: MetadataItem[]): void {
    const refCodeMetadata = metadata.find(m => m.type === 'ref_code');
    if (refCodeMetadata && typeof refCodeMetadata.data === 'string') {
      // Set preview URL for preview window
      this.previewUrlSubject.next(refCodeMetadata.data);
    }
  }

  /**
   * Process metadata for BUILD state
   * Shows complete code files when progress is BUILD and status is IN_PROGRESS or COMPLETED
   */
  private processBuildMetadata(metadata: MetadataItem[], status: StatusType): void {
    // this.logger.info('Processing BUILD metadata with status:', status);

    // Process files metadata for BUILD state when status is IN_PROGRESS or COMPLETED
    if (status === 'IN_PROGRESS' || status === 'COMPLETED') {
      const filesMetadata = metadata.find(m => m.type === 'files');
      if (filesMetadata && filesMetadata.data) {
        try {
          const files = typeof filesMetadata.data === 'string'
            ? JSON.parse(filesMetadata.data)
            : filesMetadata.data;

          if (Array.isArray(files)) {
            const fileData: FileData[] = files.map(file => ({
              path: file.fileName || file.path || 'Unknown file',
              code: file.code || file.content || ''
            }));

            this.codeFilesSubject.next(fileData);
            // this.logger.info('BUILD state: Code files prepared for code viewer:', fileData.length, 'files');
          }
        } catch (error) {
          // this.logger.warn('Failed to parse files metadata for BUILD state:', error);
        }
      }
    }
  }

  /**
   * Process general metadata for states that don't have specific artifact handling
   */
  private processGeneralMetadata(metadata: MetadataItem[]): void {
    // Process files metadata for code files
    const filesMetadata = metadata.find(m => m.type === 'files');
    if (filesMetadata && filesMetadata.data) {
      try {
        const filesData = typeof filesMetadata.data === 'string'
          ? JSON.parse(filesMetadata.data)
          : filesMetadata.data;

        if (Array.isArray(filesData)) {
          const codeFiles = filesData.map((file: any) => ({
            path: file.fileName || file.path || 'unknown',
            code: file.code || file.content || ''
          }));
          this.codeFilesSubject.next(codeFiles);
        }
      } catch (error) {
        // this.logger.warn('Failed to parse general files metadata:', error);
      }
    }

    // Process fileNames metadata for file list
    const fileNamesMetadata = metadata.find(m => m.type === 'fileNames');
    if (fileNamesMetadata && fileNamesMetadata.data) {
      try {
        const fileNames = typeof fileNamesMetadata.data === 'string'
          ? JSON.parse(fileNamesMetadata.data)
          : fileNamesMetadata.data;

        if (Array.isArray(fileNames)) {
          this.fileListSubject.next(fileNames);
        }
      } catch (error) {
        // this.logger.warn('Failed to parse fileNames metadata:', error);
      }
    }
  }

  // ============================================================================
  // ENHANCED ARTIFACT PROCESSING METHODS FOR NEW API STRUCTURE
  // ============================================================================

  /**
   * Process project-overview artifact (type: "text", README format)
   */
  private processProjectOverviewArtifactEnhanced(metadata: MetadataItem[]): void {
    const artifactMetadata = metadata.find(m => m.type === 'artifact');
    if (artifactMetadata && artifactMetadata.data) {
      try {
        const artifactData = typeof artifactMetadata.data === 'string'
          ? JSON.parse(artifactMetadata.data)
          : artifactMetadata.data;

        if (artifactData.type === 'text') {
          // Display README format content in artifacts window (replace existing, don't add new)
          const currentArtifact = this.artifactDataSubject.value;
          if (currentArtifact && currentArtifact.type === 'readme') {
            // this.logger.info('🔄 Updating existing Project Overview artifact content (enhanced method)');
            // Update existing artifact content
            // this.artifactDataSubject.next({
            //   ...currentArtifact,
            //   content: artifactData.value
            // });
          } else {
            // this.logger.info('🆕 Creating new Project Overview artifact (enhanced method)');
            // Create new artifact only if none exists
            // this.artifactDataSubject.next({
            //   type: 'readme',
            //   content: artifactData.value
            // });
          }
          // this.logger.info('Project overview artifact processed for artifacts window');
        }
      } catch (error) {
        // this.logger.warn('Failed to parse project overview artifact:', error);
      }
    }
  }

  /**
   * Process Layout Analyzed artifact (type: "string", layout code)
   */
  private processLayoutAnalyzedArtifactEnhanced(metadata: MetadataItem[]): void {
    const artifactMetadata = metadata.find(m => m.type === 'artifact');
    if (artifactMetadata && artifactMetadata.data) {
      try {
        const artifactData = typeof artifactMetadata.data === 'string'
          ? JSON.parse(artifactMetadata.data)
          : artifactMetadata.data;

        if (artifactData.type === 'string') {
          // Display layout as image in artifacts window
          this.artifactDataSubject.next({
            type: 'layout',
            layoutCode: artifactData.value
          });
          // this.logger.info('Layout analyzed artifact processed for artifacts window');
        }
      } catch (error) {
        // this.logger.warn('Failed to parse layout analyzed artifact:', error);
      }
    }
  }

  /**
   * Process Design_System Analyzed artifact (type: "json", design tokens)
   */
  private processDesignSystemArtifactEnhanced(metadata: MetadataItem[]): void {
    const artifactMetadata = metadata.find(m => m.type === 'artifact');
    if (artifactMetadata && artifactMetadata.data) {
      try {
        const artifactData = typeof artifactMetadata.data === 'string'
          ? JSON.parse(artifactMetadata.data)
          : artifactMetadata.data;

        if (artifactData.type === 'json') {
          // Display design tokens in artifacts window
          const designTokens = artifactData.value;
          this.artifactDataSubject.next({
            type: 'design-tokens',
            tokens: designTokens
          });
          // this.logger.info('Design system artifact processed for artifacts window');
        }
      } catch (error) {
        // this.logger.warn('Failed to parse design system artifact:', error);
      }
    }
  }

  /**
   * Process deployed artifact (ref_code for preview URL)
   */
  private processDeployedArtifactEnhanced(metadata: MetadataItem[]): void {
    const refCodeMetadata = metadata.find(m => m.type === 'ref_code');
    if (refCodeMetadata && typeof refCodeMetadata.data === 'string') {
      // Set preview URL for preview window
      this.previewUrlSubject.next(refCodeMetadata.data);
      // this.logger.info('Deployed artifact processed - preview URL set:', refCodeMetadata.data);
    }
  }

  /**
   * Process files metadata specifically for code viewer when status is COMPLETED
   */
  private processFilesForCodeViewerEnhanced(metadata: MetadataItem[]): void {
    const filesMetadata = metadata.find(m => m.type === 'files');
    if (filesMetadata && filesMetadata.data) {
      try {
        const files = typeof filesMetadata.data === 'string'
          ? JSON.parse(filesMetadata.data)
          : filesMetadata.data;

        if (Array.isArray(files)) {
          const fileData: FileData[] = files.map(file => ({
            path: file.fileName || file.path || 'Unknown file',
            code: file.code || ''
          }));
          this.codeFilesSubject.next(fileData);
          // this.logger.info('Files processed for code viewer:', fileData.length, 'files');
        }
      } catch (error) {
        // this.logger.warn('Failed to parse files metadata for code viewer:', error);
      }
    }
  }

  /**
   * Process preview URL from ref_code metadata
   */
  private processPreviewUrlEnhanced(metadata: MetadataItem[]): void {
    const refCodeMetadata = metadata.find(m => m.type === 'ref_code');
    if (refCodeMetadata && typeof refCodeMetadata.data === 'string') {
      this.previewUrlSubject.next(refCodeMetadata.data);
      // this.logger.info('Preview URL processed:', refCodeMetadata.data);
    }
  }

  /**
   * Retrieve missed metadata from history or prev_metadata
   * This handles cases where polling interval causes missed COMPLETED states
   */
  private retrieveMissedMetadata(response: NewPollingResponseWithPrevMetadata, currentProgress: ProgressState, currentStatus: StatusType): MetadataItem[] {
    let retrievedMetadata: MetadataItem[] = [];

    // First, check if prev_metadata is available
    if (response.prev_metadata && response.prev_metadata.length > 0) {
      // this.logger.info('Found prev_metadata with', response.prev_metadata.length, 'items');
      retrievedMetadata = [...response.prev_metadata];
    }

    // Second, check history for COMPLETED states that might have metadata
    if (response.history && response.history.length > 0) {
      // Look for the most recent COMPLETED state in history that matches our current progress
      const completedHistoryItem = response.history
        .reverse() // Start from most recent
        .find(item =>
          item.status === 'COMPLETED' &&
          item.progress === currentProgress &&
          item.metadata &&
          item.metadata.length > 0
        );

      if (completedHistoryItem) {
        // this.logger.info('Found completed history item with metadata for progress:', currentProgress);
        retrievedMetadata = [...retrievedMetadata, ...completedHistoryItem.metadata];
      }

      // Also look for any other COMPLETED states that might have relevant metadata
      const otherCompletedItems = response.history
        .filter(item =>
          item.status === 'COMPLETED' &&
          item.metadata &&
          item.metadata.length > 0 &&
          this.isRelevantProgressForCurrentState(item.progress, currentProgress)
        );

      otherCompletedItems.forEach(item => {
        // this.logger.info('Found additional completed history item with metadata for progress:', item.progress);
        retrievedMetadata = [...retrievedMetadata, ...item.metadata];
      });
    }

    return retrievedMetadata;
  }

  /**
   * Process artifacts with priority: prev_metadata FIRST, then history if needed
   * This ensures no duplicates and follows the correct priority order
   * ENHANCED: Always processes layout artifacts from prev_metadata
   */
  private processArtifactsWithPriority(response: NewPollingResponseWithPrevMetadata): void {

    let artifactsProcessed = false;

    // PRIORITY 1: Check prev_metadata first (highest priority for layout extraction)
    if (response.prev_metadata && response.prev_metadata.length > 0) {
      this.logger.info('🎯 Processing prev_metadata with priority for layout extraction');
      this.processMissedStatesFromPrevMetadata(response);
      artifactsProcessed = true;
    }

    // PRIORITY 2: Process history for any additional completed states
    if (response.history && response.history.length > 0) {
      this.processHistoryForAllCompletedStates(response.history);
      artifactsProcessed = true;
    }

    if (!artifactsProcessed) {
      this.logger.debug('No artifacts found in prev_metadata or history');
    }
  }

  /**
   * Process history for all completed states to extract artifacts
   * This ensures we don't miss any artifacts from completed states
   */
  private processHistoryForAllCompletedStates(history: any[]): void {

    if (!history || history.length === 0) {

      return;
    }

    // Process each completed state in history
    history.forEach((historyItem, index) => {

      if (historyItem.status === 'COMPLETED' && historyItem.metadata && historyItem.metadata.length > 0) {

        this.logger.debug('Processing completed history item:', historyItem.progress);
        this.processArtifactFromMetadata(historyItem.progress, historyItem.metadata);
      } else {

      }
    });
  }

  /**
   * Process missed states from prev_metadata
   * This handles the specific case where we miss a state due to polling intervals
   * ENHANCED: Prioritizes layout extraction from prev_metadata
   */
  private processMissedStatesFromPrevMetadata(response: NewPollingResponseWithPrevMetadata): void {

    if (!response.prev_metadata || response.prev_metadata.length === 0) {

      return;
    }

    this.logger.info('Processing prev_metadata for missed states:', response.prev_metadata.length, 'items');

    // ENHANCED: First pass - prioritize layout artifacts
    response.prev_metadata.forEach((metadataItem, index) => {

      if (metadataItem.type === 'artifact' && metadataItem.data) {

        // Check if this is a layout artifact
        const artifactData = typeof metadataItem.data === 'string'
          ? JSON.parse(metadataItem.data)
          : metadataItem.data;

        if (artifactData && artifactData.type === 'text' && typeof artifactData.data === 'string') {
          const layoutKey = artifactData.data;
          const validLayoutKeys = ['HB', 'HBF', 'HLSB', 'HLSBF', 'HBRS', 'HBRSF', 'HLSBRS', 'HLSBRSF'];

          if (validLayoutKeys.includes(layoutKey)) {
            this.logger.info('🎯 Layout artifact found in prev_metadata:', layoutKey);
            // ENHANCED: Store layout for later display instead of immediate processing
            this.storeLayoutForLaterDisplay(layoutKey, artifactData);
            return; // Store layout, don't process immediately
          }
        }

        // Determine the progress state based on the artifact data type and current progress
        let progressState = this.determineProgressStateFromArtifact(metadataItem.data, response.progress);

        // Process the artifact with the determined progress state
        this.processArtifactFromMetadata(progressState, [metadataItem]);
      } else {

      }
    });
  }

  /**
   * Generate a unique key for artifact duplicate prevention
   * Uses artifact type and content hash to create a unique identifier
   */
  private generateArtifactKey(type: string, content: any): string {
    // Create a simple hash of the content for uniqueness
    const contentStr = typeof content === 'string' ? content : JSON.stringify(content);
    const contentHash = this.simpleHash(contentStr);
    return `${type}_${contentHash}`;
  }

  /**
   * Simple hash function for content deduplication
   * Creates a consistent hash from string content
   */
  private simpleHash(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36); // Convert to base36 for shorter string
  }

  /**
   * Clear processed artifacts and logs cache
   * Call this when starting a new project or when you want to allow re-processing
   */
  public clearProcessedArtifacts(): void {

    this.processedArtifacts.clear();
    this.processedLogs.clear();
    this.accumulatedLogContent = '';
    this.logger.info('Processed artifacts and logs cache cleared');
  }

  /**
   * Determine the progress state from artifact data and current progress
   * This helps identify which type of artifact we're dealing with
   */
  private determineProgressStateFromArtifact(artifactData: any, currentProgress: string): string {

    // Check the artifact data structure to determine its type
    if (artifactData && artifactData.type === 'text') {
      const data = artifactData.data;

      // Check if it's layout data (typically short strings like "HLSB")
      if (typeof data === 'string' && data.length <= 10 && /^[A-Z]+$/.test(data)) {

        return 'LAYOUT_ANALYZED';
      }

      // Check if it's project overview data (typically JSON string or long text)
      if (typeof data === 'string') {
        try {
          const parsed = JSON.parse(data);
          if (parsed.projectInfo || parsed.techStack || parsed.layoutStructure) {

            return 'OVERVIEW';
          }
        } catch (e) {
          // If it's a long text, likely project overview
          if (data.length > 50) {

            return 'OVERVIEW';
          }
        }
      }
    }

    // Check if it's design system data (JSON type)
    if (artifactData && artifactData.type === 'json') {

      return 'DESIGN_SYSTEM_ANALYZED';
    }

    // Fallback: try to infer from current progress

    // Based on current progress, guess what the previous artifact might be
    switch (currentProgress.toUpperCase()) {
      case 'PAGES_GENERATED':
      case 'COMPONENTS_CREATED':
        return 'LAYOUT_ANALYZED'; // Layout comes before components/pages
      case 'DESIGN_SYSTEM_MAPPED':
        return 'LAYOUT_ANALYZED'; // Layout comes before design system
      case 'LAYOUT_ANALYZED':
        return 'OVERVIEW'; // Overview comes before layout
      default:
        return 'OVERVIEW'; // Default fallback
    }
  }

  /**
   * Get possible previous states based on current progress
   * This helps determine which artifacts to look for in prev_metadata
   */
  private getPossiblePreviousStates(currentProgress: string): string[] {
    const progressSequence = [
      'OVERVIEW',
      'SEED_PROJECT_INITIALIZED',
      'LAYOUT_ANALYZED',
      'DESIGN_SYSTEM_ANALYZED',
      'FILE_QUEUE',
      'COMPONENTS_CREATED',
      'PAGES_GENERATED',
      'BUILD',
      'DEPLOY'
    ];

    const currentIndex = progressSequence.indexOf(currentProgress);
    if (currentIndex > 0) {
      // Return the previous state(s) that might have artifacts
      return progressSequence.slice(Math.max(0, currentIndex - 3), currentIndex);
    }

    return [];
  }

  /**
   * Process artifact from metadata for a specific progress state
   * This is a unified method to handle artifact extraction regardless of source
   */
  private processArtifactFromMetadata(progress: string, metadata: MetadataItem[]): void {

    const artifactMetadata = metadata.find(m => m.type === 'artifact');
    if (!artifactMetadata || !artifactMetadata.data) {

      return;
    }

    try {
      // Handle nested artifact data structure
      let artifactData = artifactMetadata.data;

      if (typeof artifactData === 'string') {

        artifactData = JSON.parse(artifactData);
      }

      // Process based on progress state
      const progressUpper = progress.toUpperCase();

      switch (progressUpper) {
        case 'OVERVIEW':
        case 'PROJECT_OVERVIEW':

          this.processProjectOverviewArtifactFromData(artifactData);
          break;
        case 'LAYOUT_ANALYZED':

          this.processLayoutAnalyzedArtifactFromData(artifactData);
          break;
        case 'DESIGN_SYSTEM_ANALYZED':
        case 'DESIGN_SYSTEM_MAPPED':

          this.processDesignSystemArtifactFromData(artifactData);
          break;
        default:

          this.logger.debug('No specific artifact processing for progress:', progress);
      }
    } catch (error) {

      this.logger.warn('Failed to process artifact from metadata:', error);
    }
  }

  /**
   * Process project overview artifact from data (for prev_metadata and history)
   * Handles the nested structure: { type: "text", data: "content" }
   */
  private processProjectOverviewArtifactFromData(artifactData: any): void {

    if (!artifactData || artifactData.type !== 'text') {

      this.logger.debug('Project overview artifact data is not text type:', artifactData?.type);
      return;
    }

    let content = artifactData.data;

    // Generate unique key for duplicate prevention
    const artifactKey = this.generateArtifactKey('readme', content);

    // Check if this artifact has already been processed
    if (this.processedArtifacts.has(artifactKey)) {

      return;
    }

    if (typeof content === 'string') {
      try {
        const parsedContent = JSON.parse(content);
        if (typeof parsedContent === 'object' && parsedContent !== null) {

          content = this.generateReadmeFromProjectData(parsedContent);
        }
      } catch (e) {

        // Use as plain text if not JSON
      }
    }

    // Mark this artifact as processed
    this.processedArtifacts.add(artifactKey);

    // Send to artifact display
    const artifactPayload = {
      type: 'readme',
      content: content,
      rawData: artifactData
    };

    this.artifactDataSubject.next(artifactPayload);

    this.logger.info('✅ Project overview artifact processed from prev_metadata/history');
  }

  /**
   * Store layout for later display when progress moves from LAYOUT_ANALYZED to next state
   * ENHANCED: Only stores layout, doesn't process immediately
   */
  private storeLayoutForLaterDisplay(layoutKey: string, artifactData: any): void {
    this.logger.info('📦 Storing layout for later display:', layoutKey);

    this.storedLayoutKey = layoutKey;
    this.storedLayoutData = artifactData;

    this.logger.info('✅ Layout stored successfully, will be displayed when progress moves to next state');
  }

  /**
   * Process stored layout when progress moves from LAYOUT_ANALYZED to next state
   * ENHANCED: Only processes stored layout when appropriate
   */
  private processStoredLayoutIfReady(currentProgress: string): void {
    // Check if we have stored layout and progress has moved from LAYOUT_ANALYZED
    if (this.storedLayoutKey && this.storedLayoutData &&
        currentProgress !== 'LAYOUT_ANALYZED' &&
        currentProgress !== 'Layout Analyzed') {

      this.logger.info('🎯 Progress moved from LAYOUT_ANALYZED to', currentProgress, '- processing stored layout:', this.storedLayoutKey);

      // Process the stored layout
      this.processLayoutAnalyzedArtifactFromData(this.storedLayoutData);

      // Clear stored layout after processing
      this.storedLayoutKey = null;
      this.storedLayoutData = null;

      this.logger.info('✅ Stored layout processed and cleared');
    }
  }

  /**
   * Process layout analyzed artifact from data (for prev_metadata and history)
   * Handles the nested structure: { type: "text", data: "HLSBRS" }
   * ENHANCED: Properly extracts layout key and maps to correct layout image
   * PRIORITY: Always processes layout from prev_metadata regardless of duplicate prevention
   */
  private processLayoutAnalyzedArtifactFromData(artifactData: any): void {

    if (!artifactData || artifactData.type !== 'text') {

      this.logger.debug('Layout analyzed artifact data is not text type:', artifactData?.type);
      return;
    }

    // ENHANCED: Validate layout key against known layout mappings
    const layoutKey = artifactData.data;
    const validLayoutKeys = ['HB', 'HBF', 'HLSB', 'HLSBF', 'HBRS', 'HBRSF', 'HLSBRS', 'HLSBRSF'];

    // Use provided layout key or fallback to HB
    const finalLayoutKey = validLayoutKeys.includes(layoutKey) ? layoutKey : 'HB';

    if (!validLayoutKeys.includes(layoutKey)) {
      this.logger.warn('Unknown layout key detected:', layoutKey, 'Using default HB layout');
    } else {
      this.logger.info('Valid layout key detected from prev_metadata:', layoutKey);
    }

    // ENHANCED: Always process layout from prev_metadata (no duplicate prevention for layout)
    // This ensures layout is always shown when available in prev_metadata
    const artifactPayload = {
      type: 'layout',
      layoutCode: finalLayoutKey, // This will be mapped to correct image in component
      layoutKey: finalLayoutKey,   // Explicit layout key for clarity
      rawData: artifactData
    };

    this.artifactDataSubject.next(artifactPayload);

    this.logger.info('✅ Layout analyzed artifact processed from prev_metadata/history with key:', finalLayoutKey);
  }

  /**
   * Process design system artifact from data (for prev_metadata and history)
   * Handles the nested structure: { type: "json", data: {...} }
   * ENHANCED: Adds fallback logic when no prev_metadata exists
   */
  private processDesignSystemArtifactFromData(artifactData: any): void {

    if (!artifactData || artifactData.type !== 'json') {

      this.logger.debug('Design system artifact data is not json type:', artifactData?.type);
      return;
    }

    let designTokens = artifactData.data;

    // Generate unique key for duplicate prevention
    const artifactKey = this.generateArtifactKey('design-tokens', designTokens);

    // Check if this artifact has already been processed
    if (this.processedArtifacts.has(artifactKey)) {

      return;
    }

    if (typeof designTokens === 'string') {
      try {

        designTokens = JSON.parse(designTokens);
      } catch (e) {

        this.logger.warn('Failed to parse design system JSON:', e);
        return;
      }
    }

    // Mark this artifact as processed
    this.processedArtifacts.add(artifactKey);

    // Send to artifact display
    const artifactPayload = {
      type: 'design-tokens',
      tokens: designTokens,
      rawData: artifactData
    };

    this.artifactDataSubject.next(artifactPayload);

    this.logger.info('✅ Design system artifact processed from prev_metadata/history');
  }

  /**
   * Process design system fallback when no prev_metadata exists
   * Shows hardcoded design system with "Coming Soon" placeholders
   * ENHANCED: Provides fallback for DESIGN_SYSTEM_ANALYZED progress without prev_metadata
   */
  private processDesignSystemFallback(): void {
    // Always show the design system fallback when called (no duplicate prevention)
    // This ensures it shows every time Design_System Analyzed state is detected

    // Create fallback design tokens with "Coming Soon" placeholders
    const fallbackDesignTokens = {
      colors: {
        primary: '#E48900',
        secondary: '#FFA826',
        accent: '#212121',
        neutral: '#4D4D4D',
        background: '#F5F7FA',
        note: 'Design system colors - Coming Soon'
      },
      typography: {
        headings: {
          h1: { size: '64px', weight: 'Semi Bold', lineHeight: '1.2' },
          h2: { size: '36px', weight: 'Semi Bold', lineHeight: '1.2' },
          h3: { size: '28px', weight: 'Semi Bold', lineHeight: '1.3' },
          h4: { size: '20px', weight: 'Semi Bold', lineHeight: '1.4' }
        },
        note: 'Typography system - Coming Soon'
      },
      buttons: {
        primary: { variant: 'primary', label: 'Primary Button' },
        secondary: { variant: 'secondary', label: 'Secondary Button' },
        outline: { variant: 'outline', label: 'Outline Button' },
        text: { variant: 'text', label: 'Text Button' },
        note: 'Button components - Coming Soon'
      },
      status: 'fallback',
      message: 'Design system analysis in progress. Full design tokens will be available soon.'
    };

    // Send fallback to artifact display
    const artifactPayload = {
      type: 'design-tokens',
      tokens: fallbackDesignTokens,
      isFallback: true,
      rawData: { type: 'fallback', data: fallbackDesignTokens }
    };

    this.artifactDataSubject.next(artifactPayload);

    this.logger.info('✅ Design system fallback processed - showing Coming Soon placeholders');
  }

  /**
   * Check if a progress state from history is relevant for the current state
   */
  private isRelevantProgressForCurrentState(historyProgress: string, currentProgress: ProgressState): boolean {
    // Define which previous states are relevant for current states
    const relevantStates: Record<string, string[]> = {
      'OVERVIEW': ['project-overview'],
      'DESIGN_SYSTEM_MAPPED': ['Design_System Analyzed'],
      'LAYOUT_ANALYZED': ['Layout Analyzed'],
      'BUILD': ['COMPONENTS_CREATED', 'PAGES_GENERATED'],
      'DEPLOYED': ['BUILD', 'DEPLOY']
    };

    const currentProgressUpper = currentProgress.toUpperCase();
    const relevantForCurrent = relevantStates[currentProgressUpper] || [];

    return relevantForCurrent.some(relevant =>
      historyProgress.toUpperCase().includes(relevant.toUpperCase()) ||
      relevant.toUpperCase().includes(historyProgress.toUpperCase())
    );
  }

  // ============================================================================
  // NEW WORKFLOW PROCESSING METHODS
  // ============================================================================

  /**
   * Process project overview artifact for new workflow
   * Shows README format content in artifacts window
   * Updated to handle new polling response format with proper validation:
   * 1. Check outer metadata type === "artifact"
   * 2. Check inner data.type === "text"
   * 3. Extract content from data.data
   */
  private processProjectOverviewArtifactNew(metadata: MetadataItem[]): void {
    // this.logger.info('🔍 Processing project overview artifact with new format');
    // this.logger.info('Available metadata items:', metadata.length);
    // this.logger.info('Metadata types:', metadata.map(m => m.type));

    // STEP 1: Find metadata item with type === "artifact"
    const artifactMetadata = metadata.find(m => m.type === 'artifact');
    if (!artifactMetadata) {
      // this.logger.warn('❌ No metadata item found with type "artifact"');
      // this.logger.warn('Available metadata types:', metadata.map(m => m.type));
      return;
    }

    // this.logger.info('✅ Found artifact metadata item');
    // this.logger.info('Artifact metadata structure:', artifactMetadata);

    if (!artifactMetadata.data) {
      // this.logger.warn('❌ Artifact metadata has no data property');
      return;
    }

    try {
      // STEP 2: Get the inner data object (should have type and data properties)
      const innerData = artifactMetadata.data;
      // this.logger.info('📦 Inner data structure:', innerData);
      // this.logger.info('Inner data type:', typeof innerData);

      if (typeof innerData !== 'object' || innerData === null) {
        // this.logger.warn('❌ Inner data is not an object:', typeof innerData);
        return;
      }

      // STEP 3: Check if inner data has type === "text"
      if (!innerData.type) {
        // this.logger.warn('❌ Inner data has no type property');
        // this.logger.warn('Inner data properties:', Object.keys(innerData));
        return;
      }

      // this.logger.info('🏷️ Inner data type:', innerData.type);

      if (innerData.type !== 'text') {
        // this.logger.warn('❌ Inner data type is not "text":', innerData.type);
        // this.logger.warn('Expected: "text", Got:', innerData.type);
        return;
      }

      // this.logger.info('✅ Inner data type is "text" - proceeding with content extraction');

      // STEP 4: Extract the actual content from inner data.data
      if (!innerData.data) {
        // this.logger.warn('❌ Inner data has no data property (content)');
        return;
      }

      let projectContent = innerData.data;
      // this.logger.info('📄 Extracted content type:', typeof projectContent);
      // this.logger.info('📄 Content length:', projectContent?.length || 0);
      // this.logger.info('📄 Content preview:', projectContent?.substring(0, 100) + '...');

      // STEP 5: Process the content (try to parse as JSON if it's structured data)
      if (typeof projectContent === 'string') {
        try {
          const parsedContent = JSON.parse(projectContent);
          // If it parses successfully and is an object, generate README from it
          if (typeof parsedContent === 'object' && parsedContent !== null) {
            // this.logger.info('🔧 Parsed content as JSON object, generating README');
            projectContent = this.generateReadmeFromProjectData(parsedContent);
          }
          // If it's already a string, use it as is
        } catch (e) {
          // If parsing fails, use the string content as is (this is expected for plain text)
          // this.logger.info('📝 Using content as plain text (not JSON)');
        }
      } else if (typeof projectContent === 'object' && projectContent !== null) {
        // If it's already an object, generate README from it
        // this.logger.info('🔧 Content is already an object, generating README');
        projectContent = this.generateReadmeFromProjectData(projectContent);
      }

      // STEP 6: Send the content to the artifacts window (replace existing, don't add new)
      // Check if we already have a project overview artifact
      const currentArtifact = this.artifactDataSubject.value;
      if (currentArtifact && currentArtifact.type === 'readme') {
        // this.logger.info('🔄 Updating existing Project Overview artifact content');
        // Update existing artifact content
        // this.artifactDataSubject.next({
        //   ...currentArtifact,
        //   content: projectContent,
        //   rawData: innerData
        // });
      } else {
        // this.logger.info('🆕 Creating new Project Overview artifact');
        // Create new artifact only if none exists
        // this.artifactDataSubject.next({
        //   type: 'readme',
        //   content: projectContent,
        //   rawData: innerData
        // });
      }

      // this.logger.info('🎉 ✅ Project overview artifact processed successfully!');
      // this.logger.info('📊 Final content length:', projectContent?.length || 0);
      // this.logger.info('👀 Final content preview:', projectContent?.substring(0, 200) + '...');

    } catch (error) {
      // this.logger.error('💥 Failed to parse project overview artifact in new workflow:', error);
      // this.logger.error('Error details:', error);
    }
  }

  /**
   * Generate README content from project data
   */
  private generateReadmeFromProjectData(projectData: any): string {
    if (typeof projectData === 'string') {
      return projectData;
    }

    if (typeof projectData === 'object' && projectData !== null) {
      let readme = '';

      if (projectData.projectInfo) {
        readme += `# ${projectData.projectInfo.name}\n\n`;
        readme += `${projectData.projectInfo.description}\n\n`;
        readme += `**Target Page:** ${projectData.projectInfo.targetPage}\n\n`;
      }

      if (projectData.techStack) {
        readme += `## Tech Stack\n\n`;
        readme += `- **Framework:** ${projectData.techStack.framework}\n`;
        readme += `- **Styling:** ${projectData.techStack.styling}\n`;
        if (projectData.techStack.componentLibrary) {
          readme += `- **Component Library:** ${projectData.techStack.componentLibrary}\n`;
        }
        readme += '\n';
      }

      if (projectData.layoutStructure) {
        readme += `## Layout Structure\n\n`;
        readme += `**Layout Type:** ${projectData.layoutStructure.layoutType}\n\n`;
        if (projectData.layoutStructure.overall) {
          readme += `**Overall Layout:** ${projectData.layoutStructure.overall.type}\n`;
          readme += `**Grid Definition:** ${projectData.layoutStructure.overall.definition}\n\n`;
        }
      }

      if (projectData.componentBreakdown) {
        readme += `## Component Breakdown\n\n`;
        if (projectData.componentBreakdown.organisms) {
          readme += `### Organisms\n`;
          projectData.componentBreakdown.organisms.forEach((org: any) => {
            readme += `- **${org.name}:** ${org.notes}\n`;
          });
          readme += '\n';
        }
      }

      return readme;
    }

    return 'Project overview data available';
  }

  /**
   * Process design system artifact for new workflow
   * Shows design tokens in artifacts window
   * Updated to handle new polling response format
   */
  private processDesignSystemArtifactNew(metadata: MetadataItem[]): void {
    // this.logger.info('Processing design system artifact with new format');
    // this.logger.info('Available metadata:', metadata);

    const artifactMetadata = metadata.find(m => m.type === 'artifact');
    if (artifactMetadata && artifactMetadata.data) {
      try {
        // this.logger.info('Raw design system artifact data:', artifactMetadata.data);

        // The artifact data is already an object with type and data properties
        const artifactData = artifactMetadata.data;

        // this.logger.info('Design system artifact data structure:', artifactData);

        if (artifactData.type === 'json' || artifactData.type === 'text') {
          // Parse the design tokens from the data field
          let designTokens;
          try {
            if (typeof artifactData.data === 'string') {
              // Try to parse as JSON first
              try {
                designTokens = JSON.parse(artifactData.data);
                // this.logger.info('Parsed design tokens as JSON');
              } catch {
                // If JSON parsing fails, treat as string and try to extract design system
                designTokens = this.extractDesignSystemFromString(artifactData.data);
                // this.logger.info('Extracted design system from string');
              }
            } else {
              designTokens = artifactData.data;
              // this.logger.info('Using design tokens as object');
            }
          } catch (e) {
            designTokens = artifactData.data;
            // this.logger.warn('Fallback to raw data for design tokens');
          }

          // Extract design system from project data if needed
          if (designTokens && designTokens.designSystem) {
            designTokens = designTokens.designSystem;
            // this.logger.info('Extracted nested design system');
          }

          this.artifactDataSubject.next({
            type: 'design-tokens',
            tokens: designTokens,
            rawData: artifactData
          });
          // this.logger.info('✅ Design system artifact processed successfully');
        } else {
          // this.logger.warn('Design system artifact type is not "json" or "text":', artifactData.type);
        }
      } catch (error) {
        // this.logger.error('Failed to parse design system artifact in new workflow:', error);
      }
    } else {
      // this.logger.warn('No design system artifact metadata found');
      // this.logger.warn('Available metadata types:', metadata.map(m => m.type));
    }
  }

  /**
   * Extract design system from string data (for cases where it's embedded in project data)
   */
  private extractDesignSystemFromString(data: string): any {
    try {
      const projectData = JSON.parse(data);
      if (projectData.designSystem) {
        return projectData.designSystem;
      }
      return projectData;
    } catch {
      return { note: 'Design system data available but could not be parsed' };
    }
  }

  /**
   * Process layout analyzed artifact for new workflow
   * Shows layout as image in artifacts window
   * Updated to handle new polling response format
   */
  private processLayoutAnalyzedArtifactNew(metadata: MetadataItem[]): void {
    // this.logger.info('Processing layout analyzed artifact with new format');
    // this.logger.info('Available metadata:', metadata);

    const artifactMetadata = metadata.find(m => m.type === 'artifact');
    if (artifactMetadata && artifactMetadata.data) {
      try {
        // this.logger.info('Raw layout analyzed artifact data:', artifactMetadata.data);

        // The artifact data is already an object with type and data properties
        const artifactData = artifactMetadata.data;

        // this.logger.info('Layout analyzed artifact data structure:', artifactData);

        if (artifactData.type === 'string' || artifactData.type === 'text') {
          this.artifactDataSubject.next({
            type: 'layout',
            layoutCode: artifactData.data,
            rawData: artifactData
          });
          // this.logger.info('✅ Layout analyzed artifact processed successfully');
          // this.logger.info('Layout code:', artifactData.data);
        } else {
          // this.logger.warn('Layout analyzed artifact type is not "string" or "text":', artifactData.type);
        }
      } catch (error) {
        // this.logger.error('Failed to parse layout analyzed artifact in new workflow:', error);
      }
    } else {
      // this.logger.warn('No layout analyzed artifact metadata found');
      // this.logger.warn('Available metadata types:', metadata.map(m => m.type));
    }
  }

  /**
   * Process BUILD state code files for new workflow
   * Enables code viewer with complete code files
   * Updated to handle the exact structure from user's example
   */
  private processBuildCodeFilesNew(metadata: MetadataItem[]): void {
    // this.logger.info('🔧 Processing BUILD code files with IN_PROGRESS/COMPLETED status');
    // this.logger.info('Available metadata:', metadata);

    // Look for metadata with type "files" (direct format from user's example)
    const filesMetadata = metadata.find(m => m.type === 'files');
    if (filesMetadata && filesMetadata.data) {
      try {
        let files = filesMetadata.data;
        // this.logger.info('📁 Found files metadata:', files);

        // Parse files if they're in string format
        if (typeof files === 'string') {
          try {
            files = JSON.parse(files);
          } catch (e) {
            this.logger.warn('Failed to parse files string:', e);
            return;
          }
        }

        if (Array.isArray(files)) {
          // this.logger.info('📂 Processing', files.length, 'files from BUILD metadata');

          const fileData: FileData[] = files.map(file => {
            // Handle the exact structure from user's example:
            // { "fileName": "...", "content": "..." }
            const filePath = file.fileName || file.path || 'Unknown file';
            const fileCode = file.content || file.code || '';

            // this.logger.info(`📄 Processing file: ${filePath} (${fileCode.length} chars)`);

            return {
              path: filePath,
              code: fileCode
            };
          });

          this.codeFilesSubject.next(fileData);
          // this.logger.info('✅ BUILD code files processed successfully:', fileData.length, 'files');

          // Log file details for debugging
          fileData.forEach(file => {
            // this.logger.info(`📋 File: ${file.path} - ${file.code.length} characters`);
          });
        } else {
          // this.logger.warn('❌ Files data is not an array:', typeof files);
        }
      } catch (error) {
        // this.logger.error('💥 Failed to parse BUILD files metadata:', error);
      }
    } else {
      // this.logger.warn('❌ No files metadata found for BUILD state');
      // this.logger.warn('Available metadata types:', metadata.map(m => m.type));
    }
  }

  /**
   * Clear previous deployment state to ensure clean new deployment
   */
  private clearPreviousDeploymentState(): void {
    // this.logger.info('🧹 Clearing previous deployment state');
    this.previewUrlSubject.next('');
    // this.logger.info('✅ Previous deployment state cleared');
  }

  /**
   * Process deployed preview for new workflow
   * Enables preview tab with deployed URL
   *
   * **STRICT REQUIREMENTS**:
   * - Only processes when progress === "DEPLOY" AND status === "COMPLETED"
   * - Extracts URL from metadata array where type === "ref_code"
   * - Validates URL format before setting
   * - URL will be sanitized in component using DomSanitizer.bypassSecurityTrustResourceUrl()
   *
   * **Example metadata structure**:
   * ```json
   * {
   *   "metadata": [
   *     {
   *       "type": "ref_code",
   *       "data": "https://mlo-a774-9290.netlify.app"
   *     }
   *   ]
   * }
   * ```
   */
  private processDeployedPreviewNew(metadata: MetadataItem[]): void {
    this.logger.info('🚀 Processing deployed preview for new workflow');
    this.logger.debug('Current progress:', this.getCurrentProgress());
    this.logger.debug('Current status:', this.getCurrentStatus());
    this.logger.debug('Available metadata items:', metadata.length);
    this.logger.debug('Metadata types:', metadata.map(m => m.type));

    // Clear any previous preview URL first to ensure clean state
    this.logger.debug('Clearing previous preview URL');
    this.previewUrlSubject.next('');

    // **REQUIREMENT**: Validate metadata array exists and has items
    if (!metadata || !Array.isArray(metadata) || metadata.length === 0) {
      this.logger.warn('❌ No metadata array provided for DEPLOY state');
      this.logger.warn('📋 Expected metadata structure: [{ type: "ref_code", data: "https://..." }]');
      return;
    }

    // **REQUIREMENT**: Look for metadata with type "ref_code" (exact format from requirements)
    const refCodeMetadata = metadata.find(m => m.type === 'ref_code');
    this.logger.debug('Searching for ref_code metadata', {
      found: !!refCodeMetadata,
      availableTypes: metadata.map(m => m.type)
    });

    if (!refCodeMetadata) {
      this.logger.warn('❌ No ref_code metadata found for DEPLOY state');
      this.logger.warn('📋 Available metadata types:', metadata.map(m => m.type));
      this.logger.warn('📋 Expected: metadata item with type === "ref_code"');
      return;
    }

    this.logger.debug('ref_code metadata validation', {
      dataType: typeof refCodeMetadata.data,
      dataLength: (refCodeMetadata.data as string)?.length || 0
    });

    // **REQUIREMENT**: Validate data field exists and is string type
    if (!refCodeMetadata.data) {
      this.logger.warn('❌ ref_code metadata found but data field is empty');
      this.logger.warn('📋 Expected: ref_code metadata with data field containing URL string');
      return;
    }

    // **REQUIREMENT**: Type safety check for ref_code data
    if (typeof refCodeMetadata.data !== 'string') {
      this.logger.warn('❌ ref_code metadata data is not a string type');
      this.logger.warn('📋 Expected: string containing deployed URL');
      this.logger.warn('📋 Actual type:', typeof refCodeMetadata.data);
      this.logger.warn('📋 Actual value:', refCodeMetadata.data);
      return;
    }

    try {
      // **REQUIREMENT**: Extract URL from ref_code metadata data field
      // Type safety already validated above, so we can safely cast to string
      const deployedUrl: string = (refCodeMetadata.data as string).trim();
      this.logger.info('✅ ref_code data extracted and trimmed:', deployedUrl);

      // **REQUIREMENT**: Validate URL format and content
      if (!deployedUrl || deployedUrl === '') {
        this.logger.warn('❌ Extracted URL is empty after trimming');
        this.logger.warn('📋 Original data:', refCodeMetadata.data);
        return;
      }

      // **REQUIREMENT**: Basic URL validation for security
      this.logger.info('🔍 Validating extracted URL:', deployedUrl);
      if (!this.isValidUrl(deployedUrl)) {
        this.logger.warn('❌ Extracted URL is not a valid URL format:', deployedUrl);
        this.logger.warn('📋 URL must be valid HTTP/HTTPS format');
        this.logger.warn('📋 URL validation failed - checking if protocol is missing');

        // Try to fix missing protocol
        if (!deployedUrl.startsWith('http://') && !deployedUrl.startsWith('https://')) {
          const fixedUrl = 'https://' + deployedUrl;
          this.logger.info('🔧 Attempting to fix URL by adding HTTPS protocol:', fixedUrl);

          if (this.isValidUrl(fixedUrl)) {
            this.logger.info('✅ Fixed URL is valid, using corrected URL:', fixedUrl);
            this.previewUrlSubject.next(fixedUrl);
            return;
          } else {
            this.logger.warn('❌ Even with HTTPS protocol, URL is still invalid:', fixedUrl);
          }
        }

        return;
      }

      this.logger.info('✅ Successfully extracted deployed URL from ref_code metadata:', deployedUrl);
      this.logger.info('🔗 URL validation passed, setting preview URL');
      this.logger.info('📋 URL will be processed by component for iframe display');

      // **REQUIREMENT**: Set the new URL - will be sanitized in component using DomSanitizer
      this.previewUrlSubject.next(deployedUrl);

      this.logger.info('🎉 Deployed preview URL set successfully for iframe display');
      this.logger.info('🔒 URL will be sanitized in component using DomSanitizer.bypassSecurityTrustResourceUrl()');
      this.logger.info('🎯 Preview tab will be enabled with "once shown, always shown" logic');

    } catch (error) {
      this.logger.error('💥 Failed to process deployed preview URL from ref_code metadata:', error);
      this.logger.error('📋 ref_code metadata:', refCodeMetadata);
      this.logger.error('📋 Error details:', error instanceof Error ? error.message : 'Unknown error');

      // **REQUIREMENT**: Clear preview URL on error to prevent stale data
      this.previewUrlSubject.next('');
    }
  }

  /**
   * Process failed deployment for new workflow
   * Shows error page in preview tab with retry mechanism
   */
  private processFailedDeployment(): void {
    // Set a special error URL that the preview component can recognize
    this.previewUrlSubject.next('ERROR_DEPLOYMENT_FAILED');
    this.logger.info('Failed deployment processed - error page will be shown in preview');
  }

  /**
   * Validate URL format for deployed preview URLs
   * Ensures the extracted URL is a valid HTTP/HTTPS URL before processing
   *
   * **SECURITY REQUIREMENTS**:
   * - Only allows HTTP and HTTPS protocols
   * - Validates URL structure and format
   * - Prevents malicious URL injection
   */
  private isValidUrl(url: string): boolean {
    try {
      // **REQUIREMENT**: Basic string validation
      if (!url || typeof url !== 'string' || url.trim() === '') {
        this.logger.warn('🔒 URL validation failed: empty or invalid string');
        return false;
      }

      // **REQUIREMENT**: Create URL object for validation
      const urlObject = new URL(url.trim());

      // **REQUIREMENT**: Only allow HTTP and HTTPS protocols for security
      const isValidProtocol = urlObject.protocol === 'http:' || urlObject.protocol === 'https:';

      if (!isValidProtocol) {
        this.logger.warn('🔒 URL validation failed: invalid protocol', urlObject.protocol);
        this.logger.warn('📋 Only HTTP and HTTPS protocols are allowed');
        return false;
      }

      // **REQUIREMENT**: Validate hostname exists
      if (!urlObject.hostname || urlObject.hostname.trim() === '') {
        this.logger.warn('🔒 URL validation failed: missing or empty hostname');
        return false;
      }

      this.logger.info('🔒 URL validation passed:', {
        protocol: urlObject.protocol,
        hostname: urlObject.hostname,
        pathname: urlObject.pathname
      });

      return true;
    } catch (error) {
      this.logger.warn('🔒 URL validation failed: invalid URL format', error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  }

  /**
   * Get current progress state
   */
  getCurrentProgress(): ProgressState | null {
    return this.currentProgressSubject.value;
  }

  /**
   * Get current status
   */
  getCurrentStatus(): StatusType | null {
    return this.currentStatusSubject.value;
  }

  /**
   * Reset all subjects to initial state
   */
  reset(): void {
    this.currentProgressSubject.next(null);
    this.currentStatusSubject.next(null);
    this.progressDescriptionSubject.next('');
    this.logContentSubject.next('');
    this.artifactDataSubject.next(null);
    this.fileListSubject.next([]);
    this.codeFilesSubject.next([]);
    this.previewUrlSubject.next('');
    this.projectInfoSubject.next(null);

    // Clear tracking states to allow fresh processing
    this.processedLogStates.clear();
    this.processedArtifactStates.clear();
    this.lastProcessedProgress = null;
    this.lastProcessedStatus = null;
  }

  /**
   * Get current project information
   */
  getCurrentProjectInfo(): ProjectInfo | null {
    return this.projectInfoSubject.value;
  }

  /**
   * Get current preview URL
   */
  getPreviewUrl(): string {
    return this.previewUrlSubject.value;
  }

  /**
   * Map progress state to stepper state for stepper component integration
   */
  mapProgressToStepperState(progress: string): StepperState {
    const progressUpper = progress.toUpperCase();

    switch (progressUpper) {
      case 'OVERVIEW':
      case 'PROJECT_OVERVIEW':
        return StepperState.OVERVIEW;
      case 'SEED_PROJECT_INITIALIZED':
        return StepperState.SEED_PROJECT_INITIALIZED;
      case 'FILE_QUEUE':
        return StepperState.FILE_QUEUE;
      case 'DESIGN_SYSTEM_MAPPED':
      case 'DESIGN_SYSTEM_ANALYZED':
        return StepperState.DESIGN_SYSTEM_MAPPED;
      case 'COMPONENTS_CREATED':
        return StepperState.COMPONENTS_CREATED;
      case 'LAYOUT_ANALYZED':
        return StepperState.LAYOUT_ANALYZED;
      case 'PAGES_GENERATED':
        return StepperState.PAGES_GENERATED;
      case 'BUILD_STARTED':
        return StepperState.BUILD_STARTED;
      case 'BUILD_SUCCEEDED':
        return StepperState.BUILD_SUCCEEDED;
      case 'BUILD_FAILED':
        return StepperState.BUILD_FAILED;
      case 'FILES_GENERATED':
        return StepperState.FILES_GENERATED;
      case 'DEPLOYED':
        return StepperState.DEPLOYED;
      default:
        // Fallback for legacy states
        const progressLower = progress.toLowerCase();
        if (progressLower.includes('project-overview') || progressLower.includes('overview')) {
          return StepperState.OVERVIEW;
        } else if (progressLower.includes('layout') && progressLower.includes('analyzed')) {
          return StepperState.LAYOUT_ANALYZED;
        } else if (progressLower.includes('design') && progressLower.includes('system')) {
          return StepperState.DESIGN_SYSTEM_MAPPED;
        } else if (progressLower.includes('deployed')) {
          return StepperState.DEPLOYED;
        }
        return StepperState.OVERVIEW;
    }
  }

  /**
   * Get stepper display title for current progress
   */
  getStepperDisplayTitle(progress: string): string {
    const stepperState = this.mapProgressToStepperState(progress);
    return StepperStateDisplayTitles[stepperState] || progress;
  }

  /**
   * Check if current state should enable specific tabs
   * Updated for new API structure requirements
   */
  isArtifactsTabEnabled(): boolean {
    const progress = this.currentProgressSubject.value;
    const status = this.currentStatusSubject.value;
    const progressUpper = progress?.toUpperCase();

    // Special cases: Always enable artifacts tab for design system states regardless of status
    if (progress === 'Design_System Analyzed' || progressUpper === 'DESIGN_SYSTEM_MAPPED') {
      return true;
    }

    // Special case: Always enable artifacts tab for layout states regardless of status
    if (progressUpper === 'LAYOUT_ANALYZED' || progress === 'Layout Analyzed') {
      return true;
    }

    // Enable artifacts tab for other specific states when COMPLETED
    return (status === 'COMPLETED') && (
           (progressUpper === 'OVERVIEW') ||
           (progress === 'project-overview')
    );
  }

  isCodeTabEnabled(): boolean {
    const status = this.currentStatusSubject.value;
    const progress = this.currentProgressSubject.value;
    const progressUpper = progress?.toUpperCase();

    // Ensure status is not null for type safety
    if (!status) {
      return false;
    }

    // PRIMARY: Enable code tab when progress is BUILD and status is IN_PROGRESS
    const isBuildInProgress = (progressUpper === 'BUILD' && status === 'IN_PROGRESS');

    // FALLBACK: If BUILD was missed, check if DEPLOY + IN_PROGRESS has code files from previous BUILD
    const isDeployInProgressWithBuildFallback = this.checkDeployFallbackForCodeTab(progressUpper, status);

    const finalResult = isBuildInProgress || isDeployInProgressWithBuildFallback;

    // this.logger.info('🔍 Code tab enabled check (with fallback):', {
    //   progress,
    //   status,
    //   progressUpper,
    //   isBuildInProgress,
    //   isDeployInProgressWithBuildFallback,
    //   finalResult
    // });

    return finalResult;
  }

  /**
   * FALLBACK: Check if DEPLOY + IN_PROGRESS state should enable code tab
   * This handles cases where BUILD + IN_PROGRESS was missed due to polling intervals
   *
   * @param progressUpper Current progress state (uppercase, can be undefined)
   * @param status Current status
   * @returns true if DEPLOY + IN_PROGRESS has BUILD code files available
   */
  private checkDeployFallbackForCodeTab(progressUpper: string | undefined, status: string): boolean {
    // Only apply fallback for DEPLOY + IN_PROGRESS
    if (!progressUpper || progressUpper !== 'DEPLOY' || status !== 'IN_PROGRESS') {
      return false;
    }

    // Check if we have code files from a previous BUILD state
    // This could come from prev_metadata or history that contains BUILD files
    const hasCodeFilesFromBuild = this.hasCodeFilesFromPreviousBuild();

    // this.logger.info('🔄 DEPLOY fallback check for code tab:', {
    //   progressUpper,
    //   status,
    //   hasCodeFilesFromBuild,
    //   codeFilesCount: this.codeFilesSubject.value?.length || 0
    // });

    return hasCodeFilesFromBuild;
  }

  /**
   * Check if code files are available from a previous BUILD state
   * This checks both current code files and attempts to retrieve from multiple sources
   * ENHANCED: Now includes current metadata array in the search
   *
   * Search order:
   * 1. Current loaded code files
   * 2. Current metadata array (for DEPLOY + IN_PROGRESS)
   * 3. prev_metadata array
   * 4. history array (BUILD states)
   *
   * @returns true if BUILD code files are available
   */
  private hasCodeFilesFromPreviousBuild(): boolean {
    // First, check if we already have code files loaded
    const currentCodeFiles = this.codeFilesSubject.value;
    if (currentCodeFiles && currentCodeFiles.length > 0) {
      // this.logger.info('✅ Code files already available from previous BUILD state:', currentCodeFiles.length);
      return true;
    }

    // If no current code files, try to retrieve from multiple sources
    // This will check current metadata, prev_metadata, and history for BUILD files
    const retrievedFiles = this.retrieveBuildFilesFromHistory();

    if (retrievedFiles && retrievedFiles.length > 0) {
      // this.logger.info('✅ Retrieved BUILD code files from fallback sources:', retrievedFiles.length);
      // Update the code files subject with retrieved files
      this.codeFilesSubject.next(retrievedFiles);
      return true;
    }

    // this.logger.info('❌ No BUILD code files found in any available sources');
    return false;
  }

  /**
   * Retrieve BUILD code files from current metadata, history, or prev_metadata
   * This method specifically looks for BUILD state files that may have been missed
   * ENHANCED: Now also checks current metadata array for DEPLOY + IN_PROGRESS state
   *
   * @returns FileData array if BUILD files are found, null otherwise
   */
  private retrieveBuildFilesFromHistory(): FileData[] | null {
    // Get the last processed response if available
    const lastResponse = this.getLastProcessedResponse();
    if (!lastResponse) {
      return null;
    }

    // ENHANCED: First check current metadata array for DEPLOY + IN_PROGRESS state
    // This handles cases where BUILD files are included in DEPLOY metadata
    const currentProgress = this.currentProgressSubject.value;
    const currentStatus = this.currentStatusSubject.value;

    if (currentProgress?.toUpperCase() === 'DEPLOY' && currentStatus === 'IN_PROGRESS' &&
        lastResponse.metadata && lastResponse.metadata.length > 0) {

      const buildFiles = this.extractBuildFilesFromMetadata(lastResponse.metadata);
      if (buildFiles && buildFiles.length > 0) {
        // this.logger.info('🔄 Found BUILD files in current DEPLOY metadata:', buildFiles.length);
        return buildFiles;
      }
    }

    // Check prev_metadata (higher priority for historical data)
    if (lastResponse.prev_metadata && lastResponse.prev_metadata.length > 0) {
      const buildFiles = this.extractBuildFilesFromMetadata(lastResponse.prev_metadata);
      if (buildFiles && buildFiles.length > 0) {
        // this.logger.info('🔄 Found BUILD files in prev_metadata:', buildFiles.length);
        return buildFiles;
      }
    }

    // Check history for BUILD states
    if (lastResponse.history && lastResponse.history.length > 0) {
      for (const historyItem of lastResponse.history) {
        if (historyItem.progress?.toUpperCase() === 'BUILD' &&
            (historyItem.status === 'IN_PROGRESS' || historyItem.status === 'COMPLETED') &&
            historyItem.metadata && historyItem.metadata.length > 0) {

          const buildFiles = this.extractBuildFilesFromMetadata(historyItem.metadata);
          if (buildFiles && buildFiles.length > 0) {
            // this.logger.info('🔄 Found BUILD files in history:', buildFiles.length);
            return buildFiles;
          }
        }
      }
    }

    return null;
  }

  /**
   * Extract BUILD code files from metadata array
   *
   * @param metadata Metadata array to search
   * @returns FileData array if files found, null otherwise
   */
  private extractBuildFilesFromMetadata(metadata: MetadataItem[]): FileData[] | null {
    const filesMetadata = metadata.find(m => m.type === 'files');
    if (!filesMetadata || !filesMetadata.data) {
      return null;
    }

    try {
      const files = typeof filesMetadata.data === 'string'
        ? JSON.parse(filesMetadata.data)
        : filesMetadata.data;

      if (Array.isArray(files)) {
        const fileData: FileData[] = files.map(file => ({
          path: file.fileName || file.path || 'Unknown file',
          code: file.code || file.content || ''
        }));

        return fileData.length > 0 ? fileData : null;
      }
    } catch (error) {
      // this.logger.warn('Failed to parse BUILD files from metadata:', error);
    }

    return null;
  }

  /**
   * Get the last processed response for fallback operations
   *
   * @returns Last response if available, null otherwise
   */
  private getLastProcessedResponse(): NewPollingResponseWithPrevMetadata | null {
    return this.lastProcessedResponse;
  }

  isPreviewEnabled(): boolean {
    const progress = this.currentProgressSubject.value;
    const status = this.currentStatusSubject.value;

    // Enable preview ONLY when DEPLOY state is COMPLETED
    // Strict check: only DEPLOY (not DEPLOYED or other states) and only COMPLETED status
    const isDeployCompleted = (progress === 'DEPLOY' && status === 'COMPLETED');

    // this.logger.info('🔍 Preview tab enabled check (STRICT):', {
    //   progress,
    //   status,
    //   isDeployCompleted,
    //   finalResult: isDeployCompleted
    // });

    return isDeployCompleted;
  }

  /**
   * ENHANCED: Process test data from test.json for development/testing
   * This method simulates real polling responses using the test.json structure
   */
  processTestJsonData(testData: any[]): void {
    if (!Array.isArray(testData)) {
      // this.logger.warn('Test data is not an array');
      return;
    }

    // this.logger.info('Processing test.json data with', testData.length, 'responses');

    // Reset state tracking for test data
    this.resetStateTracking();

    // Process each response in the test data
    testData.forEach((response, index) => {
      if (response.details) {
        // this.logger.info(`Processing test response ${index + 1}:`, response.details.progress);

        // Convert test response to our expected format
        const pollingResponse: NewPollingResponseWithPrevMetadata = {
          status: response.details.status,
          progress: response.details.progress,
          progress_description: response.details.progress_description,
          log: response.details.log,
          metadata: response.details.metadata || [],
          history: response.details.history || [],
          prev_metadata: [] // Not present in test.json but part of our interface
        };

        // Process using our new workflow method
        this.processNewWorkflowResponse(pollingResponse);
      }
    });

    // this.logger.info('Test.json data processing completed');
  }

  /**
   * Reset state tracking for logs and artifacts
   * This is useful when starting a new session or testing
   */
  resetStateTracking(): void {
    this.lastProcessedProgress = null;
    this.lastProcessedStatus = null;
    this.processedLogStates.clear();
    this.processedArtifactStates.clear(); // Clear processed artifacts to allow fresh processing
    // this.logger.info('State tracking reset - logs and artifacts cleared');
  }

  /**
   * Get current state tracking info for debugging
   */
  getStateTrackingInfo(): any {
    return {
      lastProcessedProgress: this.lastProcessedProgress,
      lastProcessedStatus: this.lastProcessedStatus,
      processedStatesCount: this.processedLogStates.size,
      processedStates: Array.from(this.processedLogStates)
    };
  }
}
