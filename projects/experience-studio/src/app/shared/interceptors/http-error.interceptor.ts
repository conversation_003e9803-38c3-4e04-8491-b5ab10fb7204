import {
  HttpRequest,
  HttpHandlerFn,
  HttpEvent,
  HttpErrorResponse,
  HttpInterceptorFn
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { inject } from '@angular/core';
import { GlobalErrorHandlerService } from '../services/error-handling/global-error-handler.service';
import { createLogger } from '../utils/logger';

/**
 * HTTP Error Interceptor
 * Centralized error handling for all HTTP requests
 * Replaces duplicated error handling code across services
 */
export const HttpErrorInterceptor: HttpInterceptorFn = (
  request: HttpRequest<any>,
  next: HttpHandlerFn
): Observable<HttpEvent<any>> => {
  const globalErrorHandler = inject(GlobalErrorHandlerService);
  const logger = createLogger('HttpErrorInterceptor');

  return next(request).pipe(
    catchError((error: HttpErrorResponse) => {
      // Log the error for debugging
      logger.error(`HTTP Error for ${request.method} ${request.url}:`, error);

      // Handle the error through the global error handler
      globalErrorHandler.handleHttpError(error, request);

      // Re-throw the error so services can still handle specific cases if needed
      return throwError(() => error);
    })
  );
};
