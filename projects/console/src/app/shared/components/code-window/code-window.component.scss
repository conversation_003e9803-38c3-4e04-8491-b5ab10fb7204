.code-window-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.splitscreen-container {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  
  &.is-resizing {
    cursor: col-resize;
    user-select: none;
  }
}

// Left Panel Styles
.left-panel {
  width: 40%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e0e0e0;
  transition: width 0.3s ease;
  
  &.collapsed {
    width: 0;
    min-width: 0;
    border-right: none;
    overflow: hidden;
  }
}

// Right Panel Styles
.right-panel {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Panel Header Styles
.panel-header {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  
  .header-left {
    display: flex;
    align-items: center;
  }
}

// Panel Content Styles
.panel-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

// Buttons
.home-button,
.toggle-panel-button {
  background: none;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 8px;
  
  &:hover {
    background-color: #f0f0f0;
  }
  
  .icon {
    font-size: 18px;
  }
}

// Tabs
.tabs-container {
  display: flex;
  height: 100%;
  align-items: center;
}

.tab-button {
  background: none;
  border: none;
  padding: 0 16px;
  height: 36px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  position: relative;
  margin-right: 4px;
  
  &:hover {
    color: #333;
  }
  
  &.active {
    color: #6566cd;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -12px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #6566cd;
    }
  }
}

// Loading View
.loading-view {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #6566cd;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 16px;
  color: #666;
}

// Editor View
.editor-view {
  display: flex;
  width: 100%;
  height: 100%;
}

.file-explorer {
  width: 220px;
  height: 100%;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.file-explorer-header {
  padding: 16px;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #e0e0e0;
}

.file-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.file-item {
  padding: 8px 16px;
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  
  &:hover {
    background-color: #f0f0f0;
  }
  
  .file-name {
    margin-left: 8px;
  }
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 16px;
  height: 40px;
}

.editor-tab {
  padding: 0 16px;
  height: 40px;
  display: flex;
  align-items: center;
  font-size: 13px;
  border-right: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  
  &:hover {
    background-color: #f0f0f0;
  }
}

.code-display {
  flex: 1;
  overflow: auto;
  padding: 16px;
  background-color: #fafafa;
  
  pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    
    code {
      white-space: pre;
    }
  }
}

// Preview View
.preview-view {
  width: 100%;
  height: 100%;
}

.preview-iframe-container {
  width: 100%;
  height: 100%;
  background-color: white;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  text-align: center;
  
  h2 {
    margin-bottom: 16px;
    color: #333;
  }
  
  p {
    max-width: 500px;
    margin-bottom: 24px;
    color: #666;
    line-height: 1.5;
  }
  
  button {
    padding: 8px 16px;
    background-color: #6566cd;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      background-color: #5556bd;
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}