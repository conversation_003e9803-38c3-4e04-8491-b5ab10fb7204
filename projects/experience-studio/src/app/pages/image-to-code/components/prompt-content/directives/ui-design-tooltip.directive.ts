import { Directive, Input, ElementRef, Renderer2, <PERSON><PERSON>nit, On<PERSON>estroy, HostListener } from '@angular/core';

/**
 * Custom tooltip directive for UI Design mode
 * Shows contextual tooltips for disabled states during generation
 */
@Directive({
  selector: '[appUIDesignTooltip]',
  standalone: true
})
export class UIDesignTooltipDirective implements OnInit, OnDestroy {
  @Input('appUIDesignTooltip') tooltipText: string = '';
  @Input() showTooltip: boolean = false;
  @Input() tooltipPosition: 'top' | 'bottom' | 'left' | 'right' = 'top';

  private tooltipElement: HTMLElement | null = null;
  private showTimeout: any;
  private hideTimeout: any;

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnInit(): void {
    // Set up initial state
    this.updateTooltipVisibility();
  }

  ngOnDestroy(): void {
    this.removeTooltip();
    this.clearTimeouts();
  }

  @HostListener('mouseenter')
  onMouseEnter(): void {
    if (this.showTooltip && this.tooltipText) {
      this.clearTimeouts();
      this.showTimeout = setTimeout(() => {
        this.createTooltip();
      }, 300); // Delay before showing tooltip
    }
  }

  @HostListener('mouseleave')
  onMouseLeave(): void {
    this.clearTimeouts();
    this.hideTimeout = setTimeout(() => {
      this.removeTooltip();
    }, 100); // Small delay before hiding
  }

  @HostListener('click')
  onClick(): void {
    // Hide tooltip immediately on click
    this.clearTimeouts();
    this.removeTooltip();
  }

  private createTooltip(): void {
    if (this.tooltipElement || !this.showTooltip || !this.tooltipText) {
      return;
    }

    // Create tooltip element
    this.tooltipElement = this.renderer.createElement('div');
    this.renderer.addClass(this.tooltipElement, 'ui-design-tooltip');
    this.renderer.addClass(this.tooltipElement, `tooltip-${this.tooltipPosition}`);
    
    // Set tooltip content
    const textNode = this.renderer.createText(this.tooltipText);
    this.renderer.appendChild(this.tooltipElement, textNode);

    // Add tooltip to body
    this.renderer.appendChild(document.body, this.tooltipElement);

    // Position tooltip
    this.positionTooltip();

    // Add animation class
    setTimeout(() => {
      if (this.tooltipElement) {
        this.renderer.addClass(this.tooltipElement, 'tooltip-visible');
      }
    }, 10);
  }

  private positionTooltip(): void {
    if (!this.tooltipElement) return;

    const hostRect = this.elementRef.nativeElement.getBoundingClientRect();
    const tooltipRect = this.tooltipElement.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    let top: number;
    let left: number;

    switch (this.tooltipPosition) {
      case 'top':
        top = hostRect.top + scrollTop - tooltipRect.height - 8;
        left = hostRect.left + scrollLeft + (hostRect.width - tooltipRect.width) / 2;
        break;
      case 'bottom':
        top = hostRect.bottom + scrollTop + 8;
        left = hostRect.left + scrollLeft + (hostRect.width - tooltipRect.width) / 2;
        break;
      case 'left':
        top = hostRect.top + scrollTop + (hostRect.height - tooltipRect.height) / 2;
        left = hostRect.left + scrollLeft - tooltipRect.width - 8;
        break;
      case 'right':
        top = hostRect.top + scrollTop + (hostRect.height - tooltipRect.height) / 2;
        left = hostRect.right + scrollLeft + 8;
        break;
      default:
        top = hostRect.top + scrollTop - tooltipRect.height - 8;
        left = hostRect.left + scrollLeft + (hostRect.width - tooltipRect.width) / 2;
    }

    // Ensure tooltip stays within viewport
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    if (left < 0) left = 8;
    if (left + tooltipRect.width > viewportWidth) left = viewportWidth - tooltipRect.width - 8;
    if (top < scrollTop) top = scrollTop + 8;
    if (top + tooltipRect.height > scrollTop + viewportHeight) top = scrollTop + viewportHeight - tooltipRect.height - 8;

    this.renderer.setStyle(this.tooltipElement, 'position', 'absolute');
    this.renderer.setStyle(this.tooltipElement, 'top', `${top}px`);
    this.renderer.setStyle(this.tooltipElement, 'left', `${left}px`);
    this.renderer.setStyle(this.tooltipElement, 'z-index', '10000');
  }

  private removeTooltip(): void {
    if (this.tooltipElement) {
      this.renderer.removeChild(document.body, this.tooltipElement);
      this.tooltipElement = null;
    }
  }

  private clearTimeouts(): void {
    if (this.showTimeout) {
      clearTimeout(this.showTimeout);
      this.showTimeout = null;
    }
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }
  }

  private updateTooltipVisibility(): void {
    if (!this.showTooltip) {
      this.removeTooltip();
    }
  }
}
