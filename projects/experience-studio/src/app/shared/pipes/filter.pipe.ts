import { Pipe, PipeTransform } from '@angular/core';

/**
 * Filter pipe to filter arrays by property value
 * Usage: array | filter:'propertyName':'value'
 */
@Pipe({
  name: 'filter',
  standalone: true
})
export class FilterPipe implements PipeTransform {
  /**
   * Filter an array of objects by a property value
   * @param items The array to filter 
   * @param property The property to filter by
   * @param value The value to filter for
   * @returns Filtered array
   */
  transform(items: any[], property: string, value: any): any[] {
    if (!items || !property || value === undefined) {
      return items;
    }
    
    return items.filter(item => item[property] === value);
  }
}
