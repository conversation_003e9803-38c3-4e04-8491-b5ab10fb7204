// import { TestBed } from '@angular/core/testing';
// import { VSCodeExportService } from './vscode-export.service';
// import { ToastService } from '../toast.service';

// describe('VSCodeExportService', () => {
//   let service: VSCodeExportService;
//   let mockToastService: jasmine.SpyObj<ToastService>;

//   beforeEach(() => {
//     const toastSpy = jasmine.createSpyObj('ToastService', ['info', 'success', 'error', 'warning']);

//     TestBed.configureTestingModule({
//       providers: [
//         VSCodeExportService,
//         { provide: ToastService, useValue: toastSpy }
//       ]
//     });

//     service = TestBed.inject(VSCodeExportService);
//     mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
//   });

//   it('should be created', () => {
//     expect(service).toBeTruthy();
//   });

//   it('should export to VSCode with valid options', (done) => {
//     const options = {
//       projectName: 'test-project',
//       files: [
//         { name: 'index.html', content: '<html></html>', path: 'index.html' },
//         { name: 'style.css', content: 'body {}', path: 'style.css' }
//       ],
//       openInVSCode: true,
//       downloadFallback: true
//     };

//     service.exportToVSCode(options).subscribe({
//       next: (result) => {
//         expect(result.success).toBe(true);
//         expect(result.method).toBe('download-fallback');
//         expect(result.message).toContain('Project downloaded');
//         done();
//       },
//       error: (error) => {
//         fail('Export should not fail: ' + error);
//         done();
//       }
//     });
//   });

//   it('should handle empty files array', (done) => {
//     const options = {
//       projectName: 'test-project',
//       files: [],
//       openInVSCode: true,
//       downloadFallback: true
//     };

//     service.exportToVSCode(options).subscribe({
//       next: () => {
//         fail('Export should fail with empty files');
//         done();
//       },
//       error: (error) => {
//         expect(error.message).toContain('No files available');
//         done();
//       }
//     });
//   });

//   it('should handle missing project name', (done) => {
//     const options = {
//       projectName: '',
//       files: [{ name: 'test.html', content: 'test', path: 'test.html' }],
//       openInVSCode: true,
//       downloadFallback: true
//     };

//     service.exportToVSCode(options).subscribe({
//       next: () => {
//         fail('Export should fail with empty project name');
//         done();
//       },
//       error: (error) => {
//         expect(error.message).toContain('Project name is required');
//         done();
//       }
//     });
//   });
// });
