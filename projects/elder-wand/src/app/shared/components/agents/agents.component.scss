// Generic electric card styles
.electric-card {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(140, 101, 247, 0.1), rgba(232, 67, 147, 0.1));
    opacity: 0;
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    border-radius: 12px;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(173, 216, 230, 0.8) 25%,
        rgba(255, 255, 255, 0.8) 50%,
        rgba(173, 216, 230, 0.8) 75%,
        rgba(255, 255, 255, 0.8) 100%);
    background-size: 400% 100%;
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    pointer-events: none;
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.7)) drop-shadow(0 0 4px rgba(173, 216, 230, 0.5));
  }

  .card-content {
    position: relative;
    z-index: 1;
    padding: 9px;
  }
}

// Agent specific styles
.agents-container {
  padding: 20px;
  display: flex;
  flex-direction: column;

  .agents-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .title-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;

      .sparkle {
        font-size: 28px;
      }

      h1 {
        font-family: Mulish;
        font-size: 40px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: -0.76px;
        background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 118.04%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0;
      }
    }

    .task-space {
      padding: 8px 16px;
      border-radius: 4px;
    }
  }

  .agents-grid {
    margin-bottom: 40px;
  }

  .agent-card {
    border: 1px solid transparent;
    /* Add a transparent border by default */
    transition: border-color 0.3s ease;
    /* Smooth transition for border color change */
    @extend .electric-card;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 0px;
      margin-bottom: 12px;

      h2 {
        color: rgba(0, 0, 0, 0.80);
        font-family: Mulish;
        font-size: 24px;
        font-style: normal;
        font-weight: 700;
        line-height: 100%;
        margin: 0;
      }

      .rating {
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;

        .star {
          color: #FFD700;
        }
      }
    }

    .description {
      color: var(--Neutral-N-700, #52577A);
      text-align: left;
      font-family: Mulish;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 150%;
      margin-bottom: 24px;
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0px;
      width: 100%;

      .users {
        color: #858aad;
        font-family: Mulish;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 150%;
        display: flex;
        align-items: center;

        awe-icons svg {
          height: 20px;
          width: 20px;
          margin-top: 5px;
        }
      }

      .studio {
        font-family: Mulish;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%;
        padding: 2px 4px;
        border-radius: 6px;
      }
    }
  }

.explore-more {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

awe-button span {
  font-size: 24px;
}

awe-button button {
  border-radius: 8px;
}

  // Agent Details Panel Styles
  .agent-details-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    z-index: 1000;
    display: flex;
    justify-content: flex-end;
    animation: fadeIn 0.3s ease;
    font-family: 'Mulish', sans-serif;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes slideIn {
    from {
      transform: translateX(100%);
    }

    to {
      transform: translateX(0);
    }
  }

  .agent-details-panel {
    width: 650px;
    height: 100%;
    background-color: #fff;
    box-shadow: -4px 0 24px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    animation: slideIn 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative;

    .details-header {
      padding: 0;
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 10;

      .close-btn {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 24px;
        color: #666;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
      }
    }

    .details-content {
      padding: 20px;
      display: flex;
      flex-direction: column;
      min-height: 100%;
      justify-content: space-between;

      .upper-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 30px;
        padding-bottom: 30px;
      }

      .details-title {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
        margin-top: 0;
        padding-right: 30px;

        .sparkle {
          font-size: 24px;
        }

        h2 {
          color: var(--Text-Body, #33364D);
          font-family: 'Mulish', sans-serif;
          font-size: 2.5rem;
          font-weight: 700;
          margin: 0;
          line-height: 150%;
          letter-spacing: 0.4px;
        }
      }

      .details-description {
        color: #52577A;
        font-family: 'Mulish', sans-serif;
        font-size: 1.5rem;
        font-weight: 400;
        line-height: 150%;
        margin-bottom: 15px;
      }

      .details-metrics {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: flex-start;
        gap: 15px;
        margin-bottom: 0;
        padding: 10px 0;

        .metric {
          flex: 1;
          min-width: auto;
          text-align: center;

          .label-1 {
            font-family: 'Mulish', sans-serif;
            color: #858AAD;
            font-weight: 500;
            font-size: 1rem;
            line-height: 1.1;
            letter-spacing: 0;
            text-align: center;
            display: block;
            margin-bottom: 10px;
          }

          .label {
            display: block;
            color: #666D99;
            text-align: center;
            font-family: 'Mulish', sans-serif;
            font-size: 1.25rem;
            font-weight: 600;
            line-height: 97%;
            margin-bottom: 10px;
            letter-spacing: 0;
          }

          .developer {
            display: flex;
            justify-content: center;
            color: #666D99;
            text-align: center;
            font-family: 'Mulish', sans-serif;
            font-size: 1.25rem;
            font-weight: 600;
            line-height: 97%;
            margin-bottom: 10px;
            letter-spacing: 0;
          }

          .score {
            font-family: 'Mulish', sans-serif;
            font-weight: 600;
            font-size: 24px;
            line-height: 1.5;
            margin-bottom: 16px;
            letter-spacing: 0;
            color: #52577A;
          }

          .code-icon {
            font-size: 15px;
            font-weight: 500;
            color: #333;
            background-color: transparent;
            padding: 0;
            justify-content: center;
            display: flex;
            flex-direction: column;
            line-height: 1.4;
            font-family: 'Mulish', sans-serif;
          }

          .rating {
            color: var(--Text-Title, #14161F);
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%;

            .star {
              color: #FFD700;
            }

            .star1 {
              margin-bottom: 16px;
            }
          }
        }
      }

      .details-section {
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        h3 {
          color: #000;
          font-family: Mulish;
          font-size: 2rem;
          font-style: normal;
          font-weight: 600;
          line-height: 150%;
          letter-spacing: 0.32px;
        }

        p {
          color: #52577A;
          font-family: Mulish;
          font-size: 1.5rem;
          font-style: normal;
          font-weight: 400;
          line-height: 150%;
        }

        .type-badge {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          border-radius: 4px;
          font-size: 0.875;
          font-family: 'Mulish', sans-serif;

          .icon {
            font-size: 1rem;
          }
        }
      }

      .details-section-2 {
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        h3 {
          color: #33364D;
          font-family: Mulish;
          font-size: 2rem;
          font-style: normal;
          font-weight: 600;
          line-height: 150%;
          letter-spacing: 0.32px;
        }

        p {
          color: #52577A;
          font-family: Mulish;
          font-size: 1.5rem;
          font-style: normal;
          font-weight: 400;
          line-height: 150%;
        }

        .code-mig {
          font-family: 'Mulish', sans-serif;
          color: #52577A;
          font-weight: 400;
          font-size: 24px;
          line-height: 150%;
          letter-spacing: 0;
        }

        .type-badge {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          border-radius: 4px;
          font-size: 14px;
          font-family: 'Mulish', sans-serif;

          .icon {
            font-size: 16px;
          }
        }
      }

      .action-button {
        margin-top: auto;
        padding-top: 20px;

        .go-to-playground-btn {
          width: 100%;
          background: linear-gradient(90deg, #8C65F7 0%, #E84393 100%);
          color: white;
          font-family: 'Mulish', sans-serif;
          padding: 12px 24px;
          border-radius: 8px;
          font-weight: 600;
          font-size: 16px;
          display: flex;
          justify-content: center;
          align-items: center;
          text-align: center;
          height: 48px;
          border: none;
          cursor: pointer;
          outline: none;
        }
      }
    }
  }
}

// Global styles for when details panel is open
:host ::ng-deep body.details-panel-open {
  overflow: hidden;
}