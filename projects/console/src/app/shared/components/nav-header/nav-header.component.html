<awe-header theme="light" class="py-1">
  <div left-content class="mt-4">
    <img [src]="logoSrc" class="px-2" alt="Logo" />
  </div>

  <div center-content class="nav-menu">
    <div class="nav-items">
      <app-nav-item
        *ngFor="let item of navItems; let i = index"
        [label]="item.label"
        [route]="item.route"
        [selected]="item.selected"
        [hasDropdown]="item.hasDropdown"
        [dropdownOpen]="item.dropdownOpen || false"
        [dropdownItems]="item.dropdownItems || []"
        [icon]="item.icon"
        (toggleDropdownEvent)="toggleDropdown(i)"
        (navigateEvent)="navigateTo($event)"
        (selectEvent)="selectMenuItem(i)"
        (dropdownItemSelected)="onDropdownItemSelected($event, i)"
        class="nav-item-wrapper"
      >
      </app-nav-item>
    </div>
  </div>

  <div
    right-content
    class="gap-4 d-flex align-items-center header-right-content"
  >
    <div
      class="cursor-pointer d-flex justify-content-center align-items-center"
    >
      <img [src]="themeMenuIcon" alt="Menu" />
    </div>
    <div class="theme-toggle-wrapper">
      <app-theme-toggle></app-theme-toggle>
    </div>
    <div
      class="cursor-pointer d-flex justify-content-center align-items-center"
    >
      <img [src]="userAvatar" alt="User Profile" />
    </div>
  </div>
</awe-header>
