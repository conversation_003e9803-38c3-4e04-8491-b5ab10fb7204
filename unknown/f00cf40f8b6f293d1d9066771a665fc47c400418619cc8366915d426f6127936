import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormControl, FormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CardComponent } from '../../../../shared/components/card/card.component';
import { FormFieldComponent } from '../../../../shared/components/form-field/form-field.component';
import { MODEL_DEFINITIONS, ModelDefinition } from '../../../../shared/mock-data/model-definitions';

@Component({
  selector: 'app-create-knowledge-base',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    CardComponent,
    FormFieldComponent
  ],
  templateUrl: './create-knowledge-base.component.html',
  styleUrls: ['./create-knowledge-base.component.scss']
})
export class CreateKnowledgeBaseComponent implements OnInit {
  knowledgeBaseId: string | null = null;
  isEditMode: boolean = false;
  knowledgeBaseForm: FormGroup;
  
  // Retriever options
  retrieverOptions: string[] = ['Default', 'Parent Doc'];
  selectedRetriever: string = 'Default';
  
  // Split size configurations
  splitSize: number = 0.3;
  parentSplitSize: number = 0.3;
  childSplitSize: number = 0.3;
  
  // Uploaded files
  uploadedFiles: any[] = [
    { name: 'Test file knowledge-base', type: 'docx', size: '24 kb', date: '24 Feb 2025' },
    { name: 'Test file knowledge-base', type: 'pdf', size: '24 kb', date: '24 Feb 2025' },
    { name: 'Test file knowledge-base', type: 'pptx', size: '24 kb', date: '24 Feb 2025' }
  ];
  
  // Embedding model selection
  embeddingModels: ModelDefinition[] = MODEL_DEFINITIONS.filter(model => model.modelType === 'Embedding' || 
    ['GPT 3.0', 'GPT 4', 'Google Gemini', 'Claude Sonnet', 'Amazon Bedrock', 'Azure OpenAI'].includes(`${model.name} ${model.version}`));
  selectedEmbeddingModel: ModelDefinition | null = null;
  searchModelText: string = '';
  
  // Dropdown options for embedding model selection
  embeddingModelOptions: { value: string; label: string }[] = [
    { value: "Google-Google Gemini", label: "Google Gemini" }
  ];
  
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.knowledgeBaseForm = this.fb.group({
      // Basic details
      name: [''],
      description: [''],
      
      // Filters
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],
      
      // Configuration
      retriever: ['Default'],
      splitSize: [0.3],
      parentSplitSize: [0.3],
      childSplitSize: [0.3],
      embeddingModel: ['Google-Google Gemini']
    });
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.knowledgeBaseId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.knowledgeBaseId;
    
    if (this.isEditMode && this.knowledgeBaseId) {
      // In a real app, you would fetch the knowledge base data by ID
      console.log(`Editing knowledge base with ID: ${this.knowledgeBaseId}`);
      // this.loadKnowledgeBaseData(this.knowledgeBaseId);
    }

    // Initialize with some embedding models for the UI
    this.embeddingModels = [
      {
        provider: 'Google',
        name: 'Google Gemini',
        version: '',
        description: 'Conversation, Reasoning',
        modelType: 'Embedding'
      },
      {
        provider: 'OpenAI',
        name: 'GPT 3.0',
        version: '',
        description: 'Conversation, Reasoning',
        modelType: 'Embedding'
      },
      {
        provider: 'OpenAI',
        name: 'GPT 4',
        version: '',
        description: 'Conversation, Reasoning',
        modelType: 'Embedding'
      },
      {
        provider: 'Anthropic',
        name: 'Claude Sonnet',
        version: '',
        description: 'Conversation, Reasoning',
        modelType: 'Embedding'
      },
      {
        provider: 'Amazon',
        name: 'Amazon Bedrock',
        version: '',
        description: 'Conversation, Reasoning',
        modelType: 'Embedding'
      },
      {
        provider: 'Microsoft',
        name: 'Azure OpenAI',
        version: '',
        description: 'Conversation, Reasoning',
        modelType: 'Embedding'
      }
    ];
    
    // Setup dropdown options for embedding models
    this.embeddingModelOptions = [
      { value: "Google-Google Gemini", label: "Google Gemini" },
      ...this.embeddingModels
        .filter(model => model.name !== 'Google Gemini') // Skip Gemini as it's already the default
        .map(model => ({
          value: `${model.provider}-${model.name}`,
          label: `${model.name}${model.version ? ` (${model.version})` : ''}`
        }))
    ];
  }

  onSave(): void {
    console.log('Form data:', this.knowledgeBaseForm.value);
    console.log('Selected retriever:', this.selectedRetriever);
    console.log('Selected embedding model:', this.selectedEmbeddingModel);
    console.log('Uploaded files:', this.uploadedFiles);
    
    if (this.isEditMode) {
      console.log('Updating existing knowledge base');
    } else {
      console.log('Creating new knowledge base');
    }
    
    this.router.navigate(['/libraries/knowledge-base']);
  }

  onExit(): void {
    this.router.navigate(['/libraries/knowledge-base']);
  }
  
  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.knowledgeBaseForm.get(name) as FormControl;
  }
  
  // Method to handle retriever selection
  selectRetriever(retriever: string): void {
    this.selectedRetriever = retriever;
    this.knowledgeBaseForm.get('retriever')?.setValue(retriever);
  }
  
  // Method to handle embedding model selection
  selectEmbeddingModel(model: ModelDefinition): void {
    this.selectedEmbeddingModel = model;
  }
  
  // Helper to check if a model is selected
  isModelSelected(model: ModelDefinition): boolean {
    return this.selectedEmbeddingModel === model;
  }
  
  // Method to handle file uploads
  onFileSelected(event: any): void {
    const files = event.target.files;
    if (files) {
      // Process files...
      console.log('Files selected:', files);
    }
  }
  
  // Method to handle file drop
  onFileDrop(event: any): void {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files) {
      // Process dropped files...
      console.log('Files dropped:', files);
    }
  }
  
  // Method to handle split size change
  onSplitSizeChange(event: any): void {
    this.splitSize = event.target.value;
    this.knowledgeBaseForm.get('splitSize')?.setValue(this.splitSize);
  }
  
  // Method to handle parent split size change
  onParentSplitSizeChange(event: any): void {
    this.parentSplitSize = event.target.value;
    this.knowledgeBaseForm.get('parentSplitSize')?.setValue(this.parentSplitSize);
  }
  
  // Method to handle child split size change
  onChildSplitSizeChange(event: any): void {
    this.childSplitSize = event.target.value;
    this.knowledgeBaseForm.get('childSplitSize')?.setValue(this.childSplitSize);
  }
  
  // Method to delete a file from uploaded files
  deleteFile(file: any): void {
    const index = this.uploadedFiles.findIndex(f => 
      f.name === file.name && f.type === file.type && f.size === file.size
    );
    
    if (index !== -1) {
      this.uploadedFiles.splice(index, 1);
      console.log('File deleted:', file.name);
    }
  }
  
  // Method to filter models by search text
  filterModels(): ModelDefinition[] {
    if (!this.searchModelText) {
      return this.embeddingModels;
    }
    
    const searchLower = this.searchModelText.toLowerCase();
    return this.embeddingModels.filter(model => 
      model.name.toLowerCase().includes(searchLower) || 
      model.provider.toLowerCase().includes(searchLower) ||
      (model.description || '').toLowerCase().includes(searchLower)
    );
  }
} 