<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="mobileGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Phone body -->
  <rect x="6" y="2" width="12" height="20" rx="2.5" ry="2.5" fill="url(#mobileGradient)" stroke="#4c51bf" stroke-width="0.5"/>
  
  <!-- Screen -->
  <rect x="7.5" y="4" width="9" height="14" rx="1" ry="1" fill="url(#screenGradient)"/>
  
  <!-- Screen content lines -->
  <rect x="8.5" y="6" width="7" height="0.8" rx="0.4" fill="#ffffff" opacity="0.9"/>
  <rect x="8.5" y="8" width="5" height="0.8" rx="0.4" fill="#ffffff" opacity="0.7"/>
  <rect x="8.5" y="10" width="6" height="0.8" rx="0.4" fill="#ffffff" opacity="0.7"/>
  
  <!-- App icons -->
  <circle cx="9.5" cy="13" r="0.8" fill="#ffffff" opacity="0.8"/>
  <circle cx="11.5" cy="13" r="0.8" fill="#ffffff" opacity="0.8"/>
  <circle cx="13.5" cy="13" r="0.8" fill="#ffffff" opacity="0.8"/>
  <circle cx="15.5" cy="13" r="0.8" fill="#ffffff" opacity="0.8"/>
  
  <circle cx="9.5" cy="15" r="0.8" fill="#ffffff" opacity="0.6"/>
  <circle cx="11.5" cy="15" r="0.8" fill="#ffffff" opacity="0.6"/>
  <circle cx="13.5" cy="15" r="0.8" fill="#ffffff" opacity="0.6"/>
  <circle cx="15.5" cy="15" r="0.8" fill="#ffffff" opacity="0.6"/>
  
  <!-- Home button -->
  <circle cx="12" cy="19.5" r="1" fill="#ffffff" opacity="0.3" stroke="#4c51bf" stroke-width="0.3"/>
  
  <!-- Speaker -->
  <rect x="10" y="2.8" width="4" height="0.4" rx="0.2" fill="#ffffff" opacity="0.4"/>
</svg>
