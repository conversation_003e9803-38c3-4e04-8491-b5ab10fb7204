import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, inject, DestroyRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, interval } from 'rxjs';
import { map, startWith, takeUntil } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UIDesignViewportService, ViewportDebugInfo } from '../../services/ui-design-viewport.service';
import { createLogger } from '../../../../utils/logger';

@Component({
  selector: 'app-viewport-debug',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="viewport-debug-panel" [class.collapsed]="isCollapsed">
      <div class="debug-header" (click)="toggleCollapse()">
        <h4>
          <i class="bi bi-bug"></i>
          Viewport Debug Info
        </h4>
        <button class="collapse-btn">
          <i class="bi" [class.bi-chevron-up]="!isCollapsed" [class.bi-chevron-down]="isCollapsed"></i>
        </button>
      </div>

      <div class="debug-content" *ngIf="!isCollapsed">
        <div class="debug-section" *ngIf="debugInfo$ | async as info">
          <!-- Viewport Information -->
          <div class="info-group">
            <h5><i class="bi bi-eye"></i> Current Viewport</h5>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">Position X:</span>
                <span class="value">{{ info.viewport.x | number:'1.2-2' }}px</span>
              </div>
              <div class="info-item">
                <span class="label">Position Y:</span>
                <span class="value">{{ info.viewport.y | number:'1.2-2' }}px</span>
              </div>
              <div class="info-item">
                <span class="label">Zoom:</span>
                <span class="value">{{ (info.viewport.zoom * 100) | number:'1.1-1' }}%</span>
              </div>
            </div>
          </div>

          <!-- Container Information -->
          <div class="info-group">
            <h5><i class="bi bi-square"></i> Container</h5>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">Size:</span>
                <span class="value">{{ info.container.width }}×{{ info.container.height }}px</span>
              </div>
              <div class="info-item">
                <span class="label">Center:</span>
                <span class="value">({{ info.container.centerX }}, {{ info.container.centerY }})</span>
              </div>
            </div>
          </div>

          <!-- Content Information -->
          <div class="info-group">
            <h5><i class="bi bi-grid-3x3"></i> Content Bounds</h5>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">Bounds:</span>
                <span class="value">
                  ({{ info.content.bounds.minX }}, {{ info.content.bounds.minY }}) →
                  ({{ info.content.bounds.maxX }}, {{ info.content.bounds.maxY }})
                </span>
              </div>
              <div class="info-item">
                <span class="label">Size:</span>
                <span class="value">{{ info.content.width }}×{{ info.content.height }}px</span>
              </div>
              <div class="info-item">
                <span class="label">Center:</span>
                <span class="value">({{ info.content.centerX }}, {{ info.content.centerY }})</span>
              </div>
            </div>
          </div>

          <!-- Viewport Bounds -->
          <div class="info-group">
            <h5><i class="bi bi-bounding-box"></i> Viewport Bounds (Canvas Coords)</h5>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">Visible Area:</span>
                <span class="value">
                  ({{ info.viewportBounds.left | number:'1.1-1' }}, {{ info.viewportBounds.top | number:'1.1-1' }}) →
                  ({{ info.viewportBounds.right | number:'1.1-1' }}, {{ info.viewportBounds.bottom | number:'1.1-1' }})
                </span>
              </div>
            </div>
          </div>

          <!-- Formula Breakdown -->
          <div class="info-group">
            <h5><i class="bi bi-calculator"></i> Centering Formula</h5>
            <div class="formula-breakdown">
              <div class="formula-step">
                <span class="formula-label">Container Center:</span>
                <span class="formula-value">({{ info.container.centerX }}, {{ info.container.centerY }})</span>
              </div>
              <div class="formula-step">
                <span class="formula-label">Content Center:</span>
                <span class="formula-value">({{ info.content.centerX }}, {{ info.content.centerY }})</span>
              </div>
              <div class="formula-step">
                <span class="formula-label">Offset X:</span>
                <span class="formula-value">{{ info.formula.offsetX | number:'1.2-2' }}</span>
              </div>
              <div class="formula-step">
                <span class="formula-label">Offset Y:</span>
                <span class="formula-value">{{ info.formula.offsetY | number:'1.2-2' }}</span>
              </div>
              <div class="formula-step">
                <span class="formula-label">Top Padding:</span>
                <span class="formula-value">{{ info.formula.topPadding }}px</span>
              </div>
              <div class="formula-step">
                <span class="formula-label">Final Y:</span>
                <span class="formula-value">{{ info.formula.finalOffsetY | number:'1.2-2' }}</span>
              </div>
            </div>
          </div>

          <!-- Node Information -->
          <div class="info-group">
            <h5><i class="bi bi-collection"></i> Nodes</h5>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">Total:</span>
                <span class="value">{{ info.nodes.total }}</span>
              </div>
              <div class="info-item">
                <span class="label">Visible:</span>
                <span class="value">{{ info.nodes.visible }}</span>
              </div>
            </div>
          </div>

          <!-- Testing Controls -->
          <div class="info-group">
            <h5><i class="bi bi-tools"></i> Testing Controls</h5>
            <div class="control-buttons">
              <button class="test-btn" (click)="centerNodes()" title="Center Nodes (Default)">
                <i class="bi bi-bullseye"></i>
                Center
              </button>
              <button class="test-btn" (click)="moveToBottom()" title="Move to Bottom">
                <i class="bi bi-arrow-down-square"></i>
                Bottom
              </button>
              <button class="test-btn" (click)="resetViewport()" title="Reset Viewport">
                <i class="bi bi-arrow-clockwise"></i>
                Reset
              </button>
              <button class="test-btn" (click)="fitToView()" title="Fit to View">
                <i class="bi bi-aspect-ratio"></i>
                Fit
              </button>
              <button class="test-btn optimal-btn" (click)="setOptimalDefault()" title="Set Reference Position">
                <i class="bi bi-star"></i>
                Reference
              </button>
              <button class="test-btn" (click)="alignMultiplePages()" title="Align Multiple Pages">
                <i class="bi bi-grid-3x3"></i>
                Multi-Page
              </button>
            </div>
          </div>
        </div>

        <div class="refresh-info">
          <small><i class="bi bi-arrow-clockwise"></i> Auto-refreshing every 500ms</small>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./viewport-debug.component.scss']
})
export class ViewportDebugComponent implements OnInit {
  private readonly viewportService = inject(UIDesignViewportService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('ViewportDebugComponent');

  isCollapsed = false;
  debugInfo$!: Observable<ViewportDebugInfo>;

  ngOnInit(): void {
    // Auto-refresh debug info every 500ms
    this.debugInfo$ = interval(500).pipe(
      startWith(0),
      map(() => this.viewportService.getViewportDebugInfo()),
      takeUntilDestroyed(this.destroyRef)
    );

    this.logger.info('Viewport Debug Component initialized');
  }

  toggleCollapse(): void {
    this.isCollapsed = !this.isCollapsed;
  }

  centerNodes(): void {
    this.viewportService.centerViewOnNodes();
    this.logger.info('🎯 Centered nodes via debug panel');
  }

  moveToBottom(): void {
    this.viewportService.moveViewportToBottom();
    this.logger.info('🔽 Moved viewport to bottom via debug panel');
  }

  resetViewport(): void {
    this.viewportService.resetViewport();
    this.logger.info('🔄 Reset viewport via debug panel');
  }

  fitToView(): void {
    this.viewportService.fitContentToView();
    this.logger.info('📐 Fit content to view via debug panel');
  }

  setOptimalDefault(): void {
    // Set the reference position: X=39.86px, Y=82.95px, Zoom=50%
    this.viewportService.centerViewOnNodes();
    this.logger.info('⭐ Set reference position via debug panel:', {
      position: { x: 39.86, y: 82.95, zoom: 0.5 },
      description: 'Applied reference viewport coordinates for perfect multi-page alignment'
    });
  }

  alignMultiplePages(): void {
    // Apply perfect multi-page alignment
    this.viewportService.alignMultiplePages();
    this.logger.info('🔲 Applied multi-page alignment via debug panel:', {
      description: 'Auto-arranged multiple pages with perfect grid alignment'
    });
  }
}
