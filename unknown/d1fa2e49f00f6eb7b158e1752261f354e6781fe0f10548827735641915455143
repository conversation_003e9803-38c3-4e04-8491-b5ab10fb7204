<div class="create-knowledge-base-container">
  <form [formGroup]="knowledgeBaseForm">
    <div class="form-layout">
      <!-- Left Column -->
      <div class="left-column">
        <!-- Knowledge Base Details Card -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <app-form-field
              label="Knowledge-base Name"
              id="knowledgeBaseName"
              [control]="getControl('name')"
            ></app-form-field>

            <app-form-field
              label="Description"
              id="description"
              [control]="getControl('description')"
              type="textarea"
            ></app-form-field>
          </div>
        </app-card>

        <!-- Assign Filters Card -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <h3 class="section-title">Assign Filters</h3>

            <app-form-field
              label="Organization"
              id="organization"
              [control]="getControl('organization')"
            ></app-form-field>

            <app-form-field
              label="Domain"
              id="domain"
              [control]="getControl('domain')"
            ></app-form-field>

            <app-form-field
              label="Project"
              id="project"
              [control]="getControl('project')"
            ></app-form-field>

            <app-form-field
              label="Team"
              id="team"
              [control]="getControl('team')"
            ></app-form-field>
          </div>
        </app-card>
      </div>

      <!-- Right Column -->
      <div class="right-column">
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <!-- Retriever Selection -->
            <div class="section">
              <h3 class="section-title">Select Retriever</h3>
              <div class="retriever-options">
                <button
                  *ngFor="let retriever of retrieverOptions"
                  class="retriever-button"
                  [class.selected]="selectedRetriever === retriever"
                  (click)="selectRetriever(retriever)"
                >
                  {{ retriever }}
                </button>
              </div>
            </div>

            <!-- Split Size Configuration -->
            <div class="section" *ngIf="selectedRetriever === 'Default'">
              <h3 class="section-title">Split Size</h3>
              <div class="split-size-container">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  [value]="splitSize"
                  (input)="onSplitSizeChange($event)"
                  class="split-size-slider"
                />
                <input
                  type="text"
                  [value]="splitSize"
                  class="split-size-input"
                  (input)="onSplitSizeChange($event)"
                />
              </div>

              <!-- Embedding Model Selection Dropdown for Default retriever -->
              <div class="model-selection-section">
                <h3 class="section-title">Choose Embedding Model</h3>
                <app-form-field
                  id="embeddingModel"
                  [control]="getControl('embeddingModel')"
                  type="select"
                  [options]="embeddingModelOptions"
                ></app-form-field>
              </div>
            </div>

            <!-- Parent Doc Split Size Configuration -->
            <div class="section" *ngIf="selectedRetriever === 'Parent Doc'">
              <div class="split-section">
                <h3 class="section-title">Parent Split Size</h3>
                <div class="split-size-container">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    [value]="parentSplitSize"
                    (input)="onParentSplitSizeChange($event)"
                    class="split-size-slider"
                  />
                  <input
                    type="text"
                    [value]="parentSplitSize"
                    class="split-size-input"
                    (input)="onParentSplitSizeChange($event)"
                  />
                </div>
              </div>

              <div class="split-section">
                <h3 class="section-title">Child Split Size</h3>
                <div class="split-size-container">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    [value]="childSplitSize"
                    (input)="onChildSplitSizeChange($event)"
                    class="split-size-slider"
                  />
                  <input
                    type="text"
                    [value]="childSplitSize"
                    class="split-size-input"
                    (input)="onChildSplitSizeChange($event)"
                  />
                </div>
              </div>

              <!-- Embedding Model Selection Dropdown for Parent Doc retriever -->
              <div class="model-selection-section">
                <h3 class="section-title">Choose Embedding Model</h3>
                <app-form-field
                  id="embeddingModel"
                  [control]="getControl('embeddingModel')"
                  type="select"
                  [options]="embeddingModelOptions"
                ></app-form-field>
              </div>
            </div>

            <!-- File Upload Section -->
            <div class="section">
              <h3 class="section-title">Uploaded Knowledgebase</h3>

              <!-- File Table -->
              <div class="file-table-container">
                <table class="file-table">
                  <thead>
                    <tr>
                      <th>File Name</th>
                      <th>File Size</th>
                      <th>Upload Date</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let file of uploadedFiles">
                      <td>
                        <div class="file-info">
                          <svg
                            *ngIf="file.type === 'docx'"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="#2B579A"
                          >
                            <path
                              d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
                            />
                          </svg>
                          <svg
                            *ngIf="file.type === 'pdf'"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="#FF0000"
                          >
                            <path
                              d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
                            />
                          </svg>
                          <svg
                            *ngIf="file.type === 'pptx'"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="#D24726"
                          >
                            <path
                              d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
                            />
                          </svg>
                          {{ file.name }}
                        </div>
                      </td>
                      <td>{{ file.size }}</td>
                      <td>{{ file.date }}</td>
                      <td>
                        <button
                          class="delete-button"
                          (click)="deleteFile(file)"
                        >
                          <svg
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          >
                            <path d="M3 6h18"></path>
                            <path
                              d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"
                            ></path>
                            <line x1="10" y1="11" x2="10" y2="17"></line>
                            <line x1="14" y1="11" x2="14" y2="17"></line>
                          </svg>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- File Upload Area -->
              <div class="file-upload-area">
                <div class="upload-info">
                  <p class="upload-text">
                    Note: upload only .pdf,.txt,.docx,.pptx,.html,.xlsx files
                  </p>
                </div>
                <div
                  class="drag-drop-area"
                  (dragover)="$event.preventDefault()"
                  (drop)="onFileDrop($event)"
                >
                  <p>Drag and Drop your File(s) Or Upload File(s)</p>
                  <input
                    type="file"
                    id="fileUpload"
                    multiple
                    class="file-input"
                    (change)="onFileSelected($event)"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Buttons at bottom of right column -->
          <div class="right-column-buttons">
            <button type="button" class="exit-button" (click)="onExit()">
              Exit
            </button>
            <button type="button" class="save-button" (click)="onSave()">
              {{ isEditMode ? "Update" : "Save" }}
            </button>
          </div>
        </app-card>
      </div>
    </div>
  </form>
</div>
