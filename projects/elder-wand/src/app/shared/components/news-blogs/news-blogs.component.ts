import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { IconsComponent } from '@awe/play-comp-library';

interface BlogPost {
  id: number;
  title: string;
  description: string;
  image: string;
  author: {
    name: string;
    avatar: string;
  };
  views: number;
}

@Component({
  selector: 'app-news-blogs',
  templateUrl: './news-blogs.component.html',
  styleUrls: ['./news-blogs.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule, IconsComponent],
})
export class NewsBlogsComponent {
  blogPosts: BlogPost[] = [
    {
      id: 1,
      title: 'Gen AI 101 Learning Path',
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien.',
      image: '/genAI.svg',
      author: {
        name: '<PERSON><PERSON>',
        avatar: '/ellipse-avatar.svg',
      },
      views: 440,
    },
    {
      id: 2,
      title: '<PERSON>cend<PERSON> uses AI to Elevate work',
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien.',
      image: '/ascendion-logo.svg',
      author: {
        name: 'Jhone Doe',
        avatar: '/ellipse-avatar.svg',
      },
      views: 440,
    },
    {
      id: 3,
      title: 'Gen AI 101 Learning Path',
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien.',
      image: '/genai-101.svg',
      author: {
        name: 'Jhone Doe',
        avatar: '/ellipse-avatar.svg',
      },
      views: 440,
    },
    {
      id: 4,
      title: 'Gen AI 101 Learning Path',
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien.',
      image: '/genai-102.svg',
      author: {
        name: 'Jhone Doe',
        avatar: '/ellipse-avatar.svg',
      },
      views: 440,
    },
    {
      id: 5,
      title: 'Gen AI 101 Learning Path',
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien.',
      image: '/genai-103.svg',
      author: {
        name: 'Jhone Doe',
        avatar: '/ellipse-avatar.svg',
      },
      views: 440,
    },
    {
      id: 6,
      title: 'Gen AI 101 Learning Path',
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien.',
      image: '/genai-104.svg',
      author: {
        name: 'Jhone Doe',
        avatar: '/ellipse-avatar.svg',
      },
      views: 440,
    },
  ];

  readMore(id: number): void {
    // Implement your logic here for what happens when "Read More" is clicked.
    // For example, navigate to a detailed blog post page:
    // this.router.navigate(['/blog', id]);
    console.log('Read More clicked for blog post ID:', id);
    // alert(`Navigating to blog post ${id}`); // For demonstration
  }
}