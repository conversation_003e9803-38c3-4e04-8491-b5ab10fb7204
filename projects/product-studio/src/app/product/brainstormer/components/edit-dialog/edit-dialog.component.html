<!-- Dialog Backdrop -->
<div
  *ngIf="isVisible"
  class="dialog-backdrop d-flex justify-content-center align-items-center position-fixed w-100 h-100"
  (click)="onBackdropClick($event)">

  <!-- Dialog Container -->
  <div
    class="dialog-container bg-white rounded-3 shadow-lg position-relative"
    [style.width]="config.width || 'auto'"
    [style.height]="config.height || 'auto'"
    (click)="$event.stopPropagation()">

    <!-- Dialog Header -->
    <div *ngIf="config.showHeader" class="dialog-header d-flex justify-content-between align-items-center p-3 border-bottom">
      <div class="d-flex align-items-center">
        <!-- Card Icon -->
        <div class="card-icon d-flex align-items-center justify-content-center me-2">
          <i class="fas fa-credit-card"></i>
          <span class="ms-1 fw-bold text-primary">card</span>
        </div>

        <!-- Dialog Title -->
        <h5 class="dialog-title mb-0 ms-2">{{ config.title || 'Edit Feature List' }}</h5>
      </div>

      <!-- Close Button -->
      <button
        *ngIf="config.showCloseButton"
        type="button"
        class="btn-close border-0 bg-transparent p-2"
        (click)="onClose()"
        aria-label="Close">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Dialog Body -->
    <div class="dialog-body p-3">
      <!-- Content Projection -->
      <ng-content></ng-content>
    </div>

    <!-- Dialog Footer -->
    <div *ngIf="config.showFooter" class="dialog-footer d-flex justify-content-center p-3">
      <!-- Three Dots Dropdown -->
      <div class="dropdown-container position-relative">
        <button
          type="button"
          class="btn btn-light rounded-pill px-4 py-2 d-flex align-items-center"
          (click)="toggleDropdown($event)">
          <i class="fas fa-ellipsis-h"></i>
        </button>

        <!-- Dropdown Menu -->
        <div
          class="dropdown-menu position-absolute bg-white rounded-3 shadow-lg border-0 mt-2"
          [class.show]="isDropdownOpen">
          <button
            *ngFor="let item of dropdownItems"
            type="button"
            class="dropdown-item btn btn-link text-start w-100 px-3 py-2 border-0"
            (click)="onDropdownItemClick(item.action, $event)">
            <i *ngIf="item.icon" [class]="item.icon" class="me-2"></i>
            {{ item.label }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
