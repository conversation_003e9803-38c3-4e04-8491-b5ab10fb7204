import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef, DestroyRef, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NewPollingResponseProcessorService } from '../../services/new-polling-response-processor.service';
import { PollingTestService } from '../../services/polling-test.service';
import {
  ProgressState,
  StatusType,
  FileData
} from '../../models/polling-response.interface';
import { createLogger } from '../../utils/logger';

/**
 * Demo component to showcase the new polling response processing
 * This component demonstrates how UI components should integrate with the new system
 */
@Component({
  selector: 'app-polling-demo',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './polling-demo.component.html',
  styleUrls: ['./polling-demo.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PollingDemoComponent implements OnInit {
  private destroyRef = inject(DestroyRef);
  private logger = createLogger('PollingDemo');

  // Observable data from the processor service
  currentProgress: ProgressState | null = null;
  currentStatus: StatusType | null = null;
  progressDescription: string = '';
  logContent: string = '';
  artifactData: any = null;
  fileList: string[] = [];
  codeFiles: FileData[] = [];
  previewUrl: string = '';

  constructor(
    private responseProcessor: NewPollingResponseProcessorService,
    private testService: PollingTestService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.subscribeToProcessorUpdates();
  }



  /**
   * Subscribe to all processor observables to display current state
   */
  private subscribeToProcessorUpdates(): void {
    // Subscribe to progress changes
    this.responseProcessor.currentProgress$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(progress => {
        this.currentProgress = progress;
        this.logger.info('Progress updated:', progress);
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      });

    // Subscribe to status changes
    this.responseProcessor.currentStatus$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(status => {
        this.currentStatus = status;
        this.logger.info('Status updated:', status);
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      });

    // Subscribe to progress description changes
    this.responseProcessor.progressDescription$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(description => {
        this.progressDescription = description;
        this.logger.info('Description updated:', description);
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      });

    // Subscribe to log content changes
    this.responseProcessor.logContent$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(content => {
        this.logContent = content;
        this.logger.info('Log content updated:', content);
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      });

    // Subscribe to artifact data changes
    this.responseProcessor.artifactData$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(data => {
        this.artifactData = data;
        this.logger.info('Artifact data updated:', data);
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      });

    // Subscribe to file list changes
    this.responseProcessor.fileList$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(files => {
        this.fileList = files;
        this.logger.info('File list updated:', files);
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      });

    // Subscribe to code files changes
    this.responseProcessor.codeFiles$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(files => {
        this.codeFiles = files;
        this.logger.info('Code files updated:', files.length, 'files');
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      });

    // Subscribe to preview URL changes
    this.responseProcessor.previewUrl$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(url => {
        this.previewUrl = url;
        this.logger.info('Preview URL updated:', url);
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      });
  }

  /**
   * Start the complete flow demo
   */
  startCompleteFlow(): void {
    this.logger.info('Starting complete flow demo...');
    this.testService.testCompleteFlow()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (message: string) => this.logger.info('Demo message:', message),
        error: (error: any) => this.logger.error('Demo error:', error),
        complete: () => this.logger.info('Complete flow demo finished')
      });
  }

  /**
   * Start the failure scenario demo
   */
  startFailureDemo(): void {
    this.logger.info('Starting failure demo...');
    this.testService.testFailureScenarios()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (message: string) => this.logger.info('Demo message:', message),
        error: (error: any) => this.logger.error('Demo error:', error),
        complete: () => this.logger.info('Failure demo finished')
      });
  }

  /**
   * Start individual steps demo using UI scenarios
   */
  startIndividualSteps(): void {
    this.logger.info('Starting individual steps demo...');
    // Use testUIScenarios which exists in the service
    this.testService.testUIScenarios();
    this.logger.info('Individual steps demo started');
  }

  /**
   * Reset the demo state
   */
  resetDemo(): void {
    this.logger.info('Resetting demo...');
    // Reset the service state
    this.testService.resetState();

    // Reset all local state
    this.currentProgress = null;
    this.currentStatus = null;
    this.progressDescription = '';
    this.logContent = '';
    this.artifactData = null;
    this.fileList = [];
    this.codeFiles = [];
    this.previewUrl = '';

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  /**
   * Test layout analysis failure scenario
   */
  testLayoutFailure(): void {
    this.logger.info('Starting layout failure demo...');
    this.testService.testLayoutAnalysisFailure()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (message: string) => this.logger.info('Layout failure message:', message),
        error: (error: any) => this.logger.error('Layout failure error:', error),
        complete: () => this.logger.info('Layout failure demo finished')
      });
  }

  /**
   * Test design system failure scenario
   */
  testDesignSystemFailure(): void {
    this.logger.info('Starting design system failure demo...');
    this.testService.testDesignSystemFailure()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (message: string) => this.logger.info('Design system failure message:', message),
        error: (error: any) => this.logger.error('Design system failure error:', error),
        complete: () => this.logger.info('Design system failure demo finished')
      });
  }

  /**
   * Test deployment failure scenario
   */
  testDeploymentFailure(): void {
    this.logger.info('Starting deployment failure demo...');
    this.testService.testDeploymentFailure()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (message: string) => this.logger.info('Deployment failure message:', message),
        error: (error: any) => this.logger.error('Deployment failure error:', error),
        complete: () => this.logger.info('Deployment failure demo finished')
      });
  }

  /**
   * Get current state for debugging
   */
  getCurrentState(): any {
    return this.testService.getCurrentState();
  }

  /**
   * Get CSS class for status display
   */
  getStatusClass(status: StatusType | null): string {
    if (!status) return '';
    return status;
  }
}
