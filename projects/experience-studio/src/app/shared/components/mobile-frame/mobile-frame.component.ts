import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, inject, OnChanges, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SafeSrcdocDirective } from '../../directives/safe-srcdoc.directive';
import { createLogger } from '../../utils/logger';
import { ScreenshotService } from '../../services/screenshot.service';

export interface MobilePage {
  fileName: string;
  content: string;
}

@Component({
  selector: 'app-mobile-frame',
  standalone: true,
  imports: [CommonModule, SafeSrcdocDirective],
  templateUrl: './mobile-frame.component.html',
  styleUrls: ['./mobile-frame.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MobileFrameComponent implements OnChanges {
  private readonly logger = createLogger('MobileFrameComponent');
  private readonly screenshotService = inject(ScreenshotService);

  @ViewChild('mobileIframe') mobileIframe!: ElementRef<HTMLIFrameElement>;

  @Input() pages: MobilePage[] = [];
  @Input() currentPageIndex: number = 0;
  @Input() theme: 'light' | 'dark' = 'light';

  @Output() pageChange = new EventEmitter<number>();
  @Output() fullscreenRequest = new EventEmitter<MobilePage>();

  ngOnChanges(changes: SimpleChanges): void {
    // Handle changes to inputs if needed
    if (changes['currentPageIndex'] || changes['pages']) {
      this.logger.debug('Mobile frame inputs changed', {
        currentPageIndex: this.currentPageIndex,
        pagesCount: this.pages.length
      });
    }
  }

  get currentPage(): MobilePage | null {
    return this.pages[this.currentPageIndex] || null;
  }

  get currentPageContent(): string {
    const content = this.currentPage?.content || '';
    if (!content) return '';

    // Decode HTML entities first
    const decodedContent = this.decodeHtmlEntities(content);

    // Ensure the HTML content has proper CSS styling
    return this.ensureProperHtmlStyling(decodedContent);
  }

  private decodeHtmlEntities(content: string): string {
    if (!content) return '';

    // Create a temporary DOM element to decode HTML entities
    const textarea = document.createElement('textarea');
    textarea.innerHTML = content;
    let decodedContent = textarea.value;

    // Additional manual decoding for common entities that might not be handled
    decodedContent = decodedContent
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, '/')
      .replace(/&#10;/g, '\n')
      .replace(/&nbsp;/g, ' ');

    return decodedContent;
  }

  private ensureProperHtmlStyling(content: string): string {
    // Check if content already has a complete HTML structure
    if (content.includes('<!DOCTYPE html>') || content.includes('<html>')) {
      return content;
    }

    // Check if content has basic CSS reset
    const hasBasicStyling = content.includes('margin:') || content.includes('padding:') || content.includes('<style>');

    if (hasBasicStyling) {
      return content;
    }

    // Super responsive CSS for mobile rendering with hidden scrollbars but scroll enabled
    const basicCSS = `
      <style>
        * {
          box-sizing: border-box;
        }
        html {
          /* Hide scrollbar for Chrome, Safari and Opera */
          overflow: auto;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* Internet Explorer 10+ */
        }
        html::-webkit-scrollbar {
          display: none; /* Chrome, Safari and Opera */
        }
        body {
          margin: 0;
          padding: 16px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333;
          background: white;
          font-size: clamp(12px, 3.5vw, 16px);
          /* Enable scrolling but hide scrollbars */
          overflow: auto;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* Internet Explorer 10+ */
        }
        body::-webkit-scrollbar {
          display: none; /* Chrome, Safari and Opera */
        }
        /* Super responsive typography */
        h1 { font-size: clamp(20px, 6vw, 32px); margin: 0.5em 0; }
        h2 { font-size: clamp(18px, 5vw, 28px); margin: 0.5em 0; }
        h3 { font-size: clamp(16px, 4.5vw, 24px); margin: 0.5em 0; }
        h4 { font-size: clamp(14px, 4vw, 20px); margin: 0.5em 0; }
        h5 { font-size: clamp(13px, 3.5vw, 18px); margin: 0.5em 0; }
        h6 { font-size: clamp(12px, 3vw, 16px); margin: 0.5em 0; }
        p { font-size: clamp(12px, 3.5vw, 16px); margin: 0.5em 0; }
        /* Responsive images and media */
        img, video, iframe, embed, object {
          max-width: 100%;
          height: auto;
          display: block;
          margin: 0 auto;
        }
        /* Responsive tables */
        table {
          width: 100%;
          border-collapse: collapse;
          font-size: clamp(10px, 3vw, 14px);
        }
        th, td {
          padding: 0.5em;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }
        /* Responsive containers */
        .container, .wrapper, .content {
          max-width: 100%;
          margin: 0 auto;
          padding: 0 1em;
        }
        /* Responsive buttons */
        button, .btn, input[type="submit"], input[type="button"] {
          padding: 0.75em 1.5em;
          font-size: clamp(12px, 3.5vw, 16px);
          border: 1px solid #ddd;
          border-radius: 6px;
          background: white;
          cursor: pointer;
          display: inline-block;
          text-align: center;
          min-height: 44px; /* Touch-friendly */
        }
        button:hover, .btn:hover {
          background: #f5f5f5;
        }
        /* Responsive forms */
        input, textarea, select {
          width: 100%;
          padding: 0.75em;
          font-size: clamp(12px, 3.5vw, 16px);
          border: 1px solid #ddd;
          border-radius: 4px;
          margin-bottom: 1em;
        }
        /* Mobile-first responsive breakpoints */
        @media (max-width: 480px) {
          body { padding: 12px; font-size: 14px; }
        }
      </style>
    `;

    // If content is just a fragment, wrap it in a basic HTML structure
    if (!content.includes('<body>')) {
      return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  ${basicCSS}
</head>
<body>
  ${content}
</body>
</html>`;
    }

    // If content has body but no head styling, inject CSS into head or body
    if (content.includes('<head>')) {
      return content.replace('<head>', `<head>${basicCSS}`);
    } else if (content.includes('<body>')) {
      return content.replace('<body>', `<body>${basicCSS}`);
    }

    return content;
  }

  onPageSelect(index: number): void {
    if (index >= 0 && index < this.pages.length && index !== this.currentPageIndex) {
      this.pageChange.emit(index);
    }
  }

  onPreviousPage(): void {
    if (this.currentPageIndex > 0) {
      this.pageChange.emit(this.currentPageIndex - 1);
    }
  }

  onNextPage(): void {
    if (this.currentPageIndex < this.pages.length - 1) {
      this.pageChange.emit(this.currentPageIndex + 1);
    }
  }

  onFullscreenClick(): void {
    if (this.currentPage) {
      this.fullscreenRequest.emit(this.currentPage);
    }
  }

  onDownloadClick(): void {
    this.logger.info('📸 Mobile frame download button clicked', {
      hasCurrentPage: !!this.currentPage,
      hasMobileIframe: !!this.mobileIframe,
      currentPageFileName: this.currentPage?.fileName
    });

    if (!this.currentPage) {
      this.logger.warn('📸 Cannot download screenshot - no current page');
      return;
    }

    if (!this.mobileIframe) {
      this.logger.warn('📸 Cannot download screenshot - no iframe reference');
      return;
    }

    const iframe = this.mobileIframe.nativeElement;

    this.logger.info('📸 Starting mobile frame screenshot capture', {
      iframeExists: !!iframe,
      iframeSrc: iframe.src,
      iframeLoaded: iframe.contentDocument !== null,
      iframeWidth: iframe.offsetWidth,
      iframeHeight: iframe.offsetHeight
    });

    const filename = this.screenshotService.generateFilename(this.currentPage.fileName);
    const options = this.screenshotService.getMobileFrameOptions();

    this.screenshotService.captureAndDownloadIframe(iframe, filename, options).subscribe({
      next: () => {
        this.logger.info('📸 Mobile frame screenshot downloaded successfully');
      },
      error: (error) => {
        this.logger.error('📸 Mobile frame screenshot failed', error);
      }
    });
  }

  getPageDisplayName(page: MobilePage): string {
    // Remove .html extension and format the name
    return page.fileName.replace('.html', '').replace(/_/g, ' ');
  }
}

