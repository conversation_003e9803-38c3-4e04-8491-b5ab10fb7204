<div class="user-persona">
  <!-- Persona Cards Grid -->
  <div class="user-persona__grid">
    <div class="user-persona__card" *ngFor="let persona of personas">
      <button
        type="button"
        class="user-persona__close-btn"
        (click)="removePersona(persona)"
      >
        
      </button>

      <!-- Card Header with Role and Avatar -->
      <div class="user-persona__header">
        <h2 class="user-persona__role">{{ persona.role }}</h2>
        <div class="user-persona__avatar">
          <img [src]="persona.avatar" [alt]="persona.role + ' avatar'" />
        </div>
      </div>

      <!-- Personal Information -->
      <div class="user-persona__info">
        <div class="user-persona__info-row">
          <span class="user-persona__label">Age</span>
          <span class="user-persona__value">{{ persona.age }}</span>
        </div>
        <div class="user-persona__info-row">
          <span class="user-persona__label">Education</span>
          <span class="user-persona__value">{{ persona.education }}</span>
        </div>
        <div class="user-persona__info-row">
          <span class="user-persona__label">Status</span>
          <span class="user-persona__value">{{ persona.status }}</span>
        </div>
        <div class="user-persona__info-row">
          <span class="user-persona__label">Location</span>
          <span class="user-persona__value">{{ persona.location }}</span>
        </div>
        <div class="user-persona__info-row">
          <span class="user-persona__label">Tech Literacy</span>
          <span class="user-persona__value">{{ persona.techLiteracy }}</span>
        </div>
      </div>

      <!-- Quote Section -->
      <div class="user-persona__quote-section">
        <blockquote class="user-persona__quote">
          "{{ persona.quote }}"
        </blockquote>
      </div>

      <!-- Personality Section -->
      <div class="user-persona__personality">
        <span class="user-persona__section-label">Personality</span>
        <div class="user-persona__tags">
          <span
            class="user-persona__tag"
            *ngFor="let trait of persona.personality"
            >{{ trait }}</span
          >
        </div>
      </div>
    </div>
  </div>
</div>
