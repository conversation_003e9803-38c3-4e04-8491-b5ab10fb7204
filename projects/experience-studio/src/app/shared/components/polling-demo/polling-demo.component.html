<div class="polling-demo-container">
  <div class="demo-header">
    <h2>New Polling System Demo</h2>
    <p>This demo shows how the new polling response format integrates with the code-window component.</p>
  </div>

  <div class="demo-controls">
    <div class="control-group">
      <h4>Main Demos</h4>
      <button class="demo-btn primary" (click)="startCompleteFlow()">
        Start Complete Flow Demo
      </button>
      <button class="demo-btn secondary" (click)="startFailureDemo()">
        Start Basic Failure Demo
      </button>
      <button class="demo-btn tertiary" (click)="startIndividualSteps()">
        Start Individual Steps Demo
      </button>
    </div>

    <div class="control-group">
      <h4>Specific Failure Tests</h4>
      <button class="demo-btn warning" (click)="testLayoutFailure()">
        Test Layout Failure
      </button>
      <button class="demo-btn warning" (click)="testDesignSystemFailure()">
        Test Design System Failure
      </button>
      <button class="demo-btn warning" (click)="testDeploymentFailure()">
        Test Deployment Failure
      </button>
    </div>

    <div class="control-group">
      <h4>Controls</h4>
      <button class="demo-btn info" (click)="getCurrentState()">
        Get Current State
      </button>
      <button class="demo-btn danger" (click)="resetDemo()">
        Reset Demo
      </button>
    </div>
  </div>

  <div class="demo-status">
    <div class="status-section">
      <h3>Current Status</h3>
      <div class="status-grid">
        <div class="status-item">
          <label>Progress:</label>
          <span class="status-value">{{ currentProgress || 'Not Started' }}</span>
        </div>
        <div class="status-item">
          <label>Status:</label>
          <span class="status-value" [class]="getStatusClass(currentStatus)">
            {{ currentStatus || 'IDLE' }}
          </span>
        </div>
        <div class="status-item">
          <label>Description:</label>
          <span class="status-value">{{ progressDescription || 'Waiting to start...' }}</span>
        </div>
      </div>
    </div>

    <div class="logs-section">
      <h3>Log Content</h3>
      <div class="log-content">
        {{ logContent || 'No logs yet...' }}
      </div>
    </div>
  </div>

  <div class="demo-data-preview">
    <h3>Current Response Data</h3>
    <div class="data-sections">
      <div class="data-section" *ngIf="artifactData">
        <h4>Artifact Data</h4>
        <pre class="data-content">{{ artifactData | json }}</pre>
      </div>

      <div class="data-section" *ngIf="codeFiles && codeFiles.length > 0">
        <h4>Code Files ({{ codeFiles.length }})</h4>
        <div class="file-list">
          <div class="file-item" *ngFor="let file of codeFiles">
            <span class="file-path">{{ file.path }}</span>
            <span class="file-size">({{ file.code.length }} chars)</span>
          </div>
        </div>
      </div>

      <div class="data-section" *ngIf="previewUrl">
        <h4>Preview URL</h4>
        <a [href]="previewUrl" target="_blank" class="preview-link">{{ previewUrl }}</a>
      </div>

      <div class="data-section" *ngIf="fileList && fileList.length > 0">
        <h4>File List</h4>
        <ul class="simple-file-list">
          <li *ngFor="let file of fileList">{{ file }}</li>
        </ul>
      </div>
    </div>
  </div>

  <div class="demo-explanation">
    <h3>How It Works</h3>
    <div class="explanation-content">
      <p>This demo simulates the new polling API response format and shows how the code-window component reacts to different states:</p>

      <div class="flow-steps">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>Project Overview</h4>
            <p>Initial project analysis and overview generation</p>
          </div>
        </div>

        <div class="step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>Layout Analyzed</h4>
            <p>UI layout analysis and structure identification</p>
          </div>
        </div>

        <div class="step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>Design System Analyzed</h4>
            <p>Design tokens extraction and system analysis</p>
          </div>
        </div>

        <div class="step">
          <div class="step-number">4</div>
          <div class="step-content">
            <h4>Deployed</h4>
            <p>Code generation and deployment to preview environment</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
