<div class="loading-container" [ngClass]="theme + '-theme'">
  <div class="loader">
    <div class="box">
      <div class="logo">
        <div class="ui-abstergo">
          <div class="abstergo-loader">
            <div></div>
            <div></div>
            <div></div>
          </div>
        </div>
      </div>
    </div>
    <div class="box"></div>
    <div class="box"></div>
    <div class="box"></div>
    <div class="box"></div>
  </div>
  <div class="message-container">
    <p class="message">{{ currentMessage$ | async }}</p>
  </div>
</div>
