.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 2rem;
  background-color: var(--preview-page-bg-color, #f8f9fa);
  color: var(--body-text-color, #333);
  font-family: var(--font-family, 'Arial, sans-serif');
  overflow: hidden; /* Prevent animations from causing scrollbars */

  &.dark {
    background-color: var(--preview-page-bg-color, #1e1e1e);
    color: var(--body-text-color, #f8f9fa);
  }
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 600px;
  text-align: center;
  width: 100%;
}

.question-mark {
  position: absolute;
  color: #8a70ff;
  font-size: 24px;
  font-weight: bold;

  &.left {
    top: -20px;
    left: 10px;
  }

  &.right {
    top: -20px;
    right: 10px;
  }
}

/* ERROR Text Styles */
.error-text {
  margin: 2rem 0;
}

/* --- ERROR Word Section --- */
.error-word-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px; /* Space below ERROR */
}

/* --- Individual ERROR Character Container --- */
.error-char-container {
  position: relative; /* Allows absolute positioning of interactive content */
  display: inline-flex; /* Use inline-flex to center content and flow like text */
  justify-content: center;
  align-items: center;
  width: 100px; /* Container size for each character */
  height: 140px; /* Increased container height to allow for lifting */
  cursor: pointer;
  margin: 0 18px; /* Space between characters */
  user-select: none; /* Prevent selecting the character/card */
  border-radius: 10px;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px); /* Slight lift of the entire container on hover */
    z-index: 10; /* Ensure hovered character appears above others */
  }
}

.error-char-container .error-char.gray {
  position: absolute; /* Position the gray character behind */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 180px; /* Large gray character size */
  font-weight: bold;
  color: #ccc; /* Gray color */
  pointer-events: none; /* Allow clicks/hovers to pass through to parent */
  z-index: 1; /* Ensure gray character is behind the card initially */
  transition: opacity 0.3s ease, transform 0.3s ease; /* Smooth transition for opacity and transform */

  .dark & {
    color: #4a4a4a; /* Darker gray for dark theme */
  }
}

.error-char-container:hover .error-char.gray {
  opacity: 0; /* Make the gray character disappear on hover */
  transform: translate(-50%, -70%) scale(0.8); /* Move up and scale down as it disappears */

  .dark & {
    opacity: 0;
  }
}

/* --- Interactive Content (Card & Animation) --- */
.char-interactive-area {
  position: absolute;
  bottom: 0; /* Anchor to the bottom of the container */
  left: 50%;
  transform: translateX(-50%) translateY(10px); /* Start lower and centered horizontally */
  opacity: 0;
  pointer-events: none; /* Initially not interactive */
  transition: opacity 0.4s ease-out, transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 2; /* Ensure interactive content is above gray character */
  display: flex;
  flex-direction: column; /* Stack card and animation text vertically */
  align-items: center;
  width: auto; /* Allow width to adjust to content if needed, or set fixed */
  min-width: 100px; /* Ensure it's at least as wide as the card area */
  padding: 0 5px; /* Add some padding if text is wider */

  &.active {
    opacity: 1;
    pointer-events: auto;
    transform: translateX(-50%) translateY(-60px) scale(1.2);
  }
}

.error-char-container:hover .char-interactive-area {
  opacity: 1;
  pointer-events: auto; /* Make interactive on hover */
  transform: translateX(-50%) translateY(-60px) scale(1.2); /* More pronounced lift-up effect */
  animation: float 1s ease-in-out infinite alternate; /* Add floating animation */
}

.char-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  width: 80px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  transform-style: preserve-3d;
  perspective: 1000px;

  /* Add a subtle glow effect */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(138, 112, 255, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .error-char-container:hover & {
    transform: rotateY(10deg) rotateX(5deg);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);

    &::after {
      opacity: 1;
    }
  }

  .dark & {
    background-color: #2a2a2a;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);

    &::after {
      box-shadow: 0 0 15px rgba(169, 144, 255, 0.5);
    }
  }
}

.char-number.black {
  font-size: 80px;
  font-weight: bold;
  color: black;

  .dark & {
    color: white;
  }
}

/* --- Animation Content (Tooltip Text) --- */
.char-interactive-area .animation-content {
  position: relative;
  opacity: 0;
  font-size: 13px;
  font-weight: 500;
  color: #555;
  white-space: nowrap;
  pointer-events: none;
  margin-top: 12px;
  text-align: center; /* Ensure text is centered */
  transition: opacity 0.3s ease, transform 0.3s ease;
  transform: translateY(5px);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .error-char-container:hover & {
    opacity: 1;
    transform: translateY(0);
  }

  .dark & {
    color: #eee;
    background-color: rgba(42, 42, 42, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
}

/* Error Message Styles */
.error-message {
  margin-bottom: 2rem;
  width: 100%;

  h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 600;
  }

  p {
    font-size: 1.1rem;
    color: var(--secondary-text-color, #666);
    line-height: 1.5;
    min-height: 3rem; /* Ensure consistent height during typing animation */

    .dark & {
      color: var(--secondary-text-color, #aaa);
    }
  }

  .animated-text {
    display: inline-block;
  }

  .cursor-blink {
    display: inline-block;
    animation: cursor-blink 1s infinite;
  }

  .progress-state {
    margin-top: 1rem;

    .badge {
      display: inline-block;
      padding: 0.35rem 0.65rem;
      font-size: 0.85rem;
      font-weight: 500;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border-radius: 0.25rem;
      background-color: #dc3545;
      color: white;
      animation: pulse-badge 2s infinite;
    }
  }
}

@keyframes cursor-blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

@keyframes pulse-badge {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0% { transform: translateX(-50%) translateY(-60px) scale(1.2); }
  100% { transform: translateX(-50%) translateY(-65px) scale(1.2); }
}

/* Button Styles */
.action-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.retry-button, .home-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;

  i {
    font-size: 1.1rem;
  }
}

.retry-button {
  background-color: #8a70ff; /* Purple */
  color: white;

  &:hover {
    background-color: #7a60ef;
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

.home-button {
  background-color: transparent;
  border: 1px solid #8a70ff;
  color: #8a70ff;
  position: relative;
  overflow: hidden;

  .dark & {
    color: #a990ff;
    border-color: #a990ff;
  }

  &:hover {
    background-color: rgba(138, 112, 255, 0.1);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }

  /* Disabled state styling */
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: #ccc;
    color: #999;

    &:hover {
      background-color: transparent;
      transform: none;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 5px,
        rgba(0, 0, 0, 0.05) 5px,
        rgba(0, 0, 0, 0.05) 10px
      );
      pointer-events: none;
    }

    .dark & {
      border-color: #555;
      color: #777;

      &::after {
        background: repeating-linear-gradient(
          45deg,
          transparent,
          transparent 5px,
          rgba(255, 255, 255, 0.05) 5px,
          rgba(255, 255, 255, 0.05) 10px
        );
      }
    }
  }

  /* Counter badge for retry attempts */
  .retry-counter {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
}
