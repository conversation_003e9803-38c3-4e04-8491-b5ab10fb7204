import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeadingComponent, IconsComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-trusted-clients',
  templateUrl: './trusted-clients.component.html',
  styleUrls: ['./trusted-clients.component.scss'],
  standalone: true,
  imports: [CommonModule, HeadingComponent, IconsComponent],
})
export class TrustedClientsComponent {
  clients = [
    {
      name: 'CVS Health',
      logo: 'cvs-client-logo.svg',
    },
    {
      name: 'Axos Bank',
      logo: 'axos-client-logo.svg',
    },
    {
      name: 'Loan Depot',
      logo: 'axos-client-logo.svg',
    },
    {
      name: 'HP',
      logo: 'axos-client-logo.svg',
    },
  ];

  description =
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien, non tempus urna diam dictum massa. Morbi sem nisl, egestas facilisis urna sit amet, semper vestibulum nisl.';
}
