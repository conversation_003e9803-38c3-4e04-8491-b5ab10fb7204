<div class="create-agent-container">
  <h1 class="page-title">Choose the type of Agent you want to create</h1>
  
  <div class="agent-types-container">
    <!-- Individual Agent Card -->
    <app-card customClass="agent-type-card" (click)="selectAgentType('individual')">
      <div class="agent-type-content">
        <h2 class="agent-type-title">Individual Agents</h2>
        <p class="agent-type-description">Best used for executing just an individual agent</p>
      </div>
    </app-card>
    
    <!-- Collaborative Agent Card -->
    <app-card customClass="agent-type-card" (click)="selectAgentType('collaborative')">
      <div class="agent-type-content">
        <h2 class="agent-type-title">Collaborative Agents</h2>
        <p class="agent-type-description">Best used for executing many agents in a workflow</p>
      </div>
    </app-card>
  </div>
</div>
