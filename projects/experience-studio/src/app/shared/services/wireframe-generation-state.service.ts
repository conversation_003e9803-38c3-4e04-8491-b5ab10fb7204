import { Injectable, inject, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { map, distinctUntilChanged } from 'rxjs/operators';
import { createLogger } from '../utils/logger';

export interface WireframeGenerationState {
  isGenerating: boolean;
  isComplete: boolean;
  hasError: boolean;
  errorMessage: string | null;
  nodesCount: number;
  canShowSelectionControls: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class WireframeGenerationStateService {
  private readonly logger = createLogger('WireframeGenerationState');
  private readonly destroyRef = inject(DestroyRef);

  // State management with BehaviorSubjects
  private readonly isGeneratingSubject = new BehaviorSubject<boolean>(false);
  private readonly isCompleteSubject = new BehaviorSubject<boolean>(false);
  private readonly hasErrorSubject = new BehaviorSubject<boolean>(false);
  private readonly errorMessageSubject = new BehaviorSubject<string | null>(null);
  private readonly nodesCountSubject = new BehaviorSubject<number>(0);

  // Public observables
  public readonly isGenerating$ = this.isGeneratingSubject.asObservable();
  public readonly isComplete$ = this.isCompleteSubject.asObservable();
  public readonly hasError$ = this.hasErrorSubject.asObservable();
  public readonly errorMessage$ = this.errorMessageSubject.asObservable();
  public readonly nodesCount$ = this.nodesCountSubject.asObservable();

  // Combined state observable
  public readonly wireframeGenerationState$: Observable<WireframeGenerationState> = combineLatest([
    this.isGenerating$,
    this.isComplete$,
    this.hasError$,
    this.errorMessage$,
    this.nodesCount$
  ]).pipe(
    map(([isGenerating, isComplete, hasError, errorMessage, nodesCount]) => ({
      isGenerating,
      isComplete,
      hasError,
      errorMessage,
      nodesCount,
      canShowSelectionControls: isComplete && !hasError && nodesCount > 0
    })),
    distinctUntilChanged((prev, curr) => 
      prev.isGenerating === curr.isGenerating &&
      prev.isComplete === curr.isComplete &&
      prev.hasError === curr.hasError &&
      prev.errorMessage === curr.errorMessage &&
      prev.nodesCount === curr.nodesCount
    ),
    takeUntilDestroyed(this.destroyRef)
  );

  // Computed observable for selection controls visibility
  public readonly canShowSelectionControls$: Observable<boolean> = this.wireframeGenerationState$.pipe(
    map(state => state.canShowSelectionControls),
    distinctUntilChanged(),
    takeUntilDestroyed(this.destroyRef)
  );

  constructor() {
    this.logger.info('🎯 Wireframe Generation State Service initialized');
  }

  /**
   * Start wireframe generation
   */
  startGeneration(): void {
    this.isGeneratingSubject.next(true);
    this.isCompleteSubject.next(false);
    this.hasErrorSubject.next(false);
    this.errorMessageSubject.next(null);
    this.nodesCountSubject.next(0);
    
    this.logger.info('🚀 Wireframe generation started');
  }

  /**
   * Complete wireframe generation successfully
   */
  completeGeneration(nodesCount: number): void {
    this.isGeneratingSubject.next(false);
    this.isCompleteSubject.next(true);
    this.hasErrorSubject.next(false);
    this.errorMessageSubject.next(null);
    this.nodesCountSubject.next(nodesCount);
    
    this.logger.info('✅ Wireframe generation completed successfully:', { nodesCount });
  }

  /**
   * Handle wireframe generation error
   */
  setError(errorMessage: string): void {
    this.isGeneratingSubject.next(false);
    this.isCompleteSubject.next(false);
    this.hasErrorSubject.next(true);
    this.errorMessageSubject.next(errorMessage);
    this.nodesCountSubject.next(0);
    
    this.logger.error('❌ Wireframe generation failed:', errorMessage);
  }

  /**
   * Reset all state
   */
  reset(): void {
    this.isGeneratingSubject.next(false);
    this.isCompleteSubject.next(false);
    this.hasErrorSubject.next(false);
    this.errorMessageSubject.next(null);
    this.nodesCountSubject.next(0);
    
    this.logger.info('🔄 Wireframe generation state reset');
  }

  /**
   * Update nodes count
   */
  updateNodesCount(count: number): void {
    this.nodesCountSubject.next(count);
    this.logger.info('📊 Nodes count updated:', count);
  }

  /**
   * Check if generation is in progress
   */
  isGenerating(): boolean {
    return this.isGeneratingSubject.value;
  }

  /**
   * Check if generation is complete
   */
  isComplete(): boolean {
    return this.isCompleteSubject.value;
  }

  /**
   * Check if there's an error
   */
  hasError(): boolean {
    return this.hasErrorSubject.value;
  }

  /**
   * Get current error message
   */
  getErrorMessage(): string | null {
    return this.errorMessageSubject.value;
  }

  /**
   * Get current nodes count
   */
  getNodesCount(): number {
    return this.nodesCountSubject.value;
  }

  /**
   * Check if selection controls should be visible
   */
  canShowSelectionControls(): boolean {
    const state = this.getCurrentState();
    return state.canShowSelectionControls;
  }

  /**
   * Get current complete state
   */
  private getCurrentState(): WireframeGenerationState {
    return {
      isGenerating: this.isGeneratingSubject.value,
      isComplete: this.isCompleteSubject.value,
      hasError: this.hasErrorSubject.value,
      errorMessage: this.errorMessageSubject.value,
      nodesCount: this.nodesCountSubject.value,
      canShowSelectionControls: this.isCompleteSubject.value && 
                                !this.hasErrorSubject.value && 
                                this.nodesCountSubject.value > 0
    };
  }

  /**
   * Get state for debugging
   */
  getDebugState(): WireframeGenerationState {
    return this.getCurrentState();
  }
}
