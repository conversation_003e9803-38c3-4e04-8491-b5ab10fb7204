<div class="polling-demo-route">
  <!-- Left Panel - Demo Controls -->
  <div class="demo-controls-panel">
    <div class="panel-header">
      <h2>🚀 New Polling System Demo</h2>
      <p>Experience how the code-window responds to different polling states</p>
    </div>

    <!-- Demo Status -->
    <div class="demo-status-card">
      <h3>Demo Status</h3>
      <div class="status-info">
        <div class="status-item">
          <span class="label">Status:</span>
          <span class="value" [class]="isRunning ? 'running' : 'stopped'">
            {{ getDemoStatus() }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">Current Step:</span>
          <span class="value">{{ currentStepIndex + 1 }} / {{ demoSteps.length }}</span>
        </div>
        <div class="status-item">
          <span class="label">Mode:</span>
          <span class="value">{{ autoMode ? 'Automatic' : 'Manual' }}</span>
        </div>
      </div>
    </div>

    <!-- Main Controls -->
    <div class="main-controls">
      <h3>Main Controls</h3>
      <div class="control-buttons">
        <button
          class="demo-btn primary"
          (click)="startCompleteFlow()"
          [disabled]="isRunning">
          ▶️ Start Complete Flow
        </button>
        <button
          class="demo-btn warning"
          (click)="startFailureFlow()"
          [disabled]="isRunning">
          ⚠️ Start with Failures
        </button>
        <button
          class="demo-btn danger"
          (click)="stopDemo()">
          ⏹️ Stop Demo
        </button>
        <button
          class="demo-btn secondary"
          (click)="resetDemo()">
          🔄 Reset Demo
        </button>
      </div>
    </div>

    <!-- Step Controls -->
    <div class="step-controls">
      <h3>Step-by-Step Controls</h3>
      <div class="steps-list">
        <div
          *ngFor="let step of demoSteps; let i = index"
          class="step-item"
          [class]="getStepStatusClass(step)"
          [class.active]="step.isActive">

          <div class="step-header">
            <div class="step-info">
              <span class="step-number">{{ i + 1 }}</span>
              <div class="step-details">
                <h4>{{ step.name }}</h4>
                <p>{{ step.description }}</p>
              </div>
            </div>
            <div class="step-status">
              <span class="status-indicator" [class]="step.status">
                <ng-container [ngSwitch]="step.status">
                  <span *ngSwitchCase="'pending'">⏳</span>
                  <span *ngSwitchCase="'in-progress'">🔄</span>
                  <span *ngSwitchCase="'completed'">✅</span>
                  <span *ngSwitchCase="'failed'">❌</span>
                </ng-container>
              </span>
            </div>
          </div>

          <div class="step-controls-row">
            <div class="timeout-control">
              <label>Timeout (seconds):</label>
              <input
                type="number"
                [value]="step.timeout"
                min="1"
                max="10"
                (input)="updateStepTimeout(i, +($any($event.target).value))"
                [disabled]="isRunning && step.isActive">
            </div>
            <div class="step-buttons">
              <button
                class="demo-btn small success"
                (click)="runStep(i, false)"
                [disabled]="isRunning && autoMode">
                ▶️ Run Success
              </button>
              <button
                class="demo-btn small danger"
                (click)="runStep(i, true)"
                [disabled]="isRunning && autoMode">
                ❌ Run Failure
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Current State Display -->
    <div class="current-state-card">
      <h3>Current Polling State</h3>
      <div class="state-grid">
        <div class="state-item">
          <span class="label">Progress:</span>
          <span class="value code">{{ currentProgress || 'None' }}</span>
        </div>
        <div class="state-item">
          <span class="label">Status:</span>
          <span class="value" [class]="currentStatus?.toLowerCase()">
            {{ currentStatus || 'IDLE' }}
          </span>
        </div>
        <div class="state-item full-width">
          <span class="label">Description:</span>
          <span class="value">{{ progressDescription || 'Waiting to start...' }}</span>
        </div>
        <div class="state-item full-width">
          <span class="label">Log:</span>
          <span class="value log">{{ logContent || 'No logs yet...' }}</span>
        </div>
        <div class="state-item full-width" *ngIf="currentProjectInfo">
          <span class="label">Project:</span>
          <span class="value project-info">
            <strong>{{ currentProjectInfo.name }}</strong>
            <span *ngIf="currentProjectInfo.url" class="project-url"> - {{ currentProjectInfo.url }}</span>
          </span>
        </div>
        <div class="state-item full-width">
          <span class="label">Tab States:</span>
          <span class="value tab-states">
            <span class="tab-state" [class.enabled]="responseProcessor.isArtifactsTabEnabled()">
              📄 Artifacts: {{ responseProcessor.isArtifactsTabEnabled() ? 'Enabled' : 'Disabled' }}
            </span>
            <span class="tab-state" [class.enabled]="responseProcessor.isCodeTabEnabled()">
              💻 Code: {{ responseProcessor.isCodeTabEnabled() ? 'Enabled' : 'Disabled' }}
            </span>
            <span class="tab-state" [class.enabled]="responseProcessor.isPreviewEnabled()">
              👁️ Preview: {{ responseProcessor.isPreviewEnabled() ? 'Enabled' : 'Disabled' }}
            </span>
          </span>
        </div>
      </div>
    </div>

    <!-- Data Preview -->
    <div class="data-preview-card" *ngIf="artifactData || codeFiles.length > 0 || previewUrl">
      <h3>Response Data</h3>
      <div class="data-items">
        <div class="data-item" *ngIf="artifactData">
          <span class="label">Artifacts:</span>
          <span class="value">{{ artifactData ? 'Available' : 'None' }}</span>
        </div>
        <div class="data-item" *ngIf="codeFiles.length > 0">
          <span class="label">Code Files:</span>
          <span class="value">{{ codeFiles.length }} files</span>
        </div>
        <div class="data-item" *ngIf="previewUrl">
          <span class="label">Preview URL:</span>
          <span class="value">Available</span>
        </div>
      </div>
    </div>

    <!-- Stepper Integration -->
    <div class="data-preview-card">
      <h3>🔄 Stepper Integration</h3>
      <div class="data-items">
        <div class="data-item">
          <span class="label">Stepper State:</span>
          <span class="value">{{ getStepperDisplayTitle() }}</span>
        </div>
        <div class="data-item">
          <span class="label">Progress Description:</span>
          <span class="value">{{ progressDescription || 'No description' }}</span>
        </div>
        <div class="data-item">
          <span class="label">Step Status:</span>
          <span class="value" [class]="'status-' + (currentStatus || 'pending').toLowerCase()">
            {{ currentStatus || 'PENDING' }}
          </span>
        </div>
      </div>
    </div>

    <!-- Instructions -->
    <div class="instructions-card">
      <h3>How to Use</h3>
      <ol>
        <li><strong>Complete Flow:</strong> Watch the entire polling sequence automatically</li>
        <li><strong>Failure Flow:</strong> See how the UI handles random failures</li>
        <li><strong>Manual Steps:</strong> Run individual steps with custom timeouts</li>
        <li><strong>Success/Failure:</strong> Test specific success or failure scenarios</li>
      </ol>
      <p class="note">
        <strong>Note:</strong> Watch the code-window on the right to see how it responds
        to each polling state. The UI will update tabs, show progress, and display
        different content based on the current state.
      </p>
    </div>
  </div>

  <!-- Right Panel - Code Window -->
  <div class="code-window-panel">
    <div class="panel-header">
      <h3>Code Window Response</h3>
      <p>This is how the actual code-window component responds to polling states</p>
    </div>

    <div class="code-window-container">
      <app-code-window></app-code-window>
    </div>
  </div>
</div>
