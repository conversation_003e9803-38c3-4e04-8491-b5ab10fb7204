import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';

import { BrainstormerStepperComponent } from "../components/stepper/stepper.component";
import { ButtonComponent } from '@awe/play-comp-library';
import { StepperService, StepperStep } from '../../shared/services/stepper-service/stepper.service';

// Import all page components
import { UnderstandingComponent } from '../pages/understanding/understanding.component';
import { UserPersonaComponent } from '../pages/user-persona/user-persona.component';
import { FeatureListComponent } from '../pages/feature-list/feature-list.component';
import { SwotAnalysisComponent } from "../pages/swot-analysis/swot-analysis.component";
import { ProductRoadmapComponent } from "../pages/product-roadmap/product-roadmap.component";

@Component({
  selector: 'app-brainstorming',
  imports: [
    CommonModule,
    BrainstormerStepperComponent,
    But<PERSON><PERSON>omponent,
    UnderstandingComponent,
    UserPersonaComponent,
    FeatureListComponent,
    SwotAnalysisComponent,
    ProductRoadmapComponent
],
  templateUrl: './brainstorming.component.html',
  styleUrl: './brainstorming.component.scss',
})
export class BrainstormingComponent implements OnInit, OnDestroy {
  roboBallIcon: string = '/icons/robo_ball.svg';

  currentStep: StepperStep | null = null;
  currentStepIndex: number = 0;
  canGoNext: boolean = false;
  canGoPrevious: boolean = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private stepperService: StepperService
  ) {}

  ngOnInit(): void {
    // Subscribe to current step changes
    this.subscriptions.push(
      this.stepperService.currentStep$.subscribe(step => {
        this.currentStep = step;
        this.updateNavigationState();
      })
    );

    // Subscribe to current step index changes
    this.subscriptions.push(
      this.stepperService.currentStepIndex$.subscribe(index => {
        this.currentStepIndex = index;
        this.updateNavigationState();
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onStepChanged(event: { step: StepperStep; index: number }): void {
    // Handle step change if needed
    console.log('Step changed:', event);
  }

  nextStep(): void {
    if (this.stepperService.nextStep()) {
      // Mark current step as completed when moving to next
      this.stepperService.markStepAsCompleted(this.currentStepIndex - 1);
    }
  }

  previousStep(): void {
    this.stepperService.previousStep();
  }

  private updateNavigationState(): void {
    this.canGoNext = this.stepperService.canGoNext();
    this.canGoPrevious = this.stepperService.canGoPrevious();
  }

  toggleLeftPanel(): void {
    // Implement left panel toggle logic
  }

  // Helper method to get current component name for rendering
  getCurrentComponent(): string {
    return this.currentStep?.component || 'understanding';
  }

  // Helper method to check if a specific component should be shown
  shouldShowComponent(componentName: string): boolean {
    return this.getCurrentComponent() === componentName;
  }
}
