.custom-stepper {
  width: 100%;
  margin-bottom: 32px;
  background: transparent;

  .stepper-container {
    width: 100%;
  }

  .stepper-header {
    width: 100%;
    padding: 20px 0;
  }

  .steps-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    width: 100%;
  }

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;
    cursor: default;
    transition: all 0.3s ease;

    &.clickable {
      cursor: pointer;

      &:hover {
        .step-circle {
          transform: scale(1.1);
        }
      }
    }

    &:not(:last-child) {
      margin-right: 20px;
    }
  }

  .step-circle {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #423d3d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    margin-bottom: 8px;

    .step-number {
      color: inherit;
      font-weight: 600;
    }

    .check-icon {
      width: 20px;
      height: 20px;
      color: white;
    }
  }

  .step-label {
    text-align: center;
    max-width: 120px;

    .step-title {
      display: block;
      font-weight: 500;
      font-size: 14px;
      line-height: 1.2;
      margin-bottom: 4px;
      transition: color 0.3s ease;
    }

    .step-description {
      display: block;
      font-size: 12px;
      line-height: 1.3;
      opacity: 0.7;
    }
  }

  .step-connector {
    position: absolute;
    top: 7px;
    left: 54%;
    right: -50%;
    height: 2px;
    background-color: #e0e0e0;
    transition: background-color 0.3s ease;
    z-index: 1;

    &.completed {
      background-color: #4caf50;
    }
  }

  // Step States
  .step-item {
    &.inactive {
      .step-circle {
        background-color: #f5f5f5;
        border: 2px solid #e0e0e0;
        color: #9e9e9e;
      }

      .step-title {
        color: #9e9e9e;
      }
    }

    &.active {
      .step-circle {
        background-color: #2196f3;
        border: 2px solid #2196f3;
        color: white;
        box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.2);
      }

      .step-title {
        color: #2196f3;
        font-weight: 600;
      }
    }

    &.completed {
      .step-circle {
        background-color: #4caf50;
        border: 2px solid #4caf50;
        color: white;
      }

      .step-title {
        color: #4caf50;
        font-weight: 600;
      }
    }
  }

  // Progress Bar
  .progress-container {
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 12px;

    .progress-bar {
      flex: 1;
      height: 6px;
      background-color: #f5f5f5;
      border-radius: 3px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #2196f3 0%, #4caf50 100%);
        border-radius: 3px;
        transition: width 0.5s ease;
      }
    }

    .progress-text {
      font-size: 12px;
      font-weight: 500;
      color: #666;
      white-space: nowrap;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .custom-stepper {
    .steps-wrapper {
      flex-direction: column;
      gap: 20px;
    }

    .step-item {
      flex-direction: row;
      align-items: center;
      width: 100%;
      margin-right: 0 !important;

      .step-circle {
        margin-bottom: 0;
        margin-right: 12px;
        width: 32px;
        height: 32px;
        font-size: 12px;

        .check-icon {
          width: 16px;
          height: 16px;
        }
      }

      .step-label {
        text-align: left;
        max-width: none;
        flex: 1;

        .step-title {
          font-size: 16px;
          margin-bottom: 2px;
        }

        .step-description {
          font-size: 14px;
        }
      }

      .step-connector {
        display: none;
      }
    }

    .progress-container {
      margin-top: 16px;

      .progress-text {
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .custom-stepper {
    .step-item {
      .step-circle {
        width: 28px;
        height: 28px;
        font-size: 11px;
      }

      .step-label {
        .step-title {
          font-size: 14px;
        }

        .step-description {
          font-size: 12px;
        }
      }
    }
  }
}
