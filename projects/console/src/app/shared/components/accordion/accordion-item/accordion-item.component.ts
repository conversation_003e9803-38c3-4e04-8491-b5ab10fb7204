import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-accordion-item',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './accordion-item.component.html',
  styleUrl: './accordion-item.component.scss'
})
export class AccordionItemComponent {
  @Input() title: string = '';
  @Input() isOpen: boolean = false;
  @Output() toggle = new EventEmitter<void>();
  
  toggleAccordion(): void {
    this.isOpen = !this.isOpen;
    this.toggle.emit();
  }
} 