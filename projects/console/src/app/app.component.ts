import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { NavHeaderComponent } from './shared/components/nav-header/nav-header.component';
import { BreadcrumbComponent } from './shared/components/breadcrumb/breadcrumb.component';
import { ThemeService } from './shared/services/theme/theme.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NavHeaderComponent, BreadcrumbComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'console';
  
  constructor(private themeService: ThemeService) {}
  
  ngOnInit(): void {
    // Apply initial theme from saved preferences
    const savedTheme = this.themeService.getCurrentTheme();
    this.themeService.setTheme(savedTheme);
  }
}
