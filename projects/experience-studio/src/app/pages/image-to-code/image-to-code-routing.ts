import { Routes } from '@angular/router';
import { PromptSubmissionGuard } from '../../shared/guards/prompt-submission.guard';

export const IMAGE_TO_CODE_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./image-to-code.component').then(m => m.ImageToCodeComponent),
    children: [
      {
        path: 'code-preview',
        loadComponent: () =>
          import('../../shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
        canActivate: [PromptSubmissionGuard],
        outlet: 'primary',
      },
      {
        path: 'main',
        loadComponent: () => import('./image-to-code.component').then(m => m.ImageToCodeComponent),
        outlet: 'primary',
      },
      {
        path: 'prompt',
        loadComponent: () =>
          import('./components/prompt-content/prompt-content.component').then(
            m => m.PromptContentComponent
          ),
        outlet: 'primary',
      },

      { path: '', redirectTo: 'prompt', pathMatch: 'full' },
    ],
  },
];
