import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';
import { environment } from './environments/environment';
import { logger } from './app/shared/utils/logger';

bootstrapApplication(AppComponent, appConfig)
  .catch((err) => {
    if (!environment.production) {
      logger.error('Error during application bootstrap:', err);
    }
  });
