import { Component, Input, Pipe, PipeTransform, Host<PERSON><PERSON>ener, <PERSON><PERSON><PERSON><PERSON>, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { ButtonComponent, IconsComponent } from '@awe/play-comp-library';
import { Agent } from '../../interfaces/agent-list.interface';
import {
  FilterTabsComponent,
  FilterTab,
} from '../filter-tabs/filter-tabs.component';

@Pipe({
  name: 'truncate',
  standalone: true
})
export class TruncatePipe implements PipeTransform {
  transform(value: string, limit = 75): string {
    if (!value) return '';
    if (value.length <= limit) return value;
    return value.substring(0, limit) + '...';
  }
}

@Component({
  selector: 'app-agents',
  templateUrl: './agents.component.html',
  styleUrls: ['./agents.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule, ButtonComponent, FilterTabsComponent, TruncatePipe, IconsComponent],
  encapsulation: ViewEncapsulation.None
})
export class AgentsComponent implements OnDestroy {
  @Input() agents: Agent[] = [];
  @Input() showExploreButton = true;

  // New property to track the selected agent for details panel
  selectedAgent: Agent | null = null;

  // Object to keep track of hover state and border color for each agent
  agentHoverState: { [key: number]: { isHovering: boolean; borderColor: string } } = {};

  // Mapping of studio types to their respective border colors
  studioColors: { [key: string]: string } = {
    'Platform Studio': '#FFF0C2',
    'Data Studio': 'rgba(0, 209, 171, 0.40)',
    'Experience Studio': 'rgba(249, 108, 171, 0.40)',
    'Product Studio': 'rgba(168, 169, 227, 0.40)',
    'Quality Studio': '#F4CECB'
  };

  activeFilter = 'all';
  filterTabs: FilterTab[] = [
    { id: 'all', label: 'All', priority: 100 },
    { id: 'owned', label: 'Owned by me', icon: 'fas fa-user', priority: 90 },
    {
      id: 'experience',
      label: 'Experience',
      icon: 'fas fa-lightbulb',
      priority: 80,
    },
    { id: 'product', label: 'Product', icon: 'fas fa-box', priority: 70 },
    { id: 'data', label: 'Data', icon: 'fas fa-database', priority: 60 },
    { id: 'finops', label: 'Finops', icon: 'fas fa-code', priority: 50 },
    {
      id: 'quality',
      label: 'Quality Engineering',
      icon: 'fas fa-check-circle',
      priority: 40,
    },
    {
      id: 'platform',
      label: 'Platform',
      icon: 'fas fa-layer-group',
      priority: 30,
    },
  ];

  constructor(private readonly router: Router) { }

  /**
   * Host listener for keyboard events for accessibility
   * @param event KeyboardEvent
   */
  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    // Close the details panel when Escape key is pressed
    if (event.key === 'Escape' && this.selectedAgent) {
      this.selectedAgent = null;
      document.body.classList.remove('details-panel-open');
    }
  }

  onFilterChange(filterId: string) {
    this.activeFilter = filterId;
  }

  navigate() {
    this.router.navigateByUrl('/agent-list');
  }

  /**
   * Shows the agent details panel for the selected agent
   * @param agent The agent to display details for
   */
  showAgentDetails(agent: Agent): void {
    this.selectedAgent = agent;
    // Add a class to the body to prevent scrolling when overlay is open
    document.body.classList.add('details-panel-open');
  }

  /**
   * Closes the agent details panel
   * @param event The click event
   */
  closeAgentDetails(event: MouseEvent): void {
    event.stopPropagation();
    this.selectedAgent = null;
    // Remove the class from the body to allow scrolling again
    document.body.classList.remove('details-panel-open');
  }

  /**
   * Navigates to the playground for the selected agent
   */
  goToPlayground(): void {
    // You can replace this with the actual navigation logic
    this.router.navigate(['/playground', this.selectedAgent?.id]);
  }

  /**
   * Handles the hover event for an agent card
   * @param agentIndex The index of the agent
   * @param studioType The type of the studio
   */
  onArrowHover(agentIndex: number, studioType: string) {
    const color = this.studioColors[studioType] || 'transparent';
    this.agentHoverState[agentIndex] = { isHovering: true, borderColor: color };
  }

  /**
   * Handles the leave event for an agent card
   * @param agentIndex The index of the agent
   */
  onArrowLeave(agentIndex: number) {
    this.agentHoverState[agentIndex] = { isHovering: false, borderColor: 'transparent' };
  }

  /**
   * Clean up when component is destroyed
   */
  ngOnDestroy(): void {
    // Make sure to remove the class from body when component is destroyed
    document.body.classList.remove('details-panel-open');
  }
}
