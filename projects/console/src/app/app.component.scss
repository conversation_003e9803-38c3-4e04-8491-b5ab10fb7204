:host {
  display: block;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

/* This is our dedicated background div with !important to override anything from experience-studio */
.app-background {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-image: var(--bg-image) !important;
  background-size: cover !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  z-index: -1 !important;
  transition: background-image 0.3s ease !important;
}

/* Reset any backgrounds from imported styles */
html, body {
  background-image: none !important;
  background-color: transparent !important;
}

.app-container {
  width: 100%;
  height: 100vh;
  position: relative;
  padding: 0 48px;
  max-width: 2400px;
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1; /* Ensure app container is above the background */
  background-color: transparent !important;
  color: var(--text-color);
  transition: color 0.3s ease;
  
  /* Glass effect overlay for content legibility */
  .glass-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px 0 var(--glass-shadow);
    z-index: 0; /* Should be above the background but below content */
    pointer-events: none;
    transition: background 0.3s ease, border 0.3s ease, box-shadow 0.3s ease;
  }
}

app-nav-header {
  position: relative;
  z-index: 10; /* Adjusted to ensure proper stacking context */
  flex-shrink: 0;
}

app-breadcrumb {
  flex-shrink: 0;
  position: relative;
  z-index: 5; /* Added z-index for breadcrumb */
}

.content-wrapper {
  padding: 0;
  width: 100%;
  position: relative;
  z-index: 2; /* Adjusted to be above glass effect but below navigation */
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
}

/* Responsive padding adjustments */
@media (max-width: 1200px) {
  .app-container {
    padding: 0 36px;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 0 24px;
  }
}

@media (max-width: 480px) {
  .app-container {
    padding: 0 16px;
  }
}
