<div class="modal-container" *ngIf="isOpen" (click)="onContainerClick($event)">
  <div [ngClass]="modalContentClass">
    <!-- Modal Header -->
    <div class="modal-header" *ngIf="title || showCloseButton">
      <h2 class="modal-title" *ngIf="title">{{ title }}</h2>
      <button
        *ngIf="showCloseButton"
        class="close-button"
        aria-label="Close modal"
        (click)="closeModal()"
      >
        <svg
          width="14"
          height="14"
          viewBox="0 0 14 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1 1L13 13M1 13L13 1"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
          />
        </svg>
      </button>
    </div>

    <!-- Modal Body -->
    <div class="modal-body">
      <ng-content></ng-content>
    </div>

    <!-- Mo<PERSON> Footer -->
    <div class="modal-footer">
      <ng-content select="[modal-footer]"></ng-content>
    </div>
  </div>
</div>
