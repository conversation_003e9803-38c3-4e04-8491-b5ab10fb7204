import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { SafeHtml } from '@angular/platform-browser';

// Screen node interface (React Flow inspired)
export interface ScreenNode {
  id: string;
  type: 'screen';
  data: {
    title: string;
    htmlContent: SafeHtml;
    rawContent: string;
    width: number;
    height: number;
    isLoading: boolean;
    thumbnail?: string;
  };
  position: {
    x: number;
    y: number;
  };
  selected: boolean;
  dragging: boolean;
  visible: boolean;
}

// Node connection interface
export interface NodeConnection {
  id: string;
  source: string;
  target: string;
  type?: 'default' | 'flow' | 'relation';
  animated?: boolean;
  label?: string;
}

// Node layout configuration
export interface NodeLayoutConfig {
  defaultWidth: number;
  defaultHeight: number;
  minWidth: number;
  minHeight: number;
  maxWidth: number;
  maxHeight: number;
  gridSize: number;
  spacing: number;
  columns: number;
}

// Node bounds for viewport culling
export interface NodeBounds {
  left: number;
  top: number;
  right: number;
  bottom: number;
}

@Injectable({
  providedIn: 'root'
})
export class NodeManagementService {
  // Default layout configuration
  private readonly DEFAULT_LAYOUT: NodeLayoutConfig = {
    defaultWidth: 420,
    defaultHeight: 720,
    minWidth: 420,
    minHeight: 720,
    maxWidth: 420,
    maxHeight: 720,
    gridSize: 20,
    spacing: 60,
    columns: 2
  };

  // State subjects
  private nodesSubject = new BehaviorSubject<ScreenNode[]>([]);
  private connectionsSubject = new BehaviorSubject<NodeConnection[]>([]);
  private selectedNodesSubject = new BehaviorSubject<string[]>([]);
  private layoutConfigSubject = new BehaviorSubject<NodeLayoutConfig>(this.DEFAULT_LAYOUT);

  // Public observables
  nodes$: Observable<ScreenNode[]> = this.nodesSubject.asObservable();
  connections$: Observable<NodeConnection[]> = this.connectionsSubject.asObservable();
  selectedNodes$: Observable<string[]> = this.selectedNodesSubject.asObservable();
  layoutConfig$: Observable<NodeLayoutConfig> = this.layoutConfigSubject.asObservable();

  constructor() {}

  /**
   * Get current nodes
   */
  getNodes(): ScreenNode[] {
    return this.nodesSubject.getValue();
  }

  /**
   * Get node by ID
   */
  getNode(nodeId: string): ScreenNode | undefined {
    return this.getNodes().find(node => node.id === nodeId);
  }

  /**
   * Add a new node
   */
  addNode(node: Omit<ScreenNode, 'selected' | 'dragging' | 'visible'>): void {
    const newNode: ScreenNode = {
      ...node,
      selected: false,
      dragging: false,
      visible: true
    };

    const currentNodes = this.getNodes();
    this.nodesSubject.next([...currentNodes, newNode]);
  }

  /**
   * Update node data
   */
  updateNode(nodeId: string, updates: Partial<ScreenNode>): void {
    const currentNodes = this.getNodes();
    const nodeIndex = currentNodes.findIndex(node => node.id === nodeId);

    if (nodeIndex === -1) return;

    const updatedNodes = [...currentNodes];
    updatedNodes[nodeIndex] = {
      ...currentNodes[nodeIndex],
      ...updates
    };

    this.nodesSubject.next(updatedNodes);
  }

  /**
   * Update node position
   */
  updateNodePosition(nodeId: string, position: { x: number; y: number }): void {
    this.updateNode(nodeId, { position });
  }

  /**
   * Update node size
   */
  updateNodeSize(nodeId: string, size: { width: number; height: number }): void {
    const node = this.getNode(nodeId);
    if (!node) return;

    const config = this.layoutConfigSubject.getValue();
    const constrainedSize = {
      width: Math.max(config.minWidth, Math.min(config.maxWidth, size.width)),
      height: Math.max(config.minHeight, Math.min(config.maxHeight, size.height))
    };

    this.updateNode(nodeId, {
      data: {
        ...node.data,
        width: constrainedSize.width,
        height: constrainedSize.height
      }
    });
  }

  /**
   * Remove node
   */
  removeNode(nodeId: string): void {
    const currentNodes = this.getNodes();
    const updatedNodes = currentNodes.filter(node => node.id !== nodeId);
    this.nodesSubject.next(updatedNodes);

    // Remove from selection if selected
    this.deselectNode(nodeId);

    // Remove any connections involving this node
    this.removeConnectionsForNode(nodeId);
  }

  /**
   * Select node(s)
   */
  selectNode(nodeId: string, multiSelect: boolean = false): void {
    const currentSelected = this.selectedNodesSubject.getValue();

    let newSelected: string[];
    if (multiSelect) {
      newSelected = currentSelected.includes(nodeId)
        ? currentSelected.filter(id => id !== nodeId)
        : [...currentSelected, nodeId];
    } else {
      newSelected = [nodeId];
    }

    this.selectedNodesSubject.next(newSelected);
    this.updateNodeSelectionStates(newSelected);
  }

  /**
   * Deselect node
   */
  deselectNode(nodeId: string): void {
    const currentSelected = this.selectedNodesSubject.getValue();
    const newSelected = currentSelected.filter(id => id !== nodeId);
    this.selectedNodesSubject.next(newSelected);
    this.updateNodeSelectionStates(newSelected);
  }

  /**
   * Clear all selections
   */
  clearSelection(): void {
    this.selectedNodesSubject.next([]);
    this.updateNodeSelectionStates([]);
  }

  /**
   * Update node selection states
   */
  private updateNodeSelectionStates(selectedIds: string[]): void {
    const currentNodes = this.getNodes();
    const updatedNodes = currentNodes.map(node => ({
      ...node,
      selected: selectedIds.includes(node.id)
    }));
    this.nodesSubject.next(updatedNodes);
  }

  /**
   * Calculate optimal layout positions for nodes
   */
  calculateOptimalLayout(nodeCount: number): { x: number; y: number }[] {
    const config = this.layoutConfigSubject.getValue();
    const positions: { x: number; y: number }[] = [];

    const totalWidth = config.defaultWidth + config.spacing;
    const totalHeight = config.defaultHeight + config.spacing;

    // Calculate grid layout centered around origin
    for (let i = 0; i < nodeCount; i++) {
      const row = Math.floor(i / config.columns);
      const col = i % config.columns;

      // Center the grid around origin
      const startX = -(config.columns * totalWidth) / 2 + totalWidth / 2;
      const startY = -(Math.ceil(nodeCount / config.columns) * totalHeight) / 2 + totalHeight / 2;

      positions.push({
        x: startX + col * totalWidth,
        y: startY + row * totalHeight
      });
    }

    return positions;
  }

  /**
   * Update node visibility based on viewport bounds
   */
  updateNodeVisibility(viewportBounds: NodeBounds): void {
    const currentNodes = this.getNodes();
    const updatedNodes = currentNodes.map(node => ({
      ...node,
      visible: this.isNodeInViewport(node, viewportBounds)
    }));
    this.nodesSubject.next(updatedNodes);
  }

  /**
   * Check if node is visible in viewport
   */
  private isNodeInViewport(node: ScreenNode, bounds: NodeBounds): boolean {
    const nodeLeft = node.position.x;
    const nodeTop = node.position.y;
    const nodeRight = node.position.x + node.data.width;
    const nodeBottom = node.position.y + node.data.height;

    return !(nodeRight < bounds.left ||
             nodeLeft > bounds.right ||
             nodeBottom < bounds.top ||
             nodeTop > bounds.bottom);
  }

  /**
   * Get content bounds for all nodes
   */
  getContentBounds(): { minX: number; minY: number; maxX: number; maxY: number } {
    const nodes = this.getNodes();

    if (nodes.length === 0) {
      return { minX: 0, minY: 0, maxX: 0, maxY: 0 };
    }

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    nodes.forEach(node => {
      minX = Math.min(minX, node.position.x);
      minY = Math.min(minY, node.position.y);
      maxX = Math.max(maxX, node.position.x + node.data.width);
      maxY = Math.max(maxY, node.position.y + node.data.height);
    });

    return { minX, minY, maxX, maxY };
  }

  /**
   * Snap position to grid
   */
  snapToGrid(position: { x: number; y: number }): { x: number; y: number } {
    const config = this.layoutConfigSubject.getValue();
    return {
      x: Math.round(position.x / config.gridSize) * config.gridSize,
      y: Math.round(position.y / config.gridSize) * config.gridSize
    };
  }

  /**
   * Add connection between nodes
   */
  addConnection(connection: NodeConnection): void {
    const currentConnections = this.connectionsSubject.getValue();
    this.connectionsSubject.next([...currentConnections, connection]);
  }

  /**
   * Remove connection
   */
  removeConnection(connectionId: string): void {
    const currentConnections = this.connectionsSubject.getValue();
    const updatedConnections = currentConnections.filter(conn => conn.id !== connectionId);
    this.connectionsSubject.next(updatedConnections);
  }

  /**
   * Remove all connections for a node
   */
  private removeConnectionsForNode(nodeId: string): void {
    const currentConnections = this.connectionsSubject.getValue();
    const updatedConnections = currentConnections.filter(
      conn => conn.source !== nodeId && conn.target !== nodeId
    );
    this.connectionsSubject.next(updatedConnections);
  }

  /**
   * Clear all nodes and connections
   */
  clearAll(): void {
    this.nodesSubject.next([]);
    this.connectionsSubject.next([]);
    this.selectedNodesSubject.next([]);
  }

  /**
   * Update layout configuration
   */
  updateLayoutConfig(config: Partial<NodeLayoutConfig>): void {
    const current = this.layoutConfigSubject.getValue();
    this.layoutConfigSubject.next({
      ...current,
      ...config
    });
  }

  /**
   * Generate unique node ID
   */
  generateNodeId(): string {
    return `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
