import { Component, Input, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'awe-card',
  standalone: true,
  imports: [CommonModule], // Add CommonModule here for *ngIf, etc.
  templateUrl: './awe-card.component.html',
  styleUrls: ['./awe-card.component.scss']
})
export class AweCardComponent {
  // REMOVED: title, icon, iconBg as header content is now fully projected.

  @Input() showHeader: boolean = true;
  @Input() showBody: boolean = true;
  @Input() showFooter: boolean = false;

  @Input() cardClass: string = '';

  @Input() applyHeaderPadding: boolean = true; // Still useful for the header container
  @Input() applyBodyPadding: boolean = true;
  @Input() applyFooterPadding: boolean = true;

  constructor() {}
}