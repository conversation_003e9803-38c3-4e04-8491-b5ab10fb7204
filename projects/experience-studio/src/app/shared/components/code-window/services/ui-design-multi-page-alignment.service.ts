import { Injectable } from '@angular/core';
import { createLogger } from '../../../utils/logger';
import { UIDesignCanvasService } from './ui-design-canvas.service';
import { UIDesignNodeService } from './ui-design-node.service';

export interface MultiPageLayoutConfig {
  spacing: {
    horizontal: number;
    vertical: number;
  };
  alignment: 'grid' | 'flow' | 'custom';
  columns: number;
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface PagePosition {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  row: number;
  column: number;
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignMultiPageAlignmentService {
  private readonly logger = createLogger('UIDesignMultiPageAlignment');

  // Reference viewport configuration for perfect alignment
  private readonly referenceViewport = {
    x: 39.86,
    y: 82.95,
    zoom: 0.5
  };

  // Default layout configuration optimized for multiple pages
  private readonly defaultLayoutConfig: MultiPageLayoutConfig = {
    spacing: {
      horizontal: 50, // Space between pages horizontally
      vertical: 60    // Space between pages vertically
    },
    alignment: 'grid',
    columns: 3, // 3 columns for optimal viewing at 50% zoom
    padding: {
      top: 40,
      right: 40,
      bottom: 40,
      left: 40
    }
  };

  constructor(
    private canvasService: UIDesignCanvasService,
    private nodeService: UIDesignNodeService
  ) {
    this.logger.info('UI Design Multi-Page Alignment Service initialized');
  }

  /**
   * Apply perfect alignment for multiple pages using reference viewport
   */
  alignMultiplePages(): void {
    const nodes = this.nodeService.getNodes();
    
    if (nodes.length === 0) {
      this.logger.warn('No nodes to align');
      return;
    }

    // Calculate optimal positions for multiple pages
    const pagePositions = this.calculatePagePositions(nodes);
    
    // Update node positions
    pagePositions.forEach(position => {
      this.nodeService.updateNode(position.id, {
        position: { x: position.x, y: position.y }
      });
    });

    // Set viewport to reference position for perfect viewing
    this.canvasService.updateViewport(this.referenceViewport);

    this.logger.info('✨ Multi-page alignment applied:', {
      totalPages: nodes.length,
      layoutConfig: this.defaultLayoutConfig,
      referenceViewport: this.referenceViewport,
      pagePositions
    });
  }

  /**
   * Calculate optimal positions for pages in a grid layout
   */
  private calculatePagePositions(nodes: any[]): PagePosition[] {
    const config = this.defaultLayoutConfig;
    const positions: PagePosition[] = [];

    // Standard page dimensions (can be customized)
    const pageWidth = 300;
    const pageHeight = 400;

    nodes.forEach((node, index) => {
      const row = Math.floor(index / config.columns);
      const column = index % config.columns;

      const x = config.padding.left + column * (pageWidth + config.spacing.horizontal);
      const y = config.padding.top + row * (pageHeight + config.spacing.vertical);

      positions.push({
        id: node.id,
        x,
        y,
        width: pageWidth,
        height: pageHeight,
        row,
        column
      });
    });

    return positions;
  }

  /**
   * Get optimal viewport position for current page layout
   */
  getOptimalViewportForPages(): { x: number; y: number; zoom: number } {
    const nodes = this.nodeService.getNodes();
    
    if (nodes.length === 0) {
      return this.referenceViewport;
    }

    // For multiple pages, use reference viewport for best overview
    if (nodes.length > 1) {
      return this.referenceViewport;
    }

    // For single page, center it perfectly
    const contentBounds = this.nodeService.getContentBounds();
    const containerSize = { width: 800, height: 600 }; // Default container size

    const centerX = (containerSize.width - contentBounds.width * this.referenceViewport.zoom) / 2;
    const centerY = (containerSize.height - contentBounds.height * this.referenceViewport.zoom) / 2;

    return {
      x: centerX,
      y: centerY,
      zoom: this.referenceViewport.zoom
    };
  }

  /**
   * Auto-arrange pages when new pages are added
   */
  autoArrangePages(): void {
    const nodes = this.nodeService.getNodes();
    
    if (nodes.length <= 1) {
      // Single page - center it
      this.centerSinglePage();
    } else {
      // Multiple pages - use grid layout
      this.alignMultiplePages();
    }
  }

  /**
   * Center a single page perfectly
   */
  private centerSinglePage(): void {
    const nodes = this.nodeService.getNodes();
    
    if (nodes.length !== 1) {
      return;
    }

    const node = nodes[0];
    const centerX = 200; // Centered position for single page
    const centerY = 150;

    this.nodeService.updateNode(node.id, {
      position: { x: centerX, y: centerY }
    });

    // Use reference viewport for consistent viewing
    this.canvasService.updateViewport(this.referenceViewport);

    this.logger.info('📍 Single page centered:', {
      nodeId: node.id,
      position: { x: centerX, y: centerY },
      viewport: this.referenceViewport
    });
  }

  /**
   * Get layout configuration
   */
  getLayoutConfig(): MultiPageLayoutConfig {
    return { ...this.defaultLayoutConfig };
  }

  /**
   * Update layout configuration
   */
  updateLayoutConfig(updates: Partial<MultiPageLayoutConfig>): void {
    Object.assign(this.defaultLayoutConfig, updates);
    this.logger.info('Layout configuration updated:', updates);
  }

  /**
   * Calculate total content bounds for all pages
   */
  calculateTotalContentBounds(): { width: number; height: number; minX: number; minY: number; maxX: number; maxY: number } {
    const nodes = this.nodeService.getNodes();
    
    if (nodes.length === 0) {
      return { width: 0, height: 0, minX: 0, minY: 0, maxX: 0, maxY: 0 };
    }

    const positions = this.calculatePagePositions(nodes);
    
    const minX = Math.min(...positions.map(p => p.x));
    const minY = Math.min(...positions.map(p => p.y));
    const maxX = Math.max(...positions.map(p => p.x + p.width));
    const maxY = Math.max(...positions.map(p => p.y + p.height));

    return {
      width: maxX - minX,
      height: maxY - minY,
      minX,
      minY,
      maxX,
      maxY
    };
  }

  /**
   * Get reference viewport configuration
   */
  getReferenceViewport(): { x: number; y: number; zoom: number } {
    return { ...this.referenceViewport };
  }

  /**
   * Apply reference viewport immediately
   */
  applyReferenceViewport(): void {
    this.canvasService.updateViewport(this.referenceViewport);
    this.logger.info('🎯 Reference viewport applied for perfect alignment:', this.referenceViewport);
  }

  /**
   * Check if current viewport matches reference
   */
  isAtReferenceViewport(): boolean {
    const current = this.canvasService.getViewport();
    const tolerance = 1; // 1px tolerance

    return Math.abs(current.x - this.referenceViewport.x) < tolerance &&
           Math.abs(current.y - this.referenceViewport.y) < tolerance &&
           Math.abs(current.zoom - this.referenceViewport.zoom) < 0.01;
  }

  /**
   * Get alignment statistics
   */
  getAlignmentStats(): {
    totalPages: number;
    layoutType: string;
    isAtReference: boolean;
    contentBounds: any;
    viewport: any;
  } {
    const nodes = this.nodeService.getNodes();
    const contentBounds = this.calculateTotalContentBounds();
    const viewport = this.canvasService.getViewport();

    return {
      totalPages: nodes.length,
      layoutType: nodes.length > 1 ? 'multi-page-grid' : 'single-page-centered',
      isAtReference: this.isAtReferenceViewport(),
      contentBounds,
      viewport
    };
  }
}
