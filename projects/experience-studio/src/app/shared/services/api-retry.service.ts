import { Injectable } from '@angular/core';
import { Observable, throwError, timer } from 'rxjs';
import { retryWhen, mergeMap, take, tap } from 'rxjs/operators';
import { createLogger } from '../utils/logger';

export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  retryOn499: boolean;
  retryOnNetworkError: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ApiRetryService {
  private readonly logger = createLogger('ApiRetryService');

  // Default retry configuration for wireframe APIs
  private readonly defaultConfig: RetryConfig = {
    maxRetries: 2, // Retry up to 2 times for 499 errors
    retryDelay: 5000, // Wait 5 seconds between retries
    retryOn499: true, // Retry on 499 client closed request
    retryOnNetworkError: true // Retry on network errors (status 0)
  };

  /**
   * Apply retry logic to an observable for wireframe generation APIs
   * Specifically handles 499 errors and network failures
   */
  withRetry<T>(source: Observable<T>, config?: Partial<RetryConfig>): Observable<T> {
    const finalConfig = { ...this.defaultConfig, ...config };

    return source.pipe(
      retryWhen(errors =>
        errors.pipe(
          tap(error => {
            this.logger.info('🔄 Evaluating error for retry:', {
              status: error.status,
              message: error.message,
              name: error.name
            });
          }),
          mergeMap((error, index) => {
            const retryAttempt = index + 1;

            // Check if we should retry this error
            const shouldRetry = this.shouldRetryError(error, finalConfig);
            
            // Check if we've exceeded max retries
            if (retryAttempt > finalConfig.maxRetries) {
              this.logger.error(`❌ Max retries (${finalConfig.maxRetries}) exceeded for error:`, error);
              return throwError(() => error);
            }

            if (!shouldRetry) {
              this.logger.info('⏭️ Error not eligible for retry, failing immediately');
              return throwError(() => error);
            }

            this.logger.info(`🔄 Retrying request (attempt ${retryAttempt}/${finalConfig.maxRetries}) after ${finalConfig.retryDelay}ms delay`);
            
            // Wait for the specified delay before retrying
            return timer(finalConfig.retryDelay);
          }),
          take(finalConfig.maxRetries)
        )
      )
    );
  }

  /**
   * Determine if an error should be retried based on configuration
   */
  private shouldRetryError(error: any, config: RetryConfig): boolean {
    // Retry on 499 Client Closed Request if enabled
    if (error.status === 499 && config.retryOn499) {
      this.logger.info('🔄 499 error detected - will retry');
      return true;
    }

    // Retry on network errors (status 0) if enabled
    if (error.status === 0 && config.retryOnNetworkError) {
      this.logger.info('🔄 Network error detected - will retry');
      return true;
    }

    // Don't retry other errors
    this.logger.info(`⏭️ Error status ${error.status} not configured for retry`);
    return false;
  }

  /**
   * Create a retry configuration for wireframe generation APIs
   * These APIs are known to take a long time and may hit browser timeouts
   */
  getWireframeRetryConfig(): RetryConfig {
    return {
      maxRetries: 3, // More retries for wireframe APIs
      retryDelay: 10000, // Longer delay (10 seconds) for wireframe APIs
      retryOn499: true,
      retryOnNetworkError: true
    };
  }

  /**
   * Create a retry configuration for edit/regenerate APIs
   */
  getEditRetryConfig(): RetryConfig {
    return {
      maxRetries: 2, // Fewer retries for edit APIs
      retryDelay: 5000, // Standard delay
      retryOn499: true,
      retryOnNetworkError: true
    };
  }

  /**
   * Log retry statistics for monitoring
   */
  logRetryStats(apiEndpoint: string, totalAttempts: number, success: boolean): void {
    this.logger.info('📊 Retry Statistics:', {
      endpoint: apiEndpoint,
      totalAttempts,
      success,
      retriesUsed: totalAttempts - 1
    });
  }
}
