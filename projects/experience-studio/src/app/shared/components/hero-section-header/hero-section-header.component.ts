import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { HeadingComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-hero-section-header',
  imports: [HeadingComponent],
  standalone: true,
  templateUrl: './hero-section-header.component.html',
  styleUrl: './hero-section-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class HeroSectionHeaderComponent {
  @Input() headerDescription = '';
  @Input() headerTitle = '';
  @Input() subHeading = '';
}
