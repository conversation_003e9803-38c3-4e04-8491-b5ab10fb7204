import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { HeadingComponent, BodyTextComponent, CardsComponent } from '@awe/play-comp-library';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { RecentProjectService } from '../../services/recent-project-services/recent-project.service';

interface Project {
  project_id: string;
  project_name: string;
  project_description: string;
  project_type: string;
  last_modified: string;
}

interface CardOption {
  id: string;
  heading: string;
  description: string;
  actionText?: string;
  type: string;
  timestamp?: string;
}

@Component({ 
  selector: 'app-recent-projects',
  standalone: true,
  imports: [CommonModule, HeadingComponent, BodyTextComponent, CardsComponent],
  templateUrl: './recent-projects.component.html',
  styleUrl: './recent-projects.component.scss'
})
export class RecentProjectsComponent implements OnInit {
  theme: 'light' | 'dark' = 'light';
  selectedId: string | null = null;
  currentCategory: string = 'recent';
  isLoading: boolean = true;
  options: { [category: string]: CardOption[] } = {
    recent: [],
    all: []
  };

  // Mock data for testing
  private mockProjects: Project[] = [
    {
      project_id: '1',
      project_name: 'E-Commerce Dashboard',
      project_description: 'A modern dashboard for managing online store with analytics and inventory management features',
      project_type: 'UI',
      last_modified: '2025-05-19T10:30:00Z'
    },
    {
      project_id: '2',
      project_name: 'Task Management App',
      project_description: 'Collaborative task management application with real-time updates and team features',
      project_type: 'App',
      last_modified: '2025-05-18T15:45:00Z'
    },
    {
      project_id: '3',
      project_name: 'Healthcare Portal',
      project_description: 'Accessibility-focused healthcare portal for patient management and appointments',
      project_type: 'Accessibility',
      last_modified: '2025-05-17T09:20:00Z'
    },
    {
      project_id: '4',
      project_name: 'Analytics Platform',
      project_description: 'Data visualization and analytics platform with customizable dashboards',
      project_type: 'Analysis',
      last_modified: '2025-05-16T14:15:00Z'
    },
    {
      project_id: '5',
      project_name: 'Mobile Banking UI',
      project_description: 'User interface design for a mobile banking application with security features',
      project_type: 'UI',
      last_modified: '2025-05-15T11:30:00Z'
    },
    {
      project_id: '6',
      project_name: 'Social Media Dashboard',
      project_description: 'Analytics dashboard for social media management and content planning',
      project_type: 'Analysis',
      last_modified: '2025-05-14T16:45:00Z'
    }, {
      project_id: '1',
      project_name: 'E-Commerce Dashboard',
      project_description: 'A modern dashboard for managing online store with analytics and inventory management features',
      project_type: 'UI',
      last_modified: '2025-05-19T10:30:00Z'
    },
    {
      project_id: '2',
      project_name: 'Task Management App',
      project_description: 'Collaborative task management application with real-time updates and team features',
      project_type: 'App',
      last_modified: '2025-05-18T15:45:00Z'
    },
    {
      project_id: '3',
      project_name: 'Healthcare Portal',
      project_description: 'Accessibility-focused healthcare portal for patient management and appointments',
      project_type: 'Accessibility',
      last_modified: '2025-05-17T09:20:00Z'
    },
    {
      project_id: '4',
      project_name: 'Analytics Platform',
      project_description: 'Data visualization and analytics platform with customizable dashboards',
      project_type: 'Analysis',
      last_modified: '2025-05-16T14:15:00Z'
    },
    {
      project_id: '5',
      project_name: 'Mobile Banking UI',
      project_description: 'User interface design for a mobile banking application with security features',
      project_type: 'UI',
      last_modified: '2025-05-15T11:30:00Z'
    },
    {
      project_id: '6',
      project_name: 'Social Media Dashboard',
      project_description: 'Analytics dashboard for social media management and content planning',
      project_type: 'Analysis',
      last_modified: '2025-05-14T16:45:00Z'
    }
  ];

  constructor(
    private recentProjectService: RecentProjectService,
    private router: Router
  ) {}

  ngOnInit() {
    // Initialize with skeleton placeholders
    this.initializePlaceholders();

    // Use Intersection Observer to load projects only when the component is visible
    this.setupIntersectionObserver();
  }

  private setupIntersectionObserver() {
    // Create an observer to detect when the component is visible
    const options = {
      root: null, // viewport
      rootMargin: '100px', // start loading when component is 100px from viewport
      threshold: 0.1 // trigger when 10% of the component is visible
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Component is visible, load projects
          this.loadProjects();
          // Disconnect the observer after loading
          observer.disconnect();
        }
      });
    }, options);

    // Start observing the component
    const element = document.querySelector('.recent-creation-wrapper');
    if (element) {
      observer.observe(element);
    } else {
      // If element not found, load projects anyway
      this.loadProjects();
    }
  }

  private initializePlaceholders() {
    // Initialize with skeleton placeholders
    this.options['recent'] = Array(4).fill(null).map((_, i) => ({
      id: `placeholder-${i}`,
      heading: '',
      description: '',
      type: '',
      timestamp: ''
    }));

    this.options['all'] = Array(12).fill(null).map((_, i) => ({
      id: `placeholder-${i}`,
      heading: '',
      description: '',
      type: '',
      timestamp: ''
    }));
  }

  private loadProjects() {
    // Set loading state to true
    this.isLoading = true;

    // Simulate API delay
    setTimeout(() => {
      // Load recent projects (4 items)
      requestAnimationFrame(() => {
        this.options['recent'] = this.mapProjectsToCardOptions(this.mockProjects.slice(0, 4));
        this.isLoading = false;
      });

      // Load all projects
      requestAnimationFrame(() => {
        this.options['all'] = this.mapProjectsToCardOptions(this.mockProjects);
      });
    }, 1000); // 1 second delay to simulate network request
  }

  private truncateDescription(description: string): string {
    const words = description.split(' ');
    // Limit to approximately 3 lines (about 10-12 words)
    if (words.length > 10) {
      return words.slice(0, 10).join(' ') + '...';
    }
    return description;
  }

  private mapProjectsToCardOptions(projects: Project[]): CardOption[] {
    return projects.map(project => ({
      id: project.project_id,
      heading: project.project_name,
      description: this.truncateDescription(project.project_description.replace(/^"|"$/g, '')), // Remove quotes and truncate
      type: project.project_type.toLowerCase(),
      timestamp: this.recentProjectService.formatDate(project.last_modified)
    }));
  }

  switchCategory(category: string): void {
    if (this.currentCategory !== category) {
      // Update category immediately
      this.currentCategory = category;

      // Use requestAnimationFrame for smoother animation
      requestAnimationFrame(() => {
        const gridElement = document.querySelector('.cards-grid') as HTMLElement;
        if (gridElement) {
          // Remove existing animation classes
          gridElement.classList.remove('slide-recent', 'slide-all');

          // Force a reflow to ensure the animation triggers
          void gridElement.offsetWidth;

          // Add the appropriate animation class
          gridElement.classList.add(category === 'recent' ? 'slide-recent' : 'slide-all');
        }
      });
    }
  }

  getCurrentOptions(): CardOption[] {
    return this.options[this.currentCategory] || [];
  }

  isSelected(id: string): boolean {
    return this.selectedId === id;
  }

  handleSelection(id: string, event?: Event): void {
    if (event) {
      event.preventDefault();
    }
    this.selectedId = id;
  }

  getDefaultActionText(type: string): string {
    const actionMap: { [key: string]: string } = {
      'ui': 'Generate UI',
      'app': 'Generate App',
      'analysis': 'Design Analysis',
      'accessibility': 'Review Accessibility'
    };
    return actionMap[type.toLowerCase()] || 'View';
  }

  trackByFn(_: number, item: CardOption): string {
    return item.id;
  }
}
