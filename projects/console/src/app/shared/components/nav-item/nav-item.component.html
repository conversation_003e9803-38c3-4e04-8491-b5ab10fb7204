<div class="nav-item-container">
  <div 
    [class.selected]="selected"
    class="nav-item"
    (click)="onClick($event)">
    <span class="item-icon" [class.selected]="selected">
      <!-- SVG background approach for better color control -->
      <img [src]="icon" alt="" class="nav-icon" [class.selected]="selected">
    </span>
    <span class="item-label">{{ label }}</span>
    <span *ngIf="hasDropdown" class="dropdown-arrow" [class.open]="dropdownOpen">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
        <path d="M4 6L8 10L12 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </span>
  </div>
  
  <!-- Dropdown menu -->
  <app-dropdown-menu 
    *ngIf="hasDropdown" 
    [items]="dropdownItems" 
    [visible]="dropdownOpen"
    (itemSelected)="onDropdownItemSelected($event)">
  </app-dropdown-menu>
</div> 