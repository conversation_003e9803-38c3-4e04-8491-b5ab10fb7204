import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ProductNavBarComponent } from './product/shared/components/product-nav-bar/product-nav-bar.component';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, ProductNavBarComponent, CommonModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent {
  title = 'product-studio';
}
