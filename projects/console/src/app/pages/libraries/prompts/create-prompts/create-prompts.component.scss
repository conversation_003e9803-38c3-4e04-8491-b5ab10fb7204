.create-prompts-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: transparent;
}

// Chat container styles
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
  border-radius: 12px;
  overflow: hidden;
  position: relative; // Needed for absolute positioning of child elements
}

.playground-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--dashboard-primary);
  margin: 0 0 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Make sure chat interface takes full height
app-chat-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}

form {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.form-layout {
  display: flex;
  flex-direction: row;
  gap: 20px;
  padding: 20px;
  flex: 1;
  overflow: hidden;
  align-items: stretch;
  height: 100%;
  overflow-y: auto;
  transition: all 0.3s ease;
  
  &.three-column-layout {
    .left-column {
      width: 25%;
      transition: width 0.3s ease;
      
      @media (max-width: 1400px) {
        width: 25%;
      }
      
      @media (max-width: 1200px) {
        width: 100%;
      }
    }
    
    .middle-column {
      width: 40%;
      transition: width 0.3s ease;
      
      @media (max-width: 1400px) {
        width: 35%;
      }
      
      @media (max-width: 1200px) {
        width: 100%;
      }
    }
  }
  
  @media (max-width: 1400px) {
    gap: 20px;
  }
  
  @media (max-width: 1200px) {
    gap: 16px;
    padding: 16px;
  }
  
  @media (max-width: 992px) {
    flex-wrap: wrap;
  }
  
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px;
  }

  .left-column, .middle-column, .chat-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: calc(100vh - 180px);
    max-height: 800px;
    
    @media (max-width: 1200px) {
      gap: 16px;
      height: calc(100vh - 160px);
    }
    
    @media (max-width: 992px) {
      width: 100%;
      height: auto;
      max-height: none;
    }
    
    @media (max-width: 576px) {
      gap: 12px;
      width: 100% !important;
    }
  }

  .left-column {
    width: 35%;
    flex-shrink: 0;
    overflow-y: auto;
    transition: width 0.3s ease;
    
    @media (max-width: 1400px) {
      width: 30%;
    }
    
    @media (max-width: 1200px) {
      width: 40%;
    }
    
    app-card {
      flex-shrink: 0;
    }
    
    app-card:first-of-type {
      flex: 0 0 auto;
    }
    
    app-card:last-of-type {
      flex: 1;
    }
  }

  .middle-column {
    width: 65%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    transition: width 0.3s ease;
    
    @media (max-width: 1400px) {
      width: 70%;
    }
    
    @media (max-width: 1200px) {
      width: 60%;
    }
  }
  
  .chat-column {
    width: 32%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    background-color: var(--agent-chat-column-bg);
    border-left: 1px solid var(--agent-chat-column-border);
    transition: all 0.3s ease;
    box-shadow: -2px 0 10px var(--agent-chat-column-shadow);
    overflow-y: auto;

    @media (max-width: 1400px) {
      width: 35%;
    }

    @media (max-width: 1200px) {
      width: 100%;
      border-left: none;
      border-top: 1px solid var(--agent-chat-column-border);
      box-shadow: 0 -2px 10px var(--agent-chat-column-shadow);
    }
    
    .chat-column-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    
    app-card {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

.card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  &.agent-task-container {
    gap: 16px;
    padding: 16px 24px 24px;
  }
  
  @media (max-width: 576px) {
    padding: 12px;
    gap: 12px;
  }
}

// Middle and chat columns need specific height
.middle-column .card-content,
.chat-column .card-content {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Make sure chat card content takes full height
.chat-column .card-content {
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .chat-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 300px;
    height: 100%;
    overflow: hidden;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
  color: var(--text-color);
  
  @media (max-width: 576px) {
    font-size: 14px;
    margin-bottom: 6px;
  }
}

.role-goal-row {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.half-width {
  width: 50%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  @media (max-width: 576px) {
    margin-bottom: 12px;
  }
}

.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 1px solid var(--form-input-border);
  border-radius: 4px;
  width: 28px;
  height: 28px;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--dropdown-hover-bg);
    color: var(--text-color);
  }
  
  @media (max-width: 576px) {
    width: 24px;
    height: 24px;
  }
}

.fields-row {
  display: flex;
  flex-direction: row;
  gap: 20px;
  
  @media (max-width: 1200px) {
    gap: 16px;
  }
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
  
  @media (max-width: 576px) {
    gap: 12px;
  }
  
  .field-col {
    flex: 1;
  }
}

// Buttons in the middle column
.middle-column-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 16px 0 4px;
  margin-top: auto;
  margin-bottom: 0;
  flex-shrink: 0;
  
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px 0 0;
    margin-top: auto;
    margin-bottom: 0;
  }
  
  .exit-button, .save-button, .execute-button {
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    @media (max-width: 576px) {
      padding: 8px 16px;
      font-size: 13px;
    }
  }
  
  .exit-button {
    background-color: transparent;
    border: 1px solid var(--button-secondary-border);
    color: var(--button-secondary-text);
  }
  
  .save-button, .execute-button {
    background: var(--button-gradient);
    border: none;
    color: var(--button-primary-text);
    
    &:hover {
      opacity: var(--button-hover-opacity);
    }
  }
  
  .execute-button {
    background: var(--button-gradient);
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px var(--dashboard-shadow-hover);
    }
  }
}

.form-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px 24px;
  background-color: var(--modal-bg);
  border-top: 1px solid var(--card-border);
  flex-shrink: 0;
  
  @media (max-width: 576px) {
    padding: 12px 16px;
  }
  
  .button-container {
    display: flex;
    gap: 16px;
    
    @media (max-width: 576px) {
      gap: 12px;
      width: 100%;
      justify-content: space-between;
    }
  }
  
  .exit-button, .save-button {
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    @media (max-width: 576px) {
      padding: 8px 16px;
      font-size: 13px;
      flex: 1;
    }
  }
  
  .exit-button {
    background-color: transparent;
    border: 1px solid var(--form-input-border);
    color: var(--text-secondary);
    
    &:hover {
      background-color: var(--dropdown-hover-bg);
    }
  }
  
  .save-button {
    background-color: var(--dashboard-primary);
    border: none;
    color: white;
    
    &:hover {
      opacity: var(--button-hover-opacity);
    }
  }
}

// Optional sections
.optional-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 10px;
  
  @media (max-width: 1200px) {
    gap: 16px;
  }
  
  @media (max-width: 576px) {
    gap: 12px;
  }
}

// Individual accordion section
.accordion-section {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  @media (max-width: 576px) {
    margin-bottom: 12px;
  }
}

// Content area for examples inside accordions
.example-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 8px 0;
  
  @media (max-width: 576px) {
    gap: 12px;
  }
}

// Content area for steps inside accordions
.step-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 8px 0;
  
  @media (max-width: 576px) {
    gap: 12px;
  }
}

/* Agent task section styles */
.agent-task-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.input-with-actions {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.input-analyze-row {
  display: flex;
  gap: 12px;
  width: 100%;
  
  .workflow-textarea {
    flex: 1;
  }
}

.analyze-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 20px;
  height: 40px;
  border-radius: 8px;
  background: var(--button-gradient);
  color: var(--button-primary-text);
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 120px;
  
  &:hover {
    opacity: var(--button-hover-opacity);
  }
  
  svg {
    stroke: var(--button-primary-text);
    width: 16px;
    height: 16px;
  }
}

.workflow-textarea ::ng-deep .form-textarea {
  min-height: 40px !important;
  max-height: 40px !important;
  height: 40px !important;
  resize: none !important;
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  overflow-y: hidden !important;
}

// Custom styling for card component
::ng-deep app-card {
  display: flex;
  flex-direction: column;
  flex: 1;
  
  .card-container {
    background: var(--card-bg) !important;
    border: 1px solid var(--card-border) !important;
    box-shadow: 0 2px 4px var(--card-shadow) !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    background-image: none !important;
    display: flex !important;
    flex-direction: column !important;
    flex: 1 !important;
  }
}

app-card ::ng-deep .card-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}