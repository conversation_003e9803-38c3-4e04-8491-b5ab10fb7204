<div id="prompt-content-container">
  <!-- First Screen: Prompt Content Container -->
  <section id="prompt-section-container">
    <div class="container-fluid">
      <app-hero-section-header
        [headerTitle]="'Ideas to Action!'"
        [headerDescription]="
          'Explore, iterate, and evolve your ideas—all in one intelligent workspace.'
        "
      ></app-hero-section-header>

      <div class="container-fluid justify-content-center" id="card-content">
        <div class="row justify-content-center">
          <div
            class="col-12 col-md-4 col-xl-4 col-xxl-4"
            *ngFor="let card of cardData"
          >            <awe-cards
              variant="feature"
              size="medium"
              [title]="card.frontTitle"
              [image]="card.frontImage"
              theme="light"
              [hasFooter]="false"
              (click)="navigateToFeature(card.redirectPath)"
            >
              <div content>
                <h4>{{ card.frontDescription }}</h4>
              </div>
            </awe-cards>
          </div>
        </div>
      </div>
    </div>
  </section>
  
<section id="projects-section-container" class="py-5">
  <app-recent-projects></app-recent-projects>
</section>
</div>

