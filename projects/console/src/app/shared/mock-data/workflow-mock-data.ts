import { CardData } from '../models/card.model';

export const MOCK_WORKFLOWS: CardData[] = [
  {
    id: '1',
    title: 'Product Recommendation Engine',
    tags: [
      { label: 'E-commerce' },
      { label: 'Recommendation' },
      { label: 'Customer Experience' }
    ],
    createdDate: '2/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'business',
    client: 'retail',
    department: 'sales',
    role: 'analyst',
    project: 'customer-engagement',
    category: 'recommendation'
  },
  {
    id: '2',
    title: 'Customer Support Ticket Triage',
    tags: [
      { label: 'Support' },
      { label: 'Classification' },
      { label: 'Automation' }
    ],
    createdDate: '1/28/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'business',
    client: 'services',
    department: 'customer-support',
    role: 'manager',
    project: 'support-efficiency',
    category: 'automation'
  },
  {
    id: '3',
    title: 'Financial Document Analysis',
    tags: [
      { label: 'Finance' },
      { label: 'Document Processing' },
      { label: 'Data Extraction' }
    ],
    createdDate: '3/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'financial',
    department: 'accounting',
    role: 'analyst',
    project: 'document-automation',
    category: 'analysis'
  },
  {
    id: '4',
    title: 'Legal Contract Review',
    tags: [
      { label: 'Legal' },
      { label: 'Contract Analysis' },
      { label: 'Risk Assessment' }
    ],
    createdDate: '2/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'legal',
    department: 'compliance',
    role: 'legal-advisor',
    project: 'contract-review',
    category: 'analysis'
  },
  {
    id: '5',
    title: 'Social Media Content Generator',
    tags: [
      { label: 'Marketing' },
      { label: 'Content Creation' },
      { label: 'Social Media' }
    ],
    createdDate: '1/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'business',
    client: 'marketing',
    department: 'digital-marketing',
    role: 'content-creator',
    project: 'social-engagement',
    category: 'generation'
  },
  {
    id: '6',
    title: 'Healthcare Patient Data Analysis',
    tags: [
      { label: 'Healthcare' },
      { label: 'Data Analysis' },
      { label: 'Patient Care' }
    ],
    createdDate: '3/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'healthcare',
    department: 'clinical',
    role: 'medical-professional',
    project: 'patient-insights',
    category: 'analysis'
  },
  {
    id: '7',
    title: 'HR Resume Screening',
    tags: [
      { label: 'HR' },
      { label: 'Recruitment' },
      { label: 'Candidate Selection' }
    ],
    createdDate: '2/8/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'business',
    client: 'hr',
    department: 'recruitment',
    role: 'recruiter',
    project: 'hiring-process',
    category: 'automation'
  },
  {
    id: '8',
    title: 'Supply Chain Optimization',
    tags: [
      { label: 'Logistics' },
      { label: 'Optimization' },
      { label: 'Supply Chain' }
    ],
    createdDate: '1/30/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'manufacturing',
    department: 'operations',
    role: 'supply-chain-manager',
    project: 'efficiency-optimization',
    category: 'optimization'
  },
  {
    id: '9',
    title: 'Real Estate Property Valuation',
    tags: [
      { label: 'Real Estate' },
      { label: 'Valuation' },
      { label: 'Market Analysis' }
    ],
    createdDate: '3/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'business',
    client: 'real-estate',
    department: 'appraisal',
    role: 'property-evaluator',
    project: 'market-valuation',
    category: 'analysis'
  },
  {
    id: '10',
    title: 'Educational Content Personalization',
    tags: [
      { label: 'Education' },
      { label: 'Personalization' },
      { label: 'Learning' }
    ],
    createdDate: '2/12/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'education',
    client: 'educational-institutions',
    department: 'curriculum',
    role: 'educator',
    project: 'personalized-learning',
    category: 'personalization'
  },
  {
    id: '11',
    title: 'Customer Churn Prediction',
    tags: [
      { label: 'CRM' },
      { label: 'Prediction' },
      { label: 'Customer Retention' }
    ],
    createdDate: '1/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'business',
    client: 'services',
    department: 'customer-success',
    role: 'retention-specialist',
    project: 'customer-loyalty',
    category: 'prediction'
  },
  {
    id: '12',
    title: 'Manufacturing Quality Control',
    tags: [
      { label: 'Manufacturing' },
      { label: 'Quality Assurance' },
      { label: 'Defect Detection' }
    ],
    createdDate: '3/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'manufacturing',
    department: 'quality-control',
    role: 'qa-manager',
    project: 'quality-improvement',
    category: 'automation'
  },
  {
    id: '13',
    title: 'Research Paper Summarization',
    tags: [
      { label: 'Academic' },
      { label: 'Research' },
      { label: 'Summarization' }
    ],
    createdDate: '2/25/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'education',
    client: 'research-institutions',
    department: 'academia',
    role: 'researcher',
    project: 'knowledge-management',
    category: 'summarization'
  },
  {
    id: '14',
    title: 'Fraud Detection System',
    tags: [
      { label: 'Security' },
      { label: 'Finance' },
      { label: 'Fraud Prevention' }
    ],
    createdDate: '1/25/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'financial',
    department: 'security',
    role: 'risk-analyst',
    project: 'fraud-prevention',
    category: 'detection'
  },
  {
    id: '15',
    title: 'Product Description Generator',
    tags: [
      { label: 'E-commerce' },
      { label: 'Content Creation' },
      { label: 'Marketing' }
    ],
    createdDate: '3/8/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'business',
    client: 'retail',
    department: 'product-management',
    role: 'content-manager',
    project: 'product-catalog',
    category: 'generation'
  },
  {
    id: '16',
    title: 'Customer Sentiment Analysis',
    tags: [
      { label: 'Customer Experience' },
      { label: 'Sentiment Analysis' },
      { label: 'Feedback' }
    ],
    createdDate: '2/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'business',
    client: 'customer-service',
    department: 'customer-insights',
    role: 'cx-analyst',
    project: 'voice-of-customer',
    category: 'analysis'
  },
  {
    id: '17',
    title: 'Insurance Claims Processing',
    tags: [
      { label: 'Insurance' },
      { label: 'Claims' },
      { label: 'Process Automation' }
    ],
    createdDate: '1/18/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'insurance',
    department: 'claims',
    role: 'claims-processor',
    project: 'claims-automation',
    category: 'automation'
  },
  {
    id: '18',
    title: 'Market Trend Analysis',
    tags: [
      { label: 'Market Research' },
      { label: 'Trend Analysis' },
      { label: 'Business Intelligence' }
    ],
    createdDate: '3/1/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'business',
    client: 'market-research',
    department: 'business-intelligence',
    role: 'market-analyst',
    project: 'trend-forecasting',
    category: 'analysis'
  },
  {
    id: '19',
    title: 'Automated Email Response',
    tags: [
      { label: 'Communication' },
      { label: 'Email Automation' },
      { label: 'Customer Service' }
    ],
    createdDate: '2/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'business',
    client: 'services',
    department: 'customer-support',
    role: 'support-specialist',
    project: 'communication-automation',
    category: 'automation'
  },
  {
    id: '20',
    title: 'Energy Consumption Optimization',
    tags: [
      { label: 'Energy' },
      { label: 'Optimization' },
      { label: 'Sustainability' }
    ],
    createdDate: '1/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
    
      { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'utilities',
    department: 'energy-management',
    role: 'energy-analyst',
    project: 'efficiency-improvement',
    category: 'optimization'
  }
]; 