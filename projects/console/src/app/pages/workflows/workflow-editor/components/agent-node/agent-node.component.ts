import { Component, Input, Output, EventEmitter, OnInit, ElementRef, ViewChild, AfterViewInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { WorkflowNode } from '../../services/workflow-graph.service';
import { DragDropModule, CdkDragEnd, CdkDrag } from '@angular/cdk/drag-drop';
import { Subject, fromEvent } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-agent-node',
  standalone: true,
  imports: [CommonModule, DragDropModule],
  template: `
    <div 
      #nodeElement
      class="agent-node" 
      [class.selected]="selected"
      [attr.data-node-id]="node.id"
      [style.width.px]="nodeWidth"
      cdkDrag
      cdkDragBoundary=".react-flow-container"
      [cdkDragFreeDragPosition]="{x: node.position.x, y: node.position.y}"
      (cdkDragEnded)="onDragEnded($event)"
      (mousedown)="onNodeMouseDown($event)"
    >
      <!-- Drag handle for the entire node -->
      <div class="drag-handle" cdkDragHandle>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="8" cy="8" r="2"></circle>
          <circle cx="16" cy="8" r="2"></circle>
          <circle cx="8" cy="16" r="2"></circle>
          <circle cx="16" cy="16" r="2"></circle>
        </svg>
      </div>
      
      <!-- Close button positioned at top right -->
      <button class="delete-btn" (click)="onDelete($event)" aria-label="Delete node">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
      
      <!-- Node Header with Toggle (also a drag handle) -->
      <div class="node-header" cdkDragHandle>
        <span class="node-title">{{ node.data['label'] }}</span>
        <button class="toggle-btn" (click)="toggleExpanded($event)" aria-label="Toggle details">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="16" 
            height="16" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            stroke-width="2" 
            stroke-linecap="round" 
            stroke-linejoin="round"
            [class.expanded]="isExpanded"
            aria-hidden="true"
          >
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </button>
      </div>
      
      <!-- Expanded Details -->
      <div class="node-details" *ngIf="isExpanded">
        <!-- Prompt -->
        <div class="detail-item">
          <div class="detail-label">Prompt</div>
          <div class="detail-value">
            <div class="detail-card">
              <span class="detail-text">{{ node.data['description'] || 'No Data' }}</span>
              <button class="copy-btn" aria-label="Copy prompt">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        <!-- Model -->
        <div class="detail-item">
          <div class="detail-label">Model</div>
          <div class="detail-value">
            <div class="detail-card">
              <div class="model-info">
                <div class="model-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                  </svg>
                </div>
                <span class="model-name">GPT 3.5</span>
              </div>
              <button class="copy-btn" aria-label="Copy model info">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        <!-- Knowledge Base -->
        <div class="detail-item">
          <div class="detail-label">Knowledge base</div>
          <div class="detail-value">
            <div class="empty-detail">No Data</div>
          </div>
        </div>
        
        <!-- Tools -->
        <div class="detail-item">
          <div class="detail-label">Tools</div>
          <div class="detail-value">
            <div class="empty-detail">No Data</div>
          </div>
        </div>
        
        <!-- Guardrails -->
        <div class="detail-item">
          <div class="detail-label">Guardrails</div>
          <div class="detail-value">
            <div class="empty-detail">No Data</div>
          </div>
        </div>
      </div>
      
      <div class="node-handles">
        <div 
          class="handle handle-left" 
          [attr.data-handle-id]="node.id + '-left'"
          (mousedown)="onHandleMouseDown($event, 'target')"
          role="button"
          aria-label="Connect from left"
        ></div>
        <div 
          class="handle handle-right" 
          [attr.data-handle-id]="node.id + '-right'"
          (mousedown)="onHandleMouseDown($event, 'source')"
          role="button"
          aria-label="Connect from right"
        ></div>
      </div>
      
      <!-- Resize handles -->
      <div class="resize-handle resize-right" 
           (mousedown)="onResizeStart($event, 'right')"
           role="button"
           aria-label="Resize right">
        <div class="resize-handle-inner"></div>
      </div>
      <div class="resize-handle resize-left"
           (mousedown)="onResizeStart($event, 'left')"
           role="button"
           aria-label="Resize left">
        <div class="resize-handle-inner"></div>
      </div>
    </div>
  `,
  styles: [`
    .agent-node {
      min-width: 150px;
      max-width: 600px;
      background-color: var(--agent-node-bg);
      border-radius: 8px;
      border: 2px solid var(--agent-card-border);
      overflow: visible;
      box-shadow: 0 2px 5px var(--card-shadow);
      position: absolute;
      user-select: none;
      cursor: move;
      touch-action: none;
      z-index: 10;
      transition: border-color 0.15s ease, box-shadow 0.15s ease;
    }
    
    .agent-node.selected {
      border-color: var(--dashboard-primary);
      box-shadow: 0 4px 10px var(--dashboard-shadow-hover);
      z-index: 20;
    }
    
    .drag-handle {
      position: absolute;
      top: -12px;
      left: 50%;
      transform: translateX(-50%);
      width: 26px;
      height: 26px;
      background-color: var(--dashboard-primary);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--button-primary-text);
      cursor: grab;
      z-index: 25;
      opacity: 0;
      transition: opacity 0.2s ease;
    }
    
    .agent-node:hover .drag-handle {
      opacity: 1;
    }
    
    .agent-node.selected .drag-handle {
      opacity: 1;
    }
    
    .delete-btn {
      position: absolute;
      top: -10px;
      right: -10px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--modal-header-border);
      border: none;
      border-radius: 50%;
      padding: 0;
      color: var(--button-primary-text);
      cursor: pointer;
      z-index: 999;
      transition: all 0.2s ease;
      display: none;
      transform: none;
    }
    
    .agent-node.selected .delete-btn {
      display: flex;
    }
    
    .delete-btn:hover {
      background-color: var(--delete-icon-color);
    }
    
    .node-header {
      background-color: var(--agent-table-header-bg);
      padding: 12px 10px 12px 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--agent-card-border);
      cursor: move;
    }
    
    .node-title {
      font-weight: 500;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex-grow: 1;
      padding-right: 30px;
      color: var(--text-color);
    }
    
    .toggle-btn {
      position: absolute;
      right: 10px;
      top: 10px;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--form-input-bg);
      border: 1px solid var(--form-input-border);
      border-radius: 50%;
      padding: 0;
      color: var(--text-secondary);
      cursor: pointer;
      z-index: 20;
      transition: all 0.2s ease;
    }
    
    .toggle-btn:hover {
      color: var(--dashboard-primary);
      background-color: var(--card-bg);
      border-color: var(--dashboard-primary);
      box-shadow: 0 0 0 2px var(--dashboard-shadow-hover);
    }
    
    .toggle-btn svg {
      transition: transform 0.2s ease;
    }
    
    .toggle-btn svg.expanded {
      transform: rotate(180deg);
    }
    
    .node-details {
      padding: 14px;
      display: flex;
      flex-direction: column;
      gap: 14px;
    }
    
    .detail-item {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
    
    .detail-label {
      font-size: 12px;
      font-weight: 500;
      color: var(--form-label-color);
    }
    
    .detail-value {
      width: 100%;
    }
    
    .detail-card {
      background-color: var(--form-input-bg);
      border-radius: 6px;
      padding: 8px 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .detail-text {
      font-size: 12px;
      color: var(--form-input-color);
      flex-grow: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .model-info {
      display: flex;
      align-items: center;
      gap: 6px;
    }
    
    .model-icon {
      width: 20px;
      height: 20px;
      background-color: var(--dashboard-primary);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--button-primary-text);
    }
    
    .model-name {
      font-size: 12px;
      color: var(--form-input-color);
    }
    
    .copy-btn {
      background: none;
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      padding: 2px;
    }
    
    .copy-btn:hover {
      color: var(--dashboard-primary);
    }
    
    .empty-detail {
      padding: 8px 10px;
      background-color: var(--agent-table-header-bg);
      border-radius: 6px;
      color: var(--text-secondary);
      font-size: 12px;
    }
    
    .node-handles {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }
    
    .handle {
      position: absolute;
      width: 14px;
      height: 14px;
      background-color: var(--dashboard-primary);
      border: 2px solid var(--card-bg);
      border-radius: 50%;
      pointer-events: all;
      cursor: crosshair;
      z-index: 15;
    }
    
    .handle-left {
      top: 50%;
      left: -7px;
      transform: translateY(-50%);
    }
    
    .handle-right {
      top: 50%;
      right: -7px;
      transform: translateY(-50%);
    }
    
    .resize-handle {
      position: absolute;
      width: 10px;
      height: 100%;
      top: 0;
      cursor: ew-resize;
      z-index: 20;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.2s ease;
    }
    
    .agent-node:hover .resize-handle,
    .agent-node.selected .resize-handle {
      opacity: 1;
    }
    
    .resize-handle-inner {
      width: 4px;
      height: 20px;
      border-radius: 2px;
      background-color: var(--dashboard-primary);
    }
    
    .resize-left {
      left: 0;
    }
    
    .resize-right {
      right: 0;
    }

    .cdk-drag-placeholder {
      opacity: 0.3;
      background-color: var(--agent-tools-empty-bg);
      border: 2px dashed var(--dashboard-primary);
    }

    .cdk-drag-animating {
      transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }
    
    .cdk-drag-preview {
      box-shadow: 0 5px 15px var(--card-hover-shadow);
      border-color: var(--dashboard-primary);
    }
  `]
})
export class AgentNodeComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('nodeElement') nodeElement!: ElementRef;
  @Input() node!: WorkflowNode;
  @Input() selected: boolean = false;
  @Output() deleteNode = new EventEmitter<string>();
  @Output() moveNode = new EventEmitter<{nodeId: string, position: {x: number, y: number}}>();
  @Output() nodeSelected = new EventEmitter<string>();
  @Output() startConnection = new EventEmitter<{nodeId: string, handleType: 'source' | 'target', event: MouseEvent}>();
  @Output() resizeNode = new EventEmitter<{nodeId: string, width: number}>();
  @Output() nodePositionChanged = new EventEmitter<{nodeId: string, position: {x: number, y: number}}>();
  
  // Whether the node details are expanded
  isExpanded: boolean = false;
  
  // Track if we're dragging to avoid toggles during drag operations
  private isDragging: boolean = false;
  
  // Node width for resizing (with fallback to default)
  nodeWidth: number = 200;
  
  // Resize state tracking
  private isResizing: boolean = false;
  private resizeStartX: number = 0;
  private resizeStartWidth: number = 0;
  private resizeDirection: 'left' | 'right' = 'right';
  private resizeStartPosition: number = 0;
  
  // For cleanup
  private destroy$ = new Subject<void>();

  constructor(private cdr: ChangeDetectorRef) {}
  
  ngOnInit(): void {
    // Set initial width from node if available
    if (this.node?.data?.width) {
      this.nodeWidth = this.node.data.width;
    }
    
    // Emit initial position for connection points setup
    setTimeout(() => {
      this.nodePositionChanged.emit({
        nodeId: this.node.id,
        position: this.node.position
      });
    }, 0);
  }
  
  ngAfterViewInit(): void {
    // No additional initialization needed
  }
  
  ngOnDestroy(): void {
    // Cleanup subscriptions
    this.destroy$.next();
    this.destroy$.complete();
  }
  
  toggleExpanded(event: MouseEvent): void {
    event.stopPropagation();
    this.isExpanded = !this.isExpanded;
  }
  
  onNodeMouseDown(event: MouseEvent): void {
    // Start tracking potential drag
    this.isDragging = true;
    
    // Don't select when clicking on handle or interactive elements
    const targetElement = event.target as HTMLElement;
    if (
      targetElement.classList.contains('handle') || 
      targetElement.classList.contains('delete-btn') ||
      targetElement.closest('.delete-btn') ||
      targetElement.classList.contains('toggle-btn') ||
      targetElement.closest('.toggle-btn') ||
      targetElement.classList.contains('copy-btn') ||
      targetElement.closest('.copy-btn')
    ) {
      return;
    }
    
    // Select the node
    this.nodeSelected.emit(this.node.id);
  }
  
  onHandleMouseDown(event: MouseEvent, handleType: 'source' | 'target'): void {
    // Prevent parent node drag
    event.stopPropagation();
    
    // Select this node
    this.nodeSelected.emit(this.node.id);
    
    // Make sure position is up to date before starting connection
    this.nodePositionChanged.emit({
      nodeId: this.node.id,
      position: this.node.position
    });
    
    // Emit connection start event
    this.startConnection.emit({
      nodeId: this.node.id,
      handleType,
      event
    });
  }
  
  onDragEnded(event: CdkDragEnd): void {
    // End of dragging
    this.isDragging = false;
    
    // Calculate the final position after drag
    const transform = event.source.getFreeDragPosition();
    
    // Create a safe position (not negative)
    const safePosition = { 
      x: Math.max(0, transform.x), 
      y: Math.max(0, transform.y) 
    };
    
    // Emit final position to parent
    this.moveNode.emit({
      nodeId: this.node.id,
      position: safePosition
    });
    
    // Also emit position changed for connection points
    this.nodePositionChanged.emit({
      nodeId: this.node.id,
      position: safePosition
    });
  }
  
  onDelete(event: MouseEvent): void {
    event.stopPropagation();
    this.deleteNode.emit(this.node.id);
  }
  
  onResizeStart(event: MouseEvent, direction: 'left' | 'right'): void {
    // Prevent node drag
    event.stopPropagation();
    event.preventDefault();
    
    // Store initial resize state
    this.isResizing = true;
    this.resizeStartX = event.clientX;
    this.resizeStartWidth = this.nodeWidth;
    this.resizeDirection = direction;
    this.resizeStartPosition = this.node.position.x;
    
    // Select this node
    this.nodeSelected.emit(this.node.id);
    
    // Add document listeners for resize
    const mouseMoveHandler = fromEvent<MouseEvent>(document, 'mousemove')
      .pipe(takeUntil(this.destroy$))
      .subscribe(this.onResizeMove.bind(this));
    
    const mouseUpHandler = fromEvent<MouseEvent>(document, 'mouseup')
      .pipe(takeUntil(this.destroy$))
      .subscribe(this.onResizeEnd.bind(this));
    
    // Cleanup handlers after resize ends
    mouseUpHandler.add(() => {
      mouseMoveHandler.unsubscribe();
      mouseUpHandler.unsubscribe();
    });
  }
  
  onResizeMove(event: MouseEvent): void {
    if (!this.isResizing) return;
    
    // Calculate width difference
    const deltaX = event.clientX - this.resizeStartX;
    
    // Apply width change based on direction
    if (this.resizeDirection === 'right') {
      // Right handle resize - just change width
      const newWidth = Math.max(150, this.resizeStartWidth + deltaX);
      this.nodeWidth = newWidth;
    } else {
      // Left handle resize - change width and position
      const deltaWidth = this.resizeStartX - event.clientX;
      const newWidth = Math.max(150, this.resizeStartWidth + deltaWidth);
      
      // Only update position if width is changing
      if (newWidth !== this.nodeWidth) {
        const positionDelta = this.resizeStartWidth - newWidth;
        
        // Emit position update
        this.moveNode.emit({
          nodeId: this.node.id,
          position: {
            x: this.resizeStartPosition + positionDelta,
            y: this.node.position.y
          }
        });
        
        this.nodeWidth = newWidth;
      }
    }
    
    // Force change detection during resize
    this.cdr.detectChanges();
  }
  
  onResizeEnd(event: MouseEvent): void {
    if (!this.isResizing) return;
    
    // Finalize resize
    this.isResizing = false;
    
    // Emit resize event to parent
    this.resizeNode.emit({
      nodeId: this.node.id,
      width: this.nodeWidth
    });
    
    // Also emit position changed for connection points update
    this.nodePositionChanged.emit({
      nodeId: this.node.id,
      position: this.node.position
    });
  }
} 