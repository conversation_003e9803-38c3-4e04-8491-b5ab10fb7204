.dropdown-item {
  display: flex;
  // align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
  background: transparent;
  border: none;
  width: 100%;
  text-align: left;
  outline: none;
  margin: 2px 0;
  -webkit-appearance: none;
  appearance: none;

  &:hover {
    background-color: var(--dropdown-hover-bg);
    
    .dropdown-item-title {
      color: var(--dropdown-item-hover-text, var(--dropdown-text));
    }
    
    .dropdown-item-desc {
      color: var(--dropdown-item-desc-hover, var(--text-secondary));
    }
    
    .dropdown-icon {
      filter: brightness(1.1);
    }
  }
  
  &.active {
    background-color: var(--dropdown-selected-bg);
    
    .dropdown-item-title {
      color: var(--dropdown-selected-text);
    }
    
    .dropdown-item-desc {
      color: var(--dropdown-selected-text);
    }
  }
  
  &:first-child {
    margin-top: 4px;
  }
  
  &:last-child {
    margin-bottom: 4px;
  }
}

.dropdown-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--nav-item-color);
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  pointer-events: none;
  
  &.active {
    color: var(--nav-item-active-color);
  }
}

.dropdown-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  pointer-events: none;
  transition: filter 0.2s ease;
  
  &.active {
    filter: brightness(0.9);
  }
}

.dropdown-item-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  pointer-events: none;
}

.dropdown-item-title {
  font-weight: 500;
  font-size: 16px;
  color: var(--dropdown-text);
  transition: color 0.2s ease;
  pointer-events: none;
  
  &.active {
    color: var(--dropdown-selected-text);
    font-weight: 600;
  }
}

.dropdown-item-desc {
  font-size: 14px;
  color: var(--text-secondary);
  transition: color 0.2s ease;
  pointer-events: none;
  
  &.active {
    color: var(--dropdown-selected-text);
  }
} 