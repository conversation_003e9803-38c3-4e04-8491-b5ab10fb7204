import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TypewriterService {
  private placeholderSubject = new BehaviorSubject<string>('');
  public placeholder$: Observable<string> = this.placeholderSubject.asObservable();

  private texts: string[] = [];
  private staticText: string = '';
  private currentTextIndex = 0;
  private currentCharIndex = 0;
  private isTyping = true;
  private animationTimeout: any;
  private pauseTimeout: any;

  constructor() {}

  /** Start typewriter animation */
  startTypewriter(
    texts: string[],
    staticText: string = '',
    typingSpeed: number = 40,
    erasingSpeed: number = 30,
    pauseBeforeErasing: number = 400,
    pauseBeforeTyping: number = 200
  ): void {
    if (!texts || texts.length === 0) {
      return;
    }

    this.texts = this.shuffleArray([...texts]);
    this.staticText = staticText;
    this.currentTextIndex = 0;
    this.currentCharIndex = 0;
    this.isTyping = true;
    this.stopTypewriter();
    this.typeEffect(typingSpeed, erasingSpeed, pauseBeforeErasing, pauseBeforeTyping);
  }

  /** Fisher-Yates shuffle */
  private shuffleArray<T>(array: T[]): T[] {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }

  /** Stop animation */
  stopTypewriter(): void {
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout);
      this.animationTimeout = null;
    }

    if (this.pauseTimeout) {
      clearTimeout(this.pauseTimeout);
      this.pauseTimeout = null;
    }
  }

  /** Core animation logic */
  private typeEffect(
    typingSpeed: number,
    erasingSpeed: number,
    pauseBeforeErasing: number,
    pauseBeforeTyping: number
  ): void {
    const currentText = this.texts[this.currentTextIndex];

    if (this.isTyping) {
      this.currentCharIndex++;
      const animatedPart = currentText.slice(0, this.currentCharIndex);
      this.placeholderSubject.next(this.staticText ? `${this.staticText} ${animatedPart}` : animatedPart);

      if (this.currentCharIndex >= currentText.length) {
        this.isTyping = false;
        this.pauseTimeout = setTimeout(() => {
          this.typeEffect(typingSpeed, erasingSpeed, pauseBeforeErasing, pauseBeforeTyping);
        }, pauseBeforeErasing);
        return;
      }
    } else {
      this.currentCharIndex--;
      const animatedPart = currentText.slice(0, this.currentCharIndex);
      this.placeholderSubject.next(this.staticText ? `${this.staticText} ${animatedPart}` : animatedPart);

      if (this.currentCharIndex <= 0) {
        this.isTyping = true;
        this.currentTextIndex = (this.currentTextIndex + 1) % this.texts.length;
        this.pauseTimeout = setTimeout(() => {
          this.typeEffect(typingSpeed, erasingSpeed, pauseBeforeErasing, pauseBeforeTyping);
        }, pauseBeforeTyping);
        return;
      }
    }

    this.animationTimeout = setTimeout(
      () => this.typeEffect(typingSpeed, erasingSpeed, pauseBeforeErasing, pauseBeforeTyping),
      this.isTyping ? typingSpeed : erasingSpeed
    );
  }
}
