import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

// Canvas viewport interface
export interface CanvasViewport {
  zoom: number;
  x: number;
  y: number;
  isDragging: boolean;
  lastMouseX: number;
  lastMouseY: number;
}

// Canvas bounds interface
export interface CanvasBounds {
  minZoom: number;
  maxZoom: number;
  panBounds: number;
}

// Canvas interaction state
export interface CanvasInteraction {
  isSelecting: boolean;
  selectionStart: { x: number; y: number } | null;
  selectionEnd: { x: number; y: number } | null;
  isDraggingSelection: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CanvasService {
  // Default canvas configuration
  private readonly DEFAULT_VIEWPORT: CanvasViewport = {
    zoom: 1,
    x: 0,
    y: 0,
    isDragging: false,
    lastMouseX: 0,
    lastMouseY: 0
  };

  private readonly DEFAULT_BOUNDS: CanvasBounds = {
    minZoom: 0.1,
    maxZoom: 3.0,
    panBounds: 10000
  };

  private readonly DEFAULT_INTERACTION: CanvasInteraction = {
    isSelecting: false,
    selectionStart: null,
    selectionEnd: null,
    isDraggingSelection: false
  };

  // State subjects
  private viewportSubject = new BehaviorSubject<CanvasViewport>(this.DEFAULT_VIEWPORT);
  private boundsSubject = new BehaviorSubject<CanvasBounds>(this.DEFAULT_BOUNDS);
  private interactionSubject = new BehaviorSubject<CanvasInteraction>(this.DEFAULT_INTERACTION);

  // Public observables
  viewport$: Observable<CanvasViewport> = this.viewportSubject.asObservable();
  bounds$: Observable<CanvasBounds> = this.boundsSubject.asObservable();
  interaction$: Observable<CanvasInteraction> = this.interactionSubject.asObservable();

  constructor() {}

  /**
   * Get current viewport state
   */
  getViewport(): CanvasViewport {
    return this.viewportSubject.getValue();
  }

  /**
   * Update viewport state
   */
  updateViewport(viewport: Partial<CanvasViewport>): void {
    const current = this.viewportSubject.getValue();
    const bounds = this.boundsSubject.getValue();
    
    // Apply bounds constraints
    const newViewport: CanvasViewport = {
      ...current,
      ...viewport,
      zoom: Math.max(bounds.minZoom, Math.min(bounds.maxZoom, viewport.zoom ?? current.zoom)),
      x: Math.max(-bounds.panBounds, Math.min(bounds.panBounds, viewport.x ?? current.x)),
      y: Math.max(-bounds.panBounds, Math.min(bounds.panBounds, viewport.y ?? current.y))
    };

    this.viewportSubject.next(newViewport);
  }

  /**
   * Reset viewport to default state
   */
  resetViewport(): void {
    this.viewportSubject.next({ ...this.DEFAULT_VIEWPORT });
  }

  /**
   * Zoom to specific level with optional center point
   */
  zoomToLevel(zoomLevel: number, centerPoint?: { x: number; y: number }): void {
    const current = this.getViewport();
    const bounds = this.boundsSubject.getValue();
    
    const clampedZoom = Math.max(bounds.minZoom, Math.min(bounds.maxZoom, zoomLevel));
    
    if (centerPoint) {
      // Zoom to specific point
      const zoomRatio = clampedZoom / current.zoom;
      const newX = centerPoint.x - (centerPoint.x - current.x) * zoomRatio;
      const newY = centerPoint.y - (centerPoint.y - current.y) * zoomRatio;

      this.updateViewport({
        zoom: clampedZoom,
        x: newX,
        y: newY
      });
    } else {
      // Zoom to center
      this.updateViewport({
        zoom: clampedZoom
      });
    }
  }

  /**
   * Pan canvas by delta amounts
   */
  panCanvas(deltaX: number, deltaY: number): void {
    const current = this.getViewport();
    this.updateViewport({
      x: current.x + deltaX,
      y: current.y + deltaY
    });
  }

  /**
   * Start canvas dragging
   */
  startDragging(mouseX: number, mouseY: number): void {
    this.updateViewport({
      isDragging: true,
      lastMouseX: mouseX,
      lastMouseY: mouseY
    });
  }

  /**
   * Stop canvas dragging
   */
  stopDragging(): void {
    this.updateViewport({
      isDragging: false
    });
  }

  /**
   * Update interaction state
   */
  updateInteraction(interaction: Partial<CanvasInteraction>): void {
    const current = this.interactionSubject.getValue();
    this.interactionSubject.next({
      ...current,
      ...interaction
    });
  }

  /**
   * Convert screen coordinates to canvas coordinates
   */
  screenToCanvasPosition(screenX: number, screenY: number, containerRect: DOMRect): { x: number; y: number } {
    const viewport = this.getViewport();
    const canvasX = (screenX - containerRect.left - viewport.x) / viewport.zoom;
    const canvasY = (screenY - containerRect.top - viewport.y) / viewport.zoom;
    return { x: canvasX, y: canvasY };
  }

  /**
   * Convert canvas coordinates to screen coordinates
   */
  canvasToScreenPosition(canvasX: number, canvasY: number): { x: number; y: number } {
    const viewport = this.getViewport();
    const screenX = canvasX * viewport.zoom + viewport.x;
    const screenY = canvasY * viewport.zoom + viewport.y;
    return { x: screenX, y: screenY };
  }

  /**
   * Get transform style string for CSS
   */
  getTransformStyle(): string {
    const viewport = this.getViewport();
    return `translate(${viewport.x}px, ${viewport.y}px) scale(${viewport.zoom})`;
  }

  /**
   * Calculate zoom percentage
   */
  getZoomPercentage(): number {
    return Math.round(this.getViewport().zoom * 100);
  }

  /**
   * Fit content to view
   */
  fitToView(contentBounds: { minX: number; minY: number; maxX: number; maxY: number }, containerSize: { width: number; height: number }): void {
    const contentWidth = contentBounds.maxX - contentBounds.minX;
    const contentHeight = contentBounds.maxY - contentBounds.minY;
    
    if (contentWidth === 0 || contentHeight === 0) return;
    
    const padding = 100; // Padding around content
    const scaleX = (containerSize.width - padding * 2) / contentWidth;
    const scaleY = (containerSize.height - padding * 2) / contentHeight;
    const scale = Math.min(scaleX, scaleY, this.boundsSubject.getValue().maxZoom);
    
    // Center the content
    const centerX = (contentBounds.minX + contentBounds.maxX) / 2;
    const centerY = (contentBounds.minY + contentBounds.maxY) / 2;
    
    const translateX = containerSize.width / 2 - centerX * scale;
    const translateY = containerSize.height / 2 - centerY * scale;
    
    this.updateViewport({
      zoom: scale,
      x: translateX,
      y: translateY
    });
  }

  /**
   * Update canvas bounds configuration
   */
  updateBounds(bounds: Partial<CanvasBounds>): void {
    const current = this.boundsSubject.getValue();
    this.boundsSubject.next({
      ...current,
      ...bounds
    });
  }
}
