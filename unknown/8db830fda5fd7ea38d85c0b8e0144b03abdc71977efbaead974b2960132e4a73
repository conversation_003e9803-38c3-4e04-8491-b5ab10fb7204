<app-card [fixedHeight]="true" customClass="data-card" (click)="onCardClick()">
  <div class="data-card-content">
    <!-- Card title - Row 1 -->
    <div class="card-header">
      <h3 class="card-title">{{ data.title }}</h3>
    </div>

    <!-- Tags section - Row 2 -->
    <div class="tags-section">
      <div class="tags-container">
        <div class="tag" *ngFor="let tag of data.tags">{{ tag.label }}</div>
      </div>
    </div>

    <!-- Footer section with date and actions - Row 3 -->
    <div class="card-footer">
      <div class="created-date">Created on {{ data.createdDate }}</div>

      <div class="action-buttons">
        <button
          *ngFor="let action of data.actions"
          class="action-button"
          [title]="action.tooltip || ''"
          [attr.aria-label]="action.tooltip || action.action"
          (click)="onActionClick($event, action)"
        >
          <!-- Use a simple icon based on action type -->
          <ng-container [ngSwitch]="action.action">
            <span *ngSwitchCase="'execute'" class="action-icon view-icon">
              <svg
                width="17"
                height="17"
                viewBox="0 0 17 17"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M4.96925 8.5L6.61725 6.852C6.76592 6.70317 6.84192 6.52908 6.84525 6.32975C6.84842 6.13042 6.77242 5.95317 6.61725 5.798C6.46208 5.643 6.28392 5.5655 6.08275 5.5655C5.88142 5.5655 5.70317 5.643 5.548 5.798L3.47875 7.86725C3.38525 7.96092 3.31925 8.05967 3.28075 8.1635C3.24225 8.26733 3.223 8.3795 3.223 8.5C3.223 8.6205 3.24225 8.73267 3.28075 8.8365C3.31925 8.94033 3.38525 9.03908 3.47875 9.13275L5.55775 11.2115C5.70642 11.3602 5.883 11.4362 6.0875 11.4395C6.292 11.4427 6.47183 11.3667 6.627 11.2115C6.782 11.0563 6.8595 10.8807 6.8595 10.6845C6.8595 10.4885 6.782 10.3129 6.627 10.1578L4.96925 8.5ZM12.0307 8.5L10.373 10.1578C10.2243 10.3064 10.1484 10.4804 10.1453 10.6798C10.1421 10.8791 10.218 11.0563 10.373 11.2115C10.5282 11.3667 10.7064 11.4443 10.9078 11.4443C11.1089 11.4443 11.2871 11.3667 11.4423 11.2115L13.5212 9.13275C13.6147 9.03908 13.6808 8.94033 13.7193 8.8365C13.7578 8.73267 13.777 8.6205 13.777 8.5C13.777 8.3795 13.7578 8.26733 13.7193 8.1635C13.6808 8.05967 13.6147 7.96092 13.5212 7.86725L11.4423 5.7885C11.3679 5.71417 11.2843 5.65842 11.1913 5.62125C11.0984 5.58408 11.0055 5.5655 10.9125 5.5655C10.8195 5.5655 10.7249 5.58408 10.6288 5.62125C10.5328 5.65842 10.4475 5.71417 10.373 5.7885C10.218 5.94367 10.1405 6.11933 10.1405 6.3155C10.1405 6.5115 10.218 6.68708 10.373 6.84225L12.0307 8.5ZM1.80775 17C1.30258 17 0.875 16.825 0.525 16.475C0.175 16.125 0 15.6974 0 15.1923V1.80775C0 1.30258 0.175 0.875 0.525 0.525C0.875 0.175 1.30258 0 1.80775 0H15.1923C15.6974 0 16.125 0.175 16.475 0.525C16.825 0.875 17 1.30258 17 1.80775V15.1923C17 15.6974 16.825 16.125 16.475 16.475C16.125 16.825 15.6974 17 15.1923 17H1.80775ZM1.80775 15.5H15.1923C15.2693 15.5 15.3398 15.4679 15.4038 15.4038C15.4679 15.3398 15.5 15.2693 15.5 15.1923V1.80775C15.5 1.73075 15.4679 1.66025 15.4038 1.59625C15.3398 1.53208 15.2693 1.5 15.1923 1.5H1.80775C1.73075 1.5 1.66025 1.53208 1.59625 1.59625C1.53208 1.66025 1.5 1.73075 1.5 1.80775V15.1923C1.5 15.2693 1.53208 15.3398 1.59625 15.4038C1.66025 15.4679 1.73075 15.5 1.80775 15.5Z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <span *ngSwitchCase="'clone'" class="action-icon edit-icon">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <mask
                  id="mask0_3055_17228"
                  style="mask-type: alpha"
                  maskUnits="userSpaceOnUse"
                  x="0"
                  y="0"
                  width="24"
                  height="24"
                >
                  <rect width="24" height="24" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_3055_17228)">
                  <path
                    d="M18.6923 18.5H8.30775C7.80258 18.5 7.375 18.325 7.025 17.975C6.675 17.625 6.5 17.1974 6.5 16.6923V3.30775C6.5 2.80258 6.675 2.375 7.025 2.025C7.375 1.675 7.80258 1.5 8.30775 1.5H14.502C14.743 1.5 14.9748 1.54683 15.1973 1.6405C15.4196 1.734 15.6128 1.86283 15.777 2.027L19.973 6.223C20.1372 6.38717 20.266 6.58042 20.3595 6.80275C20.4532 7.02525 20.5 7.257 20.5 7.498V16.6923C20.5 17.1974 20.325 17.625 19.975 17.975C19.625 18.325 19.1974 18.5 18.6923 18.5ZM19 7.5H15.7115C15.3718 7.5 15.085 7.383 14.851 7.149C14.617 6.915 14.5 6.62817 14.5 6.2885V3H8.30775C8.23075 3 8.16025 3.03208 8.09625 3.09625C8.03208 3.16025 8 3.23075 8 3.30775V16.6923C8 16.7693 8.03208 16.8398 8.09625 16.9038C8.16025 16.9679 8.23075 17 8.30775 17H18.6923C18.7693 17 18.8398 16.9679 18.9038 16.9038C18.9679 16.8398 19 16.7693 19 16.6923V7.5ZM4.30775 22.5C3.80258 22.5 3.375 22.325 3.025 21.975C2.675 21.625 2.5 21.1974 2.5 20.6923V8.25C2.5 8.03717 2.57183 7.859 2.7155 7.7155C2.859 7.57183 3.03717 7.5 3.25 7.5C3.46283 7.5 3.641 7.57183 3.7845 7.7155C3.92817 7.859 4 8.03717 4 8.25V20.6923C4 20.7693 4.03208 20.8398 4.09625 20.9038C4.16025 20.9679 4.23075 21 4.30775 21H13.75C13.9628 21 14.141 21.0718 14.2845 21.2155C14.4282 21.359 14.5 21.5372 14.5 21.75C14.5 21.9628 14.4282 22.141 14.2845 22.2845C14.141 22.4282 13.9628 22.5 13.75 22.5H4.30775Z"
                    fill="currentColor"
                  />
                </g>
              </svg>
            </span>
            <span *ngSwitchCase="'delete'" class="action-icon delete-icon">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <mask
                  id="mask0_3055_17224"
                  style="mask-type: alpha"
                  maskUnits="userSpaceOnUse"
                  x="0"
                  y="0"
                  width="24"
                  height="24"
                >
                  <rect width="24" height="24" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_3055_17224)">
                  <path
                    d="M7.30775 20.4997C6.81058 20.4997 6.385 20.3227 6.031 19.9687C5.677 19.6147 5.5 19.1892 5.5 18.692V5.99973H5.25C5.0375 5.99973 4.85942 5.92782 4.71575 5.78398C4.57192 5.64015 4.5 5.46198 4.5 5.24948C4.5 5.03682 4.57192 4.85873 4.71575 4.71523C4.85942 4.57157 5.0375 4.49973 5.25 4.49973H9C9 4.2549 9.08625 4.04624 9.25875 3.87374C9.43108 3.7014 9.63967 3.61523 9.8845 3.61523H14.1155C14.3603 3.61523 14.5689 3.7014 14.7413 3.87374C14.9138 4.04624 15 4.2549 15 4.49973H18.75C18.9625 4.49973 19.1406 4.57165 19.2843 4.71548C19.4281 4.85932 19.5 5.03748 19.5 5.24998C19.5 5.46265 19.4281 5.64073 19.2843 5.78423C19.1406 5.9279 18.9625 5.99973 18.75 5.99973H18.5V18.692C18.5 19.1892 18.323 19.6147 17.969 19.9687C17.615 20.3227 17.1894 20.4997 16.6923 20.4997H7.30775ZM17 5.99973H7V18.692C7 18.7818 7.02883 18.8556 7.0865 18.9132C7.14417 18.9709 7.21792 18.9997 7.30775 18.9997H16.6923C16.7821 18.9997 16.8558 18.9709 16.9135 18.9132C16.9712 18.8556 17 18.7818 17 18.692V5.99973ZM10.1543 16.9997C10.3668 16.9997 10.5448 16.9279 10.6885 16.7842C10.832 16.6404 10.9037 16.4622 10.9037 16.2497V8.74973C10.9037 8.53723 10.8318 8.35907 10.688 8.21523C10.5443 8.07157 10.3662 7.99973 10.1535 7.99973C9.941 7.99973 9.76292 8.07157 9.61925 8.21523C9.47575 8.35907 9.404 8.53723 9.404 8.74973V16.2497C9.404 16.4622 9.47583 16.6404 9.6195 16.7842C9.76333 16.9279 9.94158 16.9997 10.1543 16.9997ZM13.8465 16.9997C14.059 16.9997 14.2371 16.9279 14.3807 16.7842C14.5242 16.6404 14.596 16.4622 14.596 16.2497V8.74973C14.596 8.53723 14.5242 8.35907 14.3805 8.21523C14.2367 8.07157 14.0584 7.99973 13.8458 7.99973C13.6333 7.99973 13.4552 8.07157 13.3115 8.21523C13.168 8.35907 13.0962 8.53723 13.0962 8.74973V16.2497C13.0962 16.4622 13.1682 16.6404 13.312 16.7842C13.4557 16.9279 13.6338 16.9997 13.8465 16.9997Z"
                    fill="currentColor"
                  />
                </g>
              </svg>
            </span>
            <span *ngSwitchDefault class="action-icon default-icon">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 8C13.1 8 14 7.1 14 6C14 4.9 13.1 4 12 4C10.9 4 10 4.9 10 6C10 7.1 10.9 8 12 8ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10ZM12 16C10.9 16 10 16.9 10 18C10 19.1 10.9 20 12 20C13.1 20 14 19.1 14 18C14 16.9 13.1 16 12 16Z"
                  fill="currentColor"
                />
              </svg>
            </span>
          </ng-container>
        </button>
      </div>
    </div>
  </div>
</app-card>
