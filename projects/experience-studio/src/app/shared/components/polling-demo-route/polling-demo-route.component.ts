import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription, timer } from 'rxjs';
import { NewPollingResponseProcessorService } from '../../services/new-polling-response-processor.service';
import { CodeWindowComponent } from '../code-window/code-window.component';
import {
  ProgressState,
  StatusType,
  FileData,
  NewPollingResponse
} from '../../models/polling-response.interface';
import { createLogger } from '../../utils/logger';
import {
  // Main polling responses used in demo
  MOCK_OVERVIEW_IN_PROGRESS,
  MOCK_OVERVIEW_COMPLETED,
  MOCK_LAYOUT_ANALYZED_IN_PROGRESS,
  MOCK_LAYOUT_ANALYZED_COMPLETED,
  MOCK_DESIGN_SYSTEM_IN_PROGRESS,
  MOCK_DESIGN_SYSTEM_COMPLETED,
  MOC<PERSON>_DEPLOYED_IN_PROGRESS,
  MOCK_DEPLOYED_COMPLETED,
  // Failed state responses
  MOCK_PROJECT_OVERVIEW_FAILED,
  MOCK_LAYOUT_ANALYZED_FAILED,
  MOCK_DESIGN_SYSTEM_FAILED,
  MOCK_DEPLOYED_FAILED
} from '../../data/mock-polling-responses';

interface DemoStep {
  id: string;
  name: string;
  description: string;
  inProgressResponse: NewPollingResponse;
  completedResponse: NewPollingResponse;
  failedResponse: NewPollingResponse;
  timeout: number; // in seconds
  isActive: boolean;
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
}

/**
 * Enhanced polling demo route component that shows the complete flow
 * with side-by-side layout: controls on left, code-window on right
 */
@Component({
  selector: 'app-polling-demo-route',
  standalone: true,
  imports: [CommonModule, CodeWindowComponent],
  templateUrl: './polling-demo-route.component.html',
  styleUrls: ['./polling-demo-route.component.scss']
})
export class PollingDemoRouteComponent implements OnInit, OnDestroy {
  private subscriptions = new Subscription();
  private logger = createLogger('PollingDemoRoute');
  private currentStepTimer?: Subscription;

  // Demo state
  isRunning = false;
  currentStepIndex = 0;
  autoMode = false;
  showFailureScenarios = false;

  // Current polling state for display
  currentProgress: ProgressState | null = null;
  currentStatus: StatusType | null = null;
  progressDescription: string = '';
  logContent: string = '';
  artifactData: any = null;
  fileList: string[] = [];
  codeFiles: FileData[] = [];
  previewUrl: string = '';
  currentProjectInfo: any = null;

  // Demo steps configuration
  demoSteps: DemoStep[] = [
    {
      id: 'project-overview',
      name: 'Project Overview',
      description: 'Analyzing project requirements and initializing workspace',
      inProgressResponse: MOCK_OVERVIEW_IN_PROGRESS,
      completedResponse: MOCK_OVERVIEW_COMPLETED,
      failedResponse: MOCK_PROJECT_OVERVIEW_FAILED,
      timeout: 3,
      isActive: false,
      status: 'pending'
    },
    {
      id: 'layout-analysis',
      name: 'Layout Analysis',
      description: 'Analyzing UI layout and structure identification',
      inProgressResponse: MOCK_LAYOUT_ANALYZED_IN_PROGRESS,
      completedResponse: MOCK_LAYOUT_ANALYZED_COMPLETED,
      failedResponse: MOCK_LAYOUT_ANALYZED_FAILED,
      timeout: 4,
      isActive: false,
      status: 'pending'
    },
    {
      id: 'design-system',
      name: 'Design System Analysis',
      description: 'Extracting design tokens and analyzing system',
      inProgressResponse: MOCK_DESIGN_SYSTEM_IN_PROGRESS,
      completedResponse: MOCK_DESIGN_SYSTEM_COMPLETED,
      failedResponse: MOCK_DESIGN_SYSTEM_FAILED,
      timeout: 3,
      isActive: false,
      status: 'pending'
    },
    {
      id: 'deployment',
      name: 'Deployment',
      description: 'Generating code and deploying to preview environment',
      inProgressResponse: MOCK_DEPLOYED_IN_PROGRESS,
      completedResponse: MOCK_DEPLOYED_COMPLETED,
      failedResponse: MOCK_DEPLOYED_FAILED,
      timeout: 5,
      isActive: false,
      status: 'pending'
    }
  ];

  constructor(
    public responseProcessor: NewPollingResponseProcessorService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.subscribeToProcessorUpdates();
    this.resetDemo();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.stopCurrentStep();
  }

  /**
   * Subscribe to all processor observables to display current state
   */
  private subscribeToProcessorUpdates(): void {
    this.subscriptions.add(
      this.responseProcessor.currentProgress$.subscribe(progress => {
        this.currentProgress = progress;
        this.cdr.detectChanges();
      })
    );

    this.subscriptions.add(
      this.responseProcessor.currentStatus$.subscribe(status => {
        this.currentStatus = status;
        this.cdr.detectChanges();
      })
    );

    this.subscriptions.add(
      this.responseProcessor.progressDescription$.subscribe(description => {
        this.progressDescription = description;
        this.cdr.detectChanges();
      })
    );

    this.subscriptions.add(
      this.responseProcessor.logContent$.subscribe(content => {
        this.logContent = content;
        this.cdr.detectChanges();
      })
    );

    this.subscriptions.add(
      this.responseProcessor.artifactData$.subscribe(data => {
        this.artifactData = data;
        this.cdr.detectChanges();
      })
    );

    this.subscriptions.add(
      this.responseProcessor.fileList$.subscribe(files => {
        this.fileList = files;
        this.cdr.detectChanges();
      })
    );

    this.subscriptions.add(
      this.responseProcessor.codeFiles$.subscribe(files => {
        this.codeFiles = files;
        this.cdr.detectChanges();
      })
    );

    this.subscriptions.add(
      this.responseProcessor.previewUrl$.subscribe(url => {
        this.previewUrl = url;
        this.cdr.detectChanges();
      })
    );

    this.subscriptions.add(
      this.responseProcessor.projectInfo$.subscribe(projectInfo => {
        this.currentProjectInfo = projectInfo;
        this.cdr.detectChanges();
      })
    );
  }

  /**
   * Start the complete demo flow
   */
  startCompleteFlow(): void {
    this.logger.info('Starting complete flow demo...');
    this.resetDemo();
    this.autoMode = true;
    this.isRunning = true;
    this.showFailureScenarios = false;
    this.runNextStep();
  }

  /**
   * Start failure scenarios demo
   */
  startFailureFlow(): void {
    this.logger.info('Starting failure flow demo...');
    this.resetDemo();
    this.autoMode = true;
    this.isRunning = true;
    this.showFailureScenarios = true;
    this.runNextStep();
  }

  /**
   * Run individual step manually
   */
  runStep(stepIndex: number, forceFailure: boolean = false): void {
    if (stepIndex < 0 || stepIndex >= this.demoSteps.length) return;

    this.currentStepIndex = stepIndex;
    this.isRunning = true;
    this.autoMode = false;
    this.showFailureScenarios = forceFailure;
    this.runCurrentStep();
  }

  /**
   * Stop the current demo
   */
  stopDemo(): void {
    this.logger.info('Stopping demo...');
    this.isRunning = false;
    this.autoMode = false;
    this.stopCurrentStep();
    this.resetStepStates();
  }

  /**
   * Reset the demo to initial state
   */
  resetDemo(): void {
    this.logger.info('Resetting demo...');
    this.stopDemo();
    this.currentStepIndex = 0;
    this.resetStepStates();
    this.responseProcessor.reset();

    // Reset local state
    this.currentProgress = null;
    this.currentStatus = null;
    this.progressDescription = '';
    this.logContent = '';
    this.artifactData = null;
    this.fileList = [];
    this.codeFiles = [];
    this.previewUrl = '';
    this.currentProjectInfo = null;
  }

  /**
   * Update step timeout
   */
  updateStepTimeout(stepIndex: number, timeout: number): void {
    if (stepIndex >= 0 && stepIndex < this.demoSteps.length) {
      this.demoSteps[stepIndex].timeout = timeout;
    }
  }

  /**
   * Get current step
   */
  getCurrentStep(): DemoStep | null {
    return this.demoSteps[this.currentStepIndex] || null;
  }

  /**
   * Get step status class for styling
   */
  getStepStatusClass(step: DemoStep): string {
    return `step-${step.status}`;
  }

  /**
   * Get overall demo status
   */
  getDemoStatus(): string {
    if (!this.isRunning) return 'Stopped';
    if (this.autoMode) return 'Auto Mode';
    return 'Manual Mode';
  }

  private runNextStep(): void {
    if (!this.isRunning || !this.autoMode) return;

    if (this.currentStepIndex >= this.demoSteps.length) {
      this.logger.info('Demo completed');
      this.isRunning = false;
      return;
    }

    this.runCurrentStep();
  }

  private runCurrentStep(): void {
    const step = this.demoSteps[this.currentStepIndex];
    if (!step) return;

    this.logger.info(`Running step: ${step.name}`);

    // Mark current step as active
    this.demoSteps.forEach(s => s.isActive = false);
    step.isActive = true;
    step.status = 'in-progress';

    // Process in-progress response
    this.responseProcessor.processResponse(step.inProgressResponse);

    // Set timer for completion
    this.currentStepTimer = timer(step.timeout * 1000).subscribe(() => {
      this.completeCurrentStep();
    });
  }

  private completeCurrentStep(): void {
    const step = this.demoSteps[this.currentStepIndex];
    if (!step) return;

    // Determine if this step should fail
    const shouldFail = this.showFailureScenarios && Math.random() < 0.3; // 30% chance of failure

    if (shouldFail) {
      step.status = 'failed';
      this.responseProcessor.processResponse(step.failedResponse);
      this.logger.info(`Step failed: ${step.name}`);

      if (this.autoMode) {
        // Stop auto mode on failure
        this.isRunning = false;
        this.autoMode = false;
      }
    } else {
      step.status = 'completed';
      this.responseProcessor.processResponse(step.completedResponse);
      this.logger.info(`Step completed: ${step.name}`);

      if (this.autoMode) {
        // Move to next step after a brief delay
        setTimeout(() => {
          this.currentStepIndex++;
          this.runNextStep();
        }, 1000);
      }
    }

    step.isActive = false;
  }

  private stopCurrentStep(): void {
    if (this.currentStepTimer) {
      this.currentStepTimer.unsubscribe();
      this.currentStepTimer = undefined;
    }
  }

  private resetStepStates(): void {
    this.demoSteps.forEach(step => {
      step.isActive = false;
      step.status = 'pending';
    });
  }

  /**
   * Get stepper display title for current progress
   */
  getStepperDisplayTitle(): string {
    if (!this.currentProgress) {
      return 'No progress';
    }
    return this.responseProcessor.getStepperDisplayTitle(this.currentProgress);
  }

}
