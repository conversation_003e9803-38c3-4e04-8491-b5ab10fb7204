<awe-header theme="light" class="py-1">
  <div left-content class="mt-4">
    <div class="logo-container">
      <img [src]="logoSrc" class="ascendion-logo" alt="Ascendion Logo" *ngIf="selectedUser?.type !== 'Project Team'" />
      <div class="flip-container" *ngIf="isFlipping && selectedUser?.type === 'Project Team'">
        <div class="flipper">
          <img [src]="logoSrc" class="ascendion-logo" alt="Ascendion Logo" />
          <img [src]="getCurrentProjectLogo()" class="client-logo" alt="Client Logo" />
        </div>
      </div>
    </div>
  </div>
  <div right-content class="gap-4 d-flex align-items-center header-right-content">
    <div *ngIf="selectedUser?.type === 'Sales'" class="cursor-pointer d-flex justify-content-center align-items-center">
      <img [src]="consoleIcon" alt="Toggle Theme" />
    </div>
    <div class="cursor-pointer d-flex justify-content-center align-items-center">
      <img [src]="translateIcon" alt="Translate" />
    </div>
    <div class="cursor-pointer d-flex justify-content-center align-items-center">
      <img [src]="themeToggleIcon" alt="Toggle Theme" />
    </div>

    <div class="profile-dropdown">
      <div class="cursor-pointer d-flex justify-content-center align-items-center" (click)="toggleProfileDropdown()">
        <div class="profile-initials">
          {{ getInitials(selectedUser?.name || users[0].name) }}
        </div>
      </div>
      <div class="dropdown-menu" [class.show]="isDropdownOpen">
        <div class="profile-section">
          <div class="profile-item" *ngFor="let user of users" [class.active]="selectedUser?.name === user.name"
            (click)="selectUser(user)">
            <div class="profile-info">
              <div class="profile-initials">{{ getInitials(user.name) }}</div>
              <div class="user-details">
                <span class="name">{{ user.name }}</span>
                <span class="role">{{ user.role }}</span>
              </div>
            </div>
            <div class="project-switcher" *ngIf="user.type === 'Project Team'">
              <span class="current-project" [class.active]="selectedUser?.name === user.name">
                Current Project: {{ currentProject }}
              </span>
              <div class="project-options">
                <button *ngFor="let project of getAvailableProjects(user)" class="project-option"
                  [class.active]="currentProject === project.name" (click)="switchProject(project.name)">
                  {{ project.name }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</awe-header>