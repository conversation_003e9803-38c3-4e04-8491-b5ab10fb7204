import { Injectable } from '@angular/core';

/**
 * Service to handle text transformations throughout the application
 */
@Injectable({
  providedIn: 'root'
})
export class TextTransformationService {
  constructor() {}

  /**
   * Transforms text by removing underscores and hyphens and properly capitalizing words
   * @param text The text to transform
   * @returns The transformed text
   */
  transformText(text: string): string {
    if (!text) return '';

    // Replace underscores and hyphens with spaces
    let transformedText = text.replace(/[_-]/g, ' ');
    
    // Capitalize the first letter of each sentence
    transformedText = transformedText.replace(/(^\s*\w|[.!?]\s*\w)/g, match => match.toUpperCase());
    
    return transformedText;
  }

  /**
   * Transforms an array of messages by removing underscores and hyphens
   * @param messages Array of messages to transform
   * @returns Array of transformed messages
   */
  transformMessages(messages: string[]): string[] {
    if (!messages || !Array.isArray(messages)) return [];
    
    return messages.map(message => this.transformText(message));
  }
}
