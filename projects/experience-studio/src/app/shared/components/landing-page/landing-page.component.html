<div class="container-fluid" id="main-content-container">
  <div class="container-fluid justify-content-center" id="card-content">
    <app-hero-section-header
      [headerTitle]="'Dream Build Launch!'"
      [headerDescription]="'<PERSON><PERSON><PERSON><PERSON> blends Art Science to bring your ideas to life.'"
      [subHeading]="'What would you like to build today?'"></app-hero-section-header>

    <!-- Experience Cards Container (New Design) -->
    <div class="exp-grid">
      <div class="exp-card"
           *ngFor="let card of studioCards; trackBy: trackByCardId"
           (click)="navigateToStudio(card, $event)"
           [class.disabled-card]="card.disabled"
           [style.background-color]="getCardBackground()"
           [style.border]="'0.5px solid ' + (theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)')"
           [attr.aria-disabled]="card.disabled"
           role="button"
           tabindex="0"
           [attr.aria-label]="card.title + ' - ' + card.description">
        <div class="card-content">
          <div class="text-content">
            <h2 [style.color]="getCardTextColor()">{{ card.title }}</h2>
            <p class="description" [style.color]="getCardDescriptionColor()">{{ card.description }}</p>
          </div>
          <div class="image-container">
            <img
              [src]="card.image"
              [alt]="card.title + ' illustration'"
              class="responsive-image"
              loading="lazy"
              decoding="async"
              fetchpriority="high">
          </div>
        </div>
      </div>
    </div>

    <!--
      PREVIOUS CARD DESIGN: These are the previous card designs used in the landing page
      Keeping them commented for reference in case we need to revert back
    -->
    <!--
    <div class="row justify-content-center">
      <div
        class="col-12 col-md-6 col-xl-6 col-xxl-3 mb-4 flip-card"
        *ngFor="let card of cardData"
        [class.flipped]="card.isFlipped"
        (mouseenter)="flipCard(card, true)"
        (mouseleave)="flipCard(card, false)"
        (click)="navigateTo(card.redirectPath, $event, card.frontTitle)">
        <div class="flip-card-inner">
          <div class="flip-card-front">
            <awe-cards
              variant="feature"
              size="medium"
              [title]="card.frontTitle"
              [image]="'/assets/cards-images/card_1.png'"
              [theme]="theme"
              [hasFooter]="false">
              <div content>
                <p>{{ card.frontDescription }}</p>
              </div>
            </awe-cards>
          </div>

          <div class="flip-card-back">
            <awe-cards
              class="card-back"
              variant="info"
              size="medium"
              [title]="card.frontTitle"
              [theme]="theme"
              [hasFooter]="false">
              <div content>
                <p class="back-description">{{ card.backDescription }}</p>
                <awe-button
                  [label]="card.buttonText"
                  variant="primary"
                  animation="ripple"
                  (click)="navigateTo(card.redirectPath, $event, card.frontTitle)"></awe-button>
              </div>
            </awe-cards>
          </div>
        </div>
      </div>
    </div>
    -->
  </div>

  <!-- Divider Section -->
  <div id="divider-section-container" class="mt-5">
    <div class="d-flex align-items-center justify-content-center">
      <img
        src="/assets/icons/divider-light.svg"
        class="divider-image"
        tabindex="0"
        alt="divider"
        loading="lazy"
        decoding="async" />
    </div>
  </div>

  <!-- Recent Projects Section -->
  <div id="recent-projects-section" class="mt-4 py-5">
    <app-recent-creation></app-recent-creation>
  </div>
</div>
