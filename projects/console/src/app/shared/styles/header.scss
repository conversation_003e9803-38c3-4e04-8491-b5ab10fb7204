// Shared Header Styles

// Header container
.component-header {
  width: 100%;
  margin-bottom: 20px;
}

// Header content layout
.header-content {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
}

// Search section that takes full available width
.search-section {
  flex: 1 !important;
  margin-right: 16px !important;
  min-width: 0 !important;
  width: auto !important;

  ::ng-deep .search-container {
    height: 40px !important;
    width: 100% !important;
  }

  ::ng-deep .search-input {
    height: 40px !important;
    box-sizing: border-box !important;
    width: 100% !important;
  }
}

// Action buttons section
.action-buttons {
  flex-shrink: 0 !important;
  display: flex !important;
  gap: 8px !important;
  width: auto !important;
  justify-content: flex-end !important;
}

// Filter button style
.action-button {
  padding: 0 16px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--dashboard-bg-action-button);
  border: 1px solid var(--dashboard-action-button-border);
  font-family: 'Mulish', -apple-system, 'Roboto', 'Helvetica', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--dashboard-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  svg {
    margin-left: 4px;
    transition: transform 0.3s ease;
    color: var(--dashboard-text-secondary);
  }
  
  &:hover {
    background-color: var(--dashboard-bg-lighter);
    border-color: var(--dashboard-action-button-border);
  }
  
  &.active-filter {
    background-color: var(--nav-active);
    border-color: var(--dropdown-focus-border);
    color: var(--nav-active-text);
    
    svg {
      color: var(--nav-active-text);
    }
  }
}

// Filter section styles
.filter-section {
  width: 100%;
  overflow: visible;
  transition: max-height 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
  animation: fadeIn 0.3s ease-out;
  position: relative;
  z-index: 500;
}

// When filter bar is present, adjust cards container margin
.filter-section + .cards-container {
  margin-top: 0 !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive styles
@media (max-width: 768px) {
  .header-content {
    flex-direction: column !important;
    align-items: stretch !important;
  }
  
  .search-section {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 8px !important;
  }
  
  .action-buttons {
    width: 100% !important;
    justify-content: flex-start !important;
  }
}

@media (max-width: 576px) {
  .action-buttons {
    flex-wrap: wrap !important;
  }
  
  .action-button {
    flex: 1;
    min-width: 100px;
  }
} 