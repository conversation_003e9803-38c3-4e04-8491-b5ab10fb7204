<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Toast Icon">
<g id="Group 11">
<g id="Rectangle 1039" filter="url(#filter0_b_386_1653)">
<rect width="24" height="24" rx="12" fill="url(#paint0_linear_386_1653)" fill-opacity="0.5"/>
<rect x="0.25" y="0.25" width="23.5" height="23.5" rx="11.75" stroke="url(#paint1_linear_386_1653)" stroke-width="0.5"/>
</g>
<g id="Group 380">
<path id="Vector" d="M18.7944 14.8403L13.3001 5.7215C13.1686 5.50219 12.9792 5.32003 12.7509 5.19336C12.5227 5.06669 12.2637 5 12 5C11.7363 5 11.4773 5.06669 11.2491 5.19336C11.0208 5.32003 10.8314 5.50219 10.6999 5.7215L5.20561 14.8403C5.07214 15.0585 5.00121 15.3067 5.00002 15.5597C4.99882 15.8127 5.06742 16.0616 5.19883 16.2809C5.33024 16.5003 5.51979 16.6823 5.74822 16.8086C5.97666 16.9349 6.23585 17.0009 6.49948 17H17.5005C17.7641 17.0009 18.0233 16.9349 18.2518 16.8086C18.4802 16.6823 18.6698 16.5003 18.8012 16.2809C18.9326 16.0616 19.0012 15.8127 19 15.5597C18.9988 15.3067 18.9279 15.0585 18.7944 14.8403Z" fill="#FFBF00"/>
<path id="Vector_2" d="M12 8V12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<circle id="Ellipse 230" cx="12" cy="15" r="0.5" fill="white" stroke="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_b_386_1653" x="-12" y="-12" width="48" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_386_1653"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_386_1653" result="shape"/>
</filter>
<linearGradient id="paint0_linear_386_1653" x1="0" y1="0" x2="5.48173" y2="27.0245" gradientUnits="userSpaceOnUse">
<stop stop-color="#F6E5A9"/>
<stop offset="1" stop-color="#F6DCA6"/>
</linearGradient>
<linearGradient id="paint1_linear_386_1653" x1="0.639999" y1="0.280788" x2="5.88361" y2="27.1987" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEFCF7"/>
<stop offset="1" stop-color="#FDF6E4"/>
</linearGradient>
</defs>
</svg>
