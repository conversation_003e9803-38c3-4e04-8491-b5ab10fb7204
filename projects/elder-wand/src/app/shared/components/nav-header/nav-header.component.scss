::ng-deep .outer-box.light {
  background-color: transparent !important;
  box-shadow: none !important;
}

::ng-deep .container {
  background-color: transparent !important;
}

.profile-dropdown {
  position: relative;
  display: inline-block;

  .dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 16px;
    min-width: 280px;
    display: none;
    z-index: 1000;
    border: 1px solid var(--Border-Color, #e5e7eb);

    &.show {
      display: block;
    }
  }

  .profile-section {
    .profile-item {
      padding: 12px 16px;
      cursor: pointer;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(101, 102, 205, 0.08);
      }

      &.active {
        background: linear-gradient(90deg, #8c65f7 0%, #e84393 100%);
        box-shadow: 0px 4px 12px rgba(140, 101, 247, 0.3);

        .profile-initials {
          background: rgba(255, 255, 255, 0.2);
          color: white;
        }

        .name,
        .role {
          color: white;
        }

        .project-options {
          margin-top: 12px;
        }
      }
    }
  }
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.profile-initials {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, #8c65f7 0%, #e84393 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.name {
  font-size: 14px;
  font-weight: 600;
  color: var(--Text-Primary, #1a1a1a);
}

.role {
  font-size: 12px;
  color: var(--Text-Secondary, #666666);
}

.project-switcher {
  margin-top: 8px;
  padding-left: 52px;

  .current-project {
    font-size: 12px;
    color: var(--Text-Secondary, #666666);
    margin-bottom: 8px;
    display: block;

    &.active {
      color: white;
    }
  }
}

.project-options {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  overflow-x: auto;
  padding-bottom: 4px;
  scrollbar-width: none;
  -ms-overflow-style: none;
  white-space: nowrap;

  &::-webkit-scrollbar {
    display: none;
  }

  .project-option {
    padding: 6px 12px;
    border-radius: 16px;
    border: 1px solid var(--Border-Color, #e5e5e5);
    background: transparent;
    color: white;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;

    &:hover {
      background: rgba(101, 102, 205, 0.08);
      border-color: var(--Primary-Color, #6566cd);
    }

    &.active {
      background: var(--Primary-Color, #6566cd);
      border-color: var(--Primary-Color, #6566cd);
      color: white;
    }
  }
}

@media (max-width: 400px) {
  ::ng-deep .outer-box .center-content-wrapper {
    display: none !important;
  }
}

.logo-container {
  position: relative;
  width: 200px;
  height: 40px;
  perspective: 1000px;
}

.ascendion-logo,
.client-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.flip-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.flipper {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  animation: flip 20s infinite;
}

.flipper img {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
}

.client-logo {
  transform: rotateY(180deg);
}

@keyframes flip {
  0% {
    transform: rotateY(0deg);
  }

  50% {
    transform: rotateY(180deg);
  }

  100% {
    transform: rotateY(360deg);
  }
}

.glow-button {
  position: relative;
  padding: 6px 12px;
  border-radius: 16px;
  background: linear-gradient(90deg, #8c65f7 0%, #e84393 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  animation: glow 2s infinite;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(140, 101, 247, 0.4);
  }

  .glow-text {
    color: white;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

@keyframes glow {
  0% {
    box-shadow:
      0 0 5px rgba(140, 101, 247, 0.4),
      0 0 10px rgba(140, 101, 247, 0.3),
      0 0 15px rgba(140, 101, 247, 0.2);
  }

  50% {
    box-shadow:
      0 0 10px rgba(140, 101, 247, 0.6),
      0 0 20px rgba(140, 101, 247, 0.4),
      0 0 30px rgba(140, 101, 247, 0.2);
  }

  100% {
    box-shadow:
      0 0 5px rgba(140, 101, 247, 0.4),
      0 0 10px rgba(140, 101, 247, 0.3),
      0 0 15px rgba(140, 101, 247, 0.2);
  }
}