import { CommonModule } from '@angular/common';
import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { HeadingComponent, BodyTextComponent, CardsComponent } from '@awe/play-comp-library';
import {
  RecentProjectService,
  Project,
} from '../../services/recent-project-services/recent-project.service';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { CardOption } from '../../models/recent-creation.model';
import { SubscriptionManager, ObserverManager } from '../../utils/subscription-management.util';

@Component({
  selector: 'app-recent-creation',
  imports: [CommonModule, HeadingComponent, BodyTextComponent, CardsComponent],
  standalone: true,
  templateUrl: './recent-creation.component.html',
  styleUrl: './recent-creation.component.scss',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RecentCreationComponent implements OnInit {
  theme: 'light' | 'dark' = 'light';
  selectedId: string | null = null;
  currentCategory: string = 'recent';
  isLoading: boolean = true;
  options: { [category: string]: CardOption[] } = { recent: [], all: [] };

  private subscriptionManager = new SubscriptionManager();
  private observerManager = new ObserverManager();

  constructor(
    private recentProjectService: RecentProjectService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initializePlaceholders();
    this.setupIntersectionObserver();

    // Load projects immediately if we're already on the landing page
    if (this.router.url.includes('/experience/main')) {
      this.loadProjects();
    }

    // Also set up subscription to load projects when navigating to the landing page
    this.subscriptionManager.subscribe(
      this.router.events.pipe(filter(event => event instanceof NavigationEnd)),
      () => {
        if (this.router.url.includes('/experience/main')) {
          this.loadProjects();
        }
      }
    );
  }

  switchCategory(category: string): void {
    if (this.currentCategory !== category) {
      this.currentCategory = category;
      this.animateCategorySwitch(category);
      this.cdr.markForCheck(); // Trigger change detection
    }
  }

  getCurrentOptions(): CardOption[] {
    return this.options[this.currentCategory] || [];
  }

  handleSelection(id: string, event?: Event): void {
    if (event) event.preventDefault();
    this.selectedId = id;
    this.cdr.markForCheck(); // Trigger change detection
  }

  getDefaultActionText(type: string): string {
    const actionMap: { [key: string]: string } = {
      ui: 'Generate UI',
      app: 'Generate App',
      analysis: 'Design Analysis',
      accessibility: 'Review Accessibility',
    };
    return actionMap[type.toLowerCase()] || 'View';
  }

  trackByFn(_: number, item: CardOption): string {
    return item.id;
  }

  private setupIntersectionObserver(): void {
    const element = document.querySelector('.recent-creation-wrapper');
    if (element) {
      const observer = this.observerManager.createIntersectionObserver(
        entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.loadProjects();
              observer.disconnect();
            }
          });
        },
        {
          root: null,
          rootMargin: '100px',
          threshold: 0.1,
        }
      );
      observer.observe(element);
    } else {
      this.loadProjects();
    }
  }

  private initializePlaceholders(): void {
    this.options['recent'] = Array(4)
      .fill(null)
      .map(() => this.createPlaceholder());
    this.options['all'] = Array(12)
      .fill(null)
      .map(() => this.createPlaceholder());
  }

  private createPlaceholder(): CardOption {
    return { id: '', heading: '', description: '', type: '', timestamp: '' };
  }

  private loadProjects(): void {
    this.isLoading = true;
    this.loadProjectCategory('recent', 4);
    this.loadProjectCategory('all', 12);
  }

  private loadProjectCategory(category: string, limit: number): void {
    this.subscriptionManager.subscribe(
      this.recentProjectService.getUserProjects('<EMAIL>', limit),
      response => this.handleProjectsLoad(response, category),
      () => (this.isLoading = false)
    );
  }

  private handleProjectsLoad(response: any, category: string): void {
    requestAnimationFrame(() => {
      this.options[category] = this.mapProjectsToCardOptions(response.projects);
      if (category === 'recent') {
        this.isLoading = false;
      }
      this.cdr.markForCheck(); // Trigger change detection after updating data
    });
  }

  private truncateDescription(description: string): string {
    const words = description.split(' ');
    return words.length > 10 ? words.slice(0, 10).join(' ') + '...' : description;
  }

  private mapProjectsToCardOptions(projects: Project[]): CardOption[] {
    return projects.map(project => ({
      id: project.project_id,
      heading: project.project_name,
      description: this.truncateDescription(project.project_description.replace(/^"|"$/g, '')),
      type: project.project_type.toLowerCase(),
      timestamp: this.recentProjectService.formatDate(project.last_modified),
    }));
  }

  private animateCategorySwitch(category: string): void {
    const gridElement = document.querySelector('.cards-grid') as HTMLElement;
    if (gridElement) {
      gridElement.classList.remove('slide-recent', 'slide-all');
      void gridElement.offsetWidth;
      gridElement.classList.add(category === 'recent' ? 'slide-recent' : 'slide-all');
    }
  }
}
