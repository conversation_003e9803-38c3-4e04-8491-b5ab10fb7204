<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Toast Icon">
<g id="Group 11">
<g id="Rectangle 1039" filter="url(#filter0_b_386_1730)">
<rect y="0.132568" width="24" height="24" rx="12" fill="url(#paint0_linear_386_1730)" fill-opacity="0.5"/>
<rect x="0.25" y="0.382568" width="23.5" height="23.5" rx="11.75" stroke="url(#paint1_linear_386_1730)" stroke-width="0.5"/>
</g>
<g id="Group 2">
<circle id="Ellipse 229" cx="12" cy="12.1326" r="7" fill="#6566CD"/>
<g id="Layer_1">
<g id="Group">
<path id="Vector" d="M12.75 16.6658V10.6658H10.5V11.0408H11.25V16.6658H10.5V17.0408H13.5V16.6658H12.75Z" fill="white"/>
<path id="Vector_2" d="M11.9954 9.55247C12.6188 9.55247 13.1251 9.04622 13.1251 8.42278C13.1251 7.79934 12.6188 7.29309 11.9954 7.29309C11.372 7.29309 10.8657 7.79934 10.8657 8.42278C10.8657 9.04622 11.372 9.55247 11.9954 9.55247Z" fill="white"/>
</g>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_b_386_1730" x="-12" y="-11.8674" width="48" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_386_1730"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_386_1730" result="shape"/>
</filter>
<linearGradient id="paint0_linear_386_1730" x1="0" y1="0.132568" x2="5.48173" y2="27.157" gradientUnits="userSpaceOnUse">
<stop stop-color="#CDCDEE"/>
<stop offset="1" stop-color="#AFB1E4"/>
</linearGradient>
<linearGradient id="paint1_linear_386_1730" x1="0.639999" y1="0.413357" x2="5.88361" y2="27.3313" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.5"/>
</linearGradient>
</defs>
</svg>
