import { Component, OnInit } from '@angular/core';
import { CommonModule } from "@angular/common";
import { RouterModule } from '@angular/router';
import { Router } from '@angular/router';
import { CardComponent } from '../../shared/components';

// Interfaces
interface UserLog {
  id: string;
  username: string;
  avatar: string;
  securityToken: string;
  status: 'Active' | 'Inactive' | 'Pending';
}

interface ModelUsage {
  id: string;
  name: string;
  publisher: {
    name: string;
    logo: string;
  };
  agentsCount: number;
}

interface PendingApproval {
  id: string;
  name: string;
  type: string;
}

interface QuickAction {
  icon: string;
  label: string;
  route: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule, CardComponent],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss'
})
export class DashboardComponent implements OnInit {
  // Dashboard metrics
  totalAgents: number = 136;
  newAgentsCreated: number = 50;
  
  totalWorkflows: number = 124;
  newWorkflowsCreated: number = 50;
  
  totalUsers: number = 300;
  newUsersAdded: number = 50;
  
  // Quick actions panel state
  quickActionsExpanded: boolean = false;
  
  // Current user info (would come from auth service in real app)
  currentUser: { name: string } = { name: 'Akash Raj' };
  lastUpdateTime: string = '16:24:35 EST';
  
  // Footer info
  currentYear: number = new Date().getFullYear();
  
  // User logs data
  userLogs: UserLog[] = [
    {
      id: '1',
      username: 'Michael Scott',
      avatar: 'assets/images/avatars/michael-scott.jpg',
      securityToken: 'X9D7K2B4MQ',
      status: 'Active'
    },
    {
      id: '2',
      username: 'Jim Halpert',
      avatar: 'assets/images/avatars/jim-halpert.jpg',
      securityToken: 'QWE2349SDF',
      status: 'Active'
    },
    {
      id: '3',
      username: 'Dwight Schrute',
      avatar: 'assets/images/avatars/dwight-schrute.jpg',
      securityToken: 'OWDF1230JS',
      status: 'Active'
    },
    {
      id: '4',
      username: 'Kevin Malone',
      avatar: 'assets/images/avatars/kevin-malone.jpg',
      securityToken: 'SDVP9I23EJ',
      status: 'Active'
    }
  ];
  
  // Model usage data
  modelUsage: ModelUsage[] = [
    {
      id: '1',
      name: 'GPT 3',
      publisher: {
        name: 'Open AI',
        logo: 'assets/images/logos/openai-logo.png'
      },
      agentsCount: 48
    },
    {
      id: '2',
      name: 'Claude 2',
      publisher: {
        name: 'Anthropic',
        logo: 'assets/images/logos/anthropic-logo.png'
      },
      agentsCount: 24
    },
    {
      id: '3',
      name: 'Gemini',
      publisher: {
        name: 'Google',
        logo: 'assets/images/logos/google-logo.png'
      },
      agentsCount: 20
    },
    {
      id: '4',
      name: 'GPT-4',
      publisher: {
        name: 'Open AI',
        logo: 'assets/images/logos/openai-logo.png'
      },
      agentsCount: 8
    }
  ];
  
  // Pending approvals
  pendingApprovals: PendingApproval[] = [
    {
      id: '1',
      name: 'Test Ruby to Springboot',
      type: 'migration'
    },
    {
      id: '2',
      name: 'Customer Support Chatbot',
      type: 'agent'
    },
    {
      id: '3',
      name: 'Invoice Processing & Approval',
      type: 'workflow'
    },
    {
      id: '4',
      name: 'Invoice Processing & Approval',
      type: 'workflow'
    },
    {
      id: '5',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent'
    },
    {
      id: '6',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent'
    },
    {
      id: '7',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent'
    },
    {
      id: '8',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent'
    }
  ];
  
  // Quick actions
  quickActions: QuickAction[] = [
    {
      icon: 'awe_agents',
      label: 'Create Agents',
      route: '/launch/agents/create'
    },
    {
      icon: 'awe_workflows',
      label: 'Create Workflows',
      route: '/launch/workflows/create'
    },
    {
      icon: 'awe_prompts',
      label: 'Create Prompts',
      route: '/libraries/prompts/create'
    },
    {
      icon: 'awe_knowledgebase',
      label: 'Add Knowledge base',
      route: '/libraries/knowledge-base/create'
    },
    {
      icon: 'awe_guardrails',
      label: 'Add Guardrails',
      route: '/libraries/guardrails/create'
    },
    {
      icon: 'awe_manage',
      label: 'Add Users',
      route: '/manage/users/create'
    }
  ];

  constructor(private router: Router) {}

  ngOnInit(): void {
    // The layout is now managed with fixed heights in CSS
    // No need for recalculateLayout
  }
  
  // Toggle the quick actions panel
  toggleQuickActions(): void {
    this.quickActionsExpanded = !this.quickActionsExpanded;
  }
  
  // Navigate to a route
  navigateTo(route: string): void {
    this.router.navigate([route]);
  }
  
  // Approve a pending item
  approveItem(id: string): void {
    console.log(`Approving item with ID: ${id}`);
    // In a real app, you would call a service to approve the item
    // Then remove it from the pendingApprovals array or refresh the list
  }
} 