import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-workflow-node',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './workflow-node.component.html',
  styleUrls: ['./workflow-node.component.scss']
})
export class WorkflowNodeComponent {
  @Input() data: any;
  @Input() selected: boolean = false;
  @Output() deleteNode = new EventEmitter<void>();
  
  // Methods for handling node interactions
  onDelete(event: MouseEvent): void {
    event.stopPropagation();
    this.deleteNode.emit();
  }
} 