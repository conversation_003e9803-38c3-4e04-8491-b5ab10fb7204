import {
  Component,
  Input,
  Output,
  EventEmitter,
  HostListener,
  OnInit,
  AfterViewInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';

export interface FilterTab {
  id: string;
  label: string;
  icon?: string;
  priority?: number;
}

@Component({
  selector: 'app-filter-tabs',
  templateUrl: './filter-tabs.component.html',
  styleUrls: ['./filter-tabs.component.scss'],
  standalone: true,
  imports: [CommonModule],
})
export class FilterTabsComponent implements OnInit, AfterViewInit {
  @Input() tabs: FilterTab[] = [];
  @Input() activeTab: string = 'all';
  @Output() tabChange = new EventEmitter<string>();

  visibleTabs: FilterTab[] = [];
  dropdownTabs: FilterTab[] = [];
  showDropdown = false;

  constructor() { }

  ngOnInit(): void {
    // Sort tabs by priority if provided
    this.tabs = this.tabs
      .map((tab, index) => ({
        ...tab,
        priority: tab.priority ?? this.tabs.length - index, // Default priority based on order
      }))
      .sort((a, b) => (b.priority || 0) - (a.priority || 0));

    this.updateTabsVisibility();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.updateTabsVisibility();
    });
  }

  @HostListener('window:resize')
  onResize() {
    this.updateTabsVisibility();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const filterButton = document.querySelector('.filter-dropdown-btn');
    const dropdown = document.querySelector('.filter-dropdown');
    if (
      !filterButton?.contains(event.target as Node) &&
      !dropdown?.contains(event.target as Node)
    ) {
      this.showDropdown = false;
    }
  }

  updateTabsVisibility() {
    const container = document.querySelector('.tabs-container');
    if (!container) return;

    const containerWidth = container.clientWidth;
    const filterButtonWidth = 100; // Width reserved for filter button
    const availableWidth = containerWidth - filterButtonWidth;

    // Reset tabs
    this.visibleTabs = [];
    this.dropdownTabs = [];

    let currentWidth = 0;
    const averageTabWidth = availableWidth / this.tabs.length;

    // Distribute tabs based on available space
    for (const tab of this.tabs) {
      const estimatedWidth = this.calculateTabWidth(tab);
      if (currentWidth + estimatedWidth <= availableWidth && estimatedWidth <= averageTabWidth * 1.5) {
        this.visibleTabs.push(tab);
        currentWidth += estimatedWidth;
      } else {
        this.dropdownTabs.push(tab);
      }
    }
  }

  private calculateTabWidth(tab: FilterTab): number {
    // Approximate width calculation based on text length and padding
    const textWidth = tab.label.length * 8; // Approximate 8px per character
    const padding = 32; // Left and right padding
    const iconWidth = tab.icon ? 24 : 0; // Icon width if present
    const gap = 8; // Gap between icon and text
    return textWidth + padding + iconWidth + gap;
  }

  toggleDropdown(event: Event) {
    event.stopPropagation();
    this.showDropdown = !this.showDropdown;
  }

  onTabClick(tabId: string, event?: Event) {
    if (event) {
      event.stopPropagation();
    }
    this.activeTab = tabId;
    this.tabChange.emit(tabId);
    this.showDropdown = false;
  }

  isActiveTab(tabId: string): boolean {
    return this.activeTab === tabId;
  }
}
