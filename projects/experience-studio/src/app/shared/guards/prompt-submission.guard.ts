import { Injectable } from '@angular/core';
import { CanActivate, UrlTree, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { PromptSubmissionService } from '../services/prompt-submission.service';

@Injectable({
  providedIn: 'root'
})
export class PromptSubmissionGuard implements CanActivate {
  constructor(
    private promptSubmissionService: PromptSubmissionService,
    private router: Router
  ) {}

  canActivate(route: import('@angular/router').ActivatedRouteSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    // Check if the user has submitted a prompt
    if (this.promptSubmissionService.hasPromptBeenSubmitted()) {
      return true; // Allow navigation
    }

    // Get the current URL to determine which prompt route to redirect to
    const currentUrl = this.router.url;
    let redirectUrl = '/experience/prompt'; // Default fallback

    // First try to use the route data if available
    if (route.data && route.data['cardType']) {
      const cardType = route.data['cardType'];
      if (cardType === 'Generate UI Design') {
        redirectUrl = '/experience/generate-ui-design/prompt';
      } else if (cardType === 'Generate Application') {
        redirectUrl = '/experience/generate-application/prompt';
      }
    }
    // Fallback to URL checking if route data is not available
    else if (currentUrl.includes('generate-ui-design')) {
      redirectUrl = '/experience/generate-ui-design/prompt';
    } else if (currentUrl.includes('generate-application')) {
      redirectUrl = '/experience/generate-application/prompt'; 
    }

    // If not submitted, redirect back to the appropriate prompt page
    return this.router.createUrlTree([redirectUrl]);
  }
}
