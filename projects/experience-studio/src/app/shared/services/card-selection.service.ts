import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CardSelectionService {
  private hasSelectedCardSubject = new BehaviorSubject<boolean>(false);
  public hasSelectedCard$: Observable<boolean> = this.hasSelectedCardSubject.asObservable();

  constructor() {}

  /**
   * Mark that a card has been selected
   */
  setCardSelected(selected: boolean = true): void {
    this.hasSelectedCardSubject.next(selected);
  }

  /**
   * Check if a card has been selected
   */
  hasCardBeenSelected(): boolean {
    return this.hasSelectedCardSubject.getValue();
  }

  /**
   * Reset the selection state (e.g., when navigating away from the prompt-content)
   */
  resetSelectionState(): void {
    this.hasSelectedCardSubject.next(false);
  }
}
