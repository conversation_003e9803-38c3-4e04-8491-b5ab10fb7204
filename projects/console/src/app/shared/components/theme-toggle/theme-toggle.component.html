<div class="theme-toggle-container">
  <!-- Add will-change attribute for better browser optimization -->
  <button
    class="theme-toggle-button"
    [class.dark]="currentTheme === 'dark'"
    [attr.aria-label]="
      'Switch to ' + (currentTheme === 'light' ? 'dark' : 'light') + ' theme'
    "
    title="Toggle theme"
    (click)="toggleTheme()"
  >
    <!-- Sun icon for light theme - simplified SVG for better performance -->
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="sun-icon"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      aria-hidden="true"
    >
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>

    <!-- Moon icon for dark theme - simplified SVG for better performance -->
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="moon-icon"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      aria-hidden="true"
    >
      <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
    </svg>

    <!-- Slider background - single element for better performance -->
    <div class="slider"></div>
  </button>
</div>
