name: Azure Static Web Apps CI/CD

trigger:
  branches:
    include:
      - main
  paths:
    include:
      - "projects/experience-studio/*"
      - "package.json"
      - "package-lock.json"
      - "angular.json"
      - "tsconfig.json"

variables:
  - group: elder-wand-variables # Variable group containing shared variables
  - name: npm_config_cache
    value: $(Pipeline.Workspace)/.npm

jobs:
  - job: build_experience_studio
    displayName: "Build Experience Studio"
    pool:
      vmImage: "ubuntu-latest"
    steps:
      - template: templates/node-build-steps.yml

      - script: |
          # Replace environment variables in the production environment file
          sed -i "s|\$(ELDER_WAND_URL)|$(ELDER_WAND_URL)|g" projects/experience-studio/src/environments/environment.prod.ts
          sed -i "s|\$(API_URL)|$(API_URL)|g" projects/experience-studio/src/environments/environment.prod.ts

          # Build with environment variables
          ELDER_WAND_URL="$(ELDER_WAND_URL)" npm run build:experience-studio
          echo "Checking build output directory:"
          ls -la dist
          echo "Checking experience-studio build directory:"
          ls -la dist/experience-studio
        displayName: "Build Experience Studio"
        env:
          NODE_ENV: production
          ELDER_WAND_URL: $(ELDER_WAND_URL)
          API_URL: $(API_URL)
          NODE_AUTH_TOKEN: $(Azure_DevOPS_PAT)

      - task: CopyFiles@2
        inputs:
          sourceFolder: "dist/experience-studio"
          contents: "**/*"
          targetFolder: "$(Build.ArtifactStagingDirectory)/experience-studio"
        displayName: "Copy Build Artifacts"

      - task: PublishBuildArtifacts@1
        inputs:
          pathToPublish: "$(Build.ArtifactStagingDirectory)/experience-studio"
          artifactName: "experience-studio"
        displayName: "Publish Build Artifacts"

  - job: deploy_experience_studio
    displayName: "Deploy Experience Studio"
    dependsOn: build_experience_studio
    condition: succeeded()
    pool:
      vmImage: "ubuntu-latest"
    steps:
      - checkout: self
        displayName: "Checkout Repository"

      - download: current
        artifact: "experience-studio"
        displayName: "Download Build Artifacts"

      - script: |
          echo "Current directory:"
          pwd
          echo "Pipeline workspace:"
          echo $(Pipeline.Workspace)
          echo "Checking downloaded artifacts:"
          ls -la $(Pipeline.Workspace)/experience-studio
          echo "Checking working directory:"
          ls -la $(Pipeline.Workspace)/s
          echo "Checking root directory:"
          ls -la /
        displayName: "Verify Artifacts"

      - script: |
          # Create deployment directory
          mkdir -p $(Pipeline.Workspace)/deploy
          # Copy artifacts to deployment directory
          cp -r $(Pipeline.Workspace)/experience-studio/* $(Pipeline.Workspace)/deploy/
          echo "Checking deployment directory:"
          $(Pipeline.Workspace)/deploy
          ls -la $(Pipeline.Workspace)/deploy
        displayName: "Prepare Deployment Directory"

      - task: AzureStaticWebApp@0
        inputs:
          azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_EXPERIENCE_STUDIO)
          app_location: "/"
          cwd: "$(Pipeline.Workspace)/deploy"
          output_location: ""
          skip_app_build: true
          api_location: ""
          deployment_source: "local"
        displayName: "Deploy Experience Studio to Azure Static Web Apps"
