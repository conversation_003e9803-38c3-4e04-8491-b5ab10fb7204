import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CdkDragDrop, DragDropModule, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { HeadingComponent } from '@awe/play-comp-library';
import { EditDialogComponent, DialogConfig, DropdownItem } from '../../components/edit-dialog/edit-dialog.component';

interface FeatureCard {
  id: string;
  title: string;
  description: string;
  tags: string[];
  impact: number; // 0-100 for progress bar
  priority: number; // 0-100 for progress bar
}

interface FeatureSection {
  id: string;
  title: string;
  subtitle: string;
  features: FeatureCard[];
}

@Component({
  selector: 'app-swot-analysis',
  standalone: true,
  imports: [CommonModule, EditDialogComponent, HeadingComponent, DragDropModule],
  templateUrl: './swot-analysis.component.html',
  styleUrl: './swot-analysis.component.scss'
})
export class SwotAnalysisComponent  implements OnInit {
  roboBallIcon: string = '/icons/robo_ball.svg';
  threeDotsIcon: string = 'icons/three-dot.svg';
  trashIcon: string = '/icons/awe_trash.svg';
  editIcon: string = '/icons/awe_edit.svg';

  // Dialog properties
  isDialogVisible: boolean = false;
  dialogConfig: DialogConfig = {
    showHeader: true,
    showFooter: true,
    showCloseButton: true,
    title: 'Edit SWOT Item',
    width: '600px',
    backdrop: true
  };

  dropdownItems: DropdownItem[] = [
    { label: 'Edit', action: 'edit', icon: this.editIcon },
    { label: 'Delete', action: 'delete', icon: this.trashIcon }
  ];

  currentEditingFeature: FeatureCard | null = null;
  openDropdownId: string | null = null;

  sections: FeatureSection[] = [
    {
      id: 'strengths',
      title: 'S',
      subtitle: 'STRENGTHS',
      features: [
        {
          id: 'strength-1',
          title: 'Unique Biometric Authentication',
          description: '',
          tags: ['Impact', 'Security', 'Innovation'],
          impact: 85,
          priority: 90
        },
        {
          id: 'strength-2',
          title: 'Advanced Security Features',
          description: 'Multi-layer security with biometric verification and encryption',
          tags: ['Security', 'Technology', 'Trust'],
          impact: 92,
          priority: 88
        }
      ]
    },
    {
      id: 'weaknesses',
      title: 'W',
      subtitle: 'WEAKNESSES',
      features: [
        {
          id: 'weakness-1',
          title: 'High Manufacturing Cost',
          description: 'Biometric sensors increase production costs significantly',
          tags: ['Cost', 'Manufacturing', 'Budget'],
          impact: 75,
          priority: 80
        },
        {
          id: 'weakness-2',
          title: 'Limited Merchant Adoption',
          description: 'Requires specialized terminals for biometric verification',
          tags: ['Adoption', 'Infrastructure', 'Market'],
          impact: 70,
          priority: 85
        }
      ]
    },
    {
      id: 'opportunities',
      title: 'O',
      subtitle: 'OPPORTUNITIES',
      features: [
        {
          id: 'opportunity-1',
          title: 'Growing Security Concerns',
          description: 'Increasing demand for secure payment methods due to fraud',
          tags: ['Market', 'Security', 'Demand'],
          impact: 88,
          priority: 92
        },
        {
          id: 'opportunity-2',
          title: 'Digital Payment Growth',
          description: 'Rapid expansion of contactless payment adoption globally',
          tags: ['Growth', 'Digital', 'Global'],
          impact: 82,
          priority: 78
        }
      ]
    },
    {
      id: 'threats',
      title: 'T',
      subtitle: 'THREATS',
      features: [
        {
          id: 'threat-1',
          title: 'Competitive Technology',
          description: 'Alternative authentication methods like facial recognition',
          tags: ['Competition', 'Technology', 'Innovation'],
          impact: 65,
          priority: 75
        },
        {
          id: 'threat-2',
          title: 'Privacy Regulations',
          description: 'Strict biometric data protection laws may limit adoption',
          tags: ['Regulation', 'Privacy', 'Compliance'],
          impact: 78,
          priority: 82
        }
      ]
    }
  ];

  constructor() {}

  getSectionIds(): string[] {
    return this.sections.map(section => section.id);
  }

  onNext(): void {
    // Handle next button click
  }

  onDrop(event: CdkDragDrop<FeatureCard[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );
    }
  }

  addNewFeature(sectionId: string): void {
    const section = this.sections.find(s => s.id === sectionId);
    if (section) {
      const newId = `${sectionId}-${section.features.length + 1}`;
      section.features.push({
        id: newId,
        title: 'New SWOT Item',
        description: 'Add description here',
        tags: ['Tag'],
        impact: 50,
        priority: 50
      });
    }
  }

  deleteFeature(sectionId: string, featureId: string): void {
    const section = this.sections.find(s => s.id === sectionId);
    if (section) {
      section.features = section.features.filter(f => f.id !== featureId);
    }
  }

  toggleDropdown(featureId: string, event: Event): void {
    event.stopPropagation();

    // If clicking on the same dropdown, close it
    if (this.openDropdownId === featureId) {
      this.openDropdownId = null;
    } else {
      // Open the clicked dropdown and close others
      this.openDropdownId = featureId;
    }
  }

  isDropdownOpen(featureId: string): boolean {
    return this.openDropdownId === featureId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  // Dialog methods
  openEditDialog(feature: FeatureCard): void {
    this.currentEditingFeature = feature;
    this.dialogConfig.title = `Edit SWOT Item: ${feature.title}`;
    this.isDialogVisible = true;
  }

  closeDialog(): void {
    this.isDialogVisible = false;
    this.currentEditingFeature = null;
  }

  onDialogDropdownAction(action: string): void {
    if (!this.currentEditingFeature) return;

    switch (action) {
      case 'edit':
        console.log('Edit feature:', this.currentEditingFeature);
        // Implement edit logic here
        break;
      case 'delete':
        this.deleteFeatureById(this.currentEditingFeature.id);
        this.closeDialog();
        break;
      default:
        console.log('Unknown action:', action);
    }
  }

  private deleteFeatureById(featureId: string): void {
    for (const section of this.sections) {
      const featureIndex = section.features.findIndex(f => f.id === featureId);
      if (featureIndex !== -1) {
        section.features.splice(featureIndex, 1);
        break;
      }
    }
  }

  // Method to open dialog from card menu
  openDialogFromCard(feature: FeatureCard): void {
    this.openEditDialog(feature);
  }

  ngOnInit(): void {
    // Add click listener to close dropdowns when clicking outside
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown')) {
        this.closeAllDropdowns();
      }
    });
  }

  // Helper methods for progress bars
  getImpactWidth(feature: FeatureCard): string {
    return `${feature.impact}%`;
  }

  getPriorityWidth(feature: FeatureCard): string {
    return `${feature.priority}%`;
  }

  getImpactColor(impact: number): string {
    if (impact >= 80) return '#868686'; // Green
    if (impact >= 60) return '#868686'; // Yellow
    return '#868686'; // Red
  }

  getPriorityColor(priority: number): string {
    if (priority >= 80) return '#868686'; // Blue
    if (priority >= 60) return '#868686'; // Purple
    return '#868686'; // Gray
  }
}