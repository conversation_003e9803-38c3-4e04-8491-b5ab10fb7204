import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

import { CreateCardComponent } from '../../../shared/components/create-card/create-card.component';
import { DataCardComponent } from '../../../shared/components/data-card/data-card.component';
import { FilterBarComponent } from '../../../shared/components/filter-bar/filter-bar.component';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import SearchBar from '../../../shared/components/search-bar/search-bar.component';

import { MOCK_KNOWLEDGE_BASE } from '../../../shared/mock-data/knowledge-base-mock-data';
import { CardData } from '../../../shared/models/card.model';
import { FilterConfig } from '../../../shared/models/filter.model';
import { FilterService } from '../../../shared/services/filter.service';
import { PaginationService } from '../../../shared/services/pagination.service';

@Component({
  selector: 'app-knowledge-base',
  standalone: true,
  imports: [
    CommonModule,
    CreateCardComponent,
    DataCardComponent,
    FilterBarComponent,
    PageFooterComponent,
    SearchBar
  ],
  templateUrl: './knowledge-base.component.html',
  styleUrls: ['./knowledge-base.component.scss']
})
export class KnowledgeBaseComponent implements OnInit {
  // Data
  allKnowledgeBase: CardData[] = MOCK_KNOWLEDGE_BASE;
  filteredKnowledgeBase: CardData[] = [];
  displayedKnowledgeBase: CardData[] = [];
  
  // Filter
  filterConfig!: FilterConfig;
  showFilterBar = false;
  searchTerm = '';
  
  // Pagination
  currentPage = 1;
  itemsPerPage = 12;
  
  // Map filter IDs to data properties for filtering
  private filterPropertyMap: {[key: string]: string} = {
    'userType': 'userType',
    'client': 'client',
    'department': 'department',
    'role': 'role',
    'project': 'project',
    'category': 'category'
  };
  
  constructor(
    private filterService: FilterService,
    private paginationService: PaginationService,
    private router: Router
  ) {}
  
  ngOnInit(): void {
    this.filterConfig = this.filterService.getFilterConfig('workflows'); // Reuse workflows filter config for now
    this.filteredKnowledgeBase = [...this.allKnowledgeBase];
    this.updateDisplayedKnowledgeBase();
  }
  
  updateDisplayedKnowledgeBase(): void {
    const paginationResult = this.paginationService.getPaginatedItems(
      this.filteredKnowledgeBase,
      this.currentPage,
      this.itemsPerPage
    );
    
    this.displayedKnowledgeBase = paginationResult.displayedItems;
  }
  
  onCreateKnowledgeBase(): void {
    console.log('Create Knowledge Base clicked');
    // Navigate to create knowledge base page
    this.router.navigate(['/libraries/knowledge-base/create']);
  }
  
  onCardClicked(knowledgeBaseId: string): void {
    console.log('Knowledge Base clicked:', knowledgeBaseId);
    // Navigate to knowledge base details page
    // this.router.navigate([`/launch/libraries/knowledge-base/${knowledgeBaseId}`]);
  }
  
  onActionClicked(event: {action: string, cardId: string}): void {
    console.log('Action clicked:', event.action, 'on card ID:', event.cardId);
    // Handle different actions (execute, clone, delete)
  }
  
  toggleFilterBar(): void {
    this.showFilterBar = !this.showFilterBar;
  }
  
  onFilterChange(filters: {[key: string]: string}): void {
    this.filteredKnowledgeBase = this.filterService.filterData(
      this.allKnowledgeBase,
      filters,
      this.filterPropertyMap
    );
    
    // Reset pagination when filters change
    this.currentPage = 1;
    this.updateDisplayedKnowledgeBase();
  }
  
  onSearch(term: string): void {
    this.searchTerm = term;
    // Simple search implementation - filter by title containing the search term
    if (term.trim() === '') {
      this.filteredKnowledgeBase = [...this.allKnowledgeBase];
    } else {
      this.filteredKnowledgeBase = this.allKnowledgeBase.filter(item => 
        item.title.toLowerCase().includes(term.toLowerCase())
      );
    }
    
    // Reset pagination when search term changes
    this.currentPage = 1;
    this.updateDisplayedKnowledgeBase();
  }
  
  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedKnowledgeBase();
  }
}
