import { Component } from '@angular/core';
import { Agent } from '../../shared/interfaces/agent-list.interface';
import { agentsData } from '../../../assets/data/agents.mock';
import { AgentsComponent } from '../../shared/components/agents/agents.component';

@Component({
  selector: 'app-agents-filter',
  imports: [AgentsComponent],
  templateUrl: './agents-filter.component.html',
  styleUrl: './agents-filter.component.scss',
})
export class AgentsFilterComponent {
  agentsData: Agent[] = agentsData;
}
