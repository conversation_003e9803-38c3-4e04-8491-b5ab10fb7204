import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { createLogger } from '../utils/logger';
import { FileTreePersistenceService } from './file-tree-persistence.service';

export interface MonacoEditorState {
  filePath: string;
  content: string;
  cursorPosition: {
    lineNumber: number;
    column: number;
  };
  scrollPosition: {
    scrollTop: number;
    scrollLeft: number;
  };
  selectionRange?: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  lastModified: number;
  isDirty: boolean;
}

export interface MonacoModelState {
  modelUri: string;
  content: string;
  language: string;
  isDirty: boolean;
  lastSavedContent: string;
  lastModified: number;
}

@Injectable({
  providedIn: 'root'
})
export class MonacoStateManagementService {
  private logger = createLogger('MonacoStateManagementService');

  // Track editor states for each file
  private editorStates$ = new BehaviorSubject<Map<string, MonacoEditorState>>(new Map());

  // Track model states
  private modelStates$ = new BehaviorSubject<Map<string, MonacoModelState>>(new Map());

  // Track content changes
  private contentChanges$ = new Subject<{ filePath: string; content: string; isDirty: boolean }>();

  // Track which files have unsaved changes
  private dirtyFiles$ = new BehaviorSubject<Set<string>>(new Set());

  // Storage keys
  private readonly EDITOR_STATES_KEY = 'monaco_editor_states';
  private readonly MODEL_STATES_KEY = 'monaco_model_states';
  private readonly DIRTY_FILES_KEY = 'monaco_dirty_files';

  constructor(private fileTreePersistenceService: FileTreePersistenceService) {
    this.loadPersistedState();
    this.setupContentChangeHandler();
  }

  /**
   * Public observables
   */
  get editorStates(): Observable<Map<string, MonacoEditorState>> {
    return this.editorStates$.asObservable();
  }

  get modelStates(): Observable<Map<string, MonacoModelState>> {
    return this.modelStates$.asObservable();
  }

  get contentChanges(): Observable<{ filePath: string; content: string; isDirty: boolean }> {
    return this.contentChanges$.asObservable();
  }

  get dirtyFiles(): Observable<Set<string>> {
    return this.dirtyFiles$.asObservable();
  }

  /**
   * Record editor state for a file
   */
  recordEditorState(filePath: string, state: Partial<MonacoEditorState>): void {
    const normalizedPath = this.normalizeFilePath(filePath);
    const editorStates = this.editorStates$.value;

    const currentState = editorStates.get(normalizedPath) || {
      filePath: normalizedPath,
      content: '',
      cursorPosition: { lineNumber: 1, column: 1 },
      scrollPosition: { scrollTop: 0, scrollLeft: 0 },
      lastModified: Date.now(),
      isDirty: false
    };

    const updatedState: MonacoEditorState = {
      ...currentState,
      ...state,
      filePath: normalizedPath,
      lastModified: Date.now()
    };

    editorStates.set(normalizedPath, updatedState);
    this.editorStates$.next(editorStates);

    this.persistState();
    this.logger.info(`📝 Recorded editor state for: ${normalizedPath}`);
  }

  /**
   * Record content change and mark file as dirty
   */
  recordContentChange(filePath: string, content: string, originalContent?: string): void {
    const normalizedPath = this.normalizeFilePath(filePath);
    const isDirty = originalContent ? content !== originalContent : true;

    // Update editor state
    this.recordEditorState(normalizedPath, { content, isDirty });

    // Update dirty files tracking
    const dirtyFiles = this.dirtyFiles$.value;
    if (isDirty) {
      dirtyFiles.add(normalizedPath);
    } else {
      dirtyFiles.delete(normalizedPath);
    }
    this.dirtyFiles$.next(dirtyFiles);

    // Record user edit in file tree persistence service
    if (isDirty) {
      this.fileTreePersistenceService.recordUserEdit(
        normalizedPath,
        content,
        'User edit in Monaco Editor'
      );
    }

    // Emit content change
    this.contentChanges$.next({ filePath: normalizedPath, content, isDirty });

    this.persistState();
    this.logger.info(`✏️ Content change recorded for: ${normalizedPath} (dirty: ${isDirty})`);
  }

  /**
   * Record model state
   */
  recordModelState(modelUri: string, state: Partial<MonacoModelState>): void {
    const modelStates = this.modelStates$.value;

    const currentState = modelStates.get(modelUri) || {
      modelUri,
      content: '',
      language: 'plaintext',
      isDirty: false,
      lastSavedContent: '',
      lastModified: Date.now()
    };

    const updatedState: MonacoModelState = {
      ...currentState,
      ...state,
      lastModified: Date.now()
    };

    modelStates.set(modelUri, updatedState);
    this.modelStates$.next(modelStates);

    this.persistState();
    this.logger.info(`📄 Recorded model state for: ${modelUri}`);
  }

  /**
   * Get editor state for a file
   */
  getEditorState(filePath: string): MonacoEditorState | null {
    const normalizedPath = this.normalizeFilePath(filePath);
    return this.editorStates$.value.get(normalizedPath) || null;
  }

  /**
   * Get model state
   */
  getModelState(modelUri: string): MonacoModelState | null {
    return this.modelStates$.value.get(modelUri) || null;
  }

  /**
   * Check if file has unsaved changes
   */
  isFileDirty(filePath: string): boolean {
    const normalizedPath = this.normalizeFilePath(filePath);
    return this.dirtyFiles$.value.has(normalizedPath);
  }

  /**
   * Mark file as saved (clean)
   */
  markFileAsSaved(filePath: string): void {
    const normalizedPath = this.normalizeFilePath(filePath);
    const dirtyFiles = this.dirtyFiles$.value;
    dirtyFiles.delete(normalizedPath);
    this.dirtyFiles$.next(dirtyFiles);

    // Update editor state
    this.recordEditorState(normalizedPath, { isDirty: false });

    this.persistState();
    this.logger.info(`💾 Marked file as saved: ${normalizedPath}`);
  }

  /**
   * Get all dirty files
   */
  getDirtyFiles(): string[] {
    return Array.from(this.dirtyFiles$.value);
  }

  /**
   * Clear all dirty states (typically after regeneration)
   */
  clearAllDirtyStates(): void {
    this.dirtyFiles$.next(new Set());

    // Update all editor states to not dirty
    const editorStates = this.editorStates$.value;
    for (const [, state] of editorStates.entries()) {
      state.isDirty = false;
    }
    this.editorStates$.next(editorStates);

    this.persistState();
    this.logger.info('🧹 Cleared all dirty states');
  }

  /**
   * Restore editor state for a file
   */
  restoreEditorState(filePath: string, editor: any): void {
    const state = this.getEditorState(filePath);
    if (!state || !editor) {
      return;
    }

    try {
      // Restore cursor position
      if (state.cursorPosition) {
        editor.setPosition(state.cursorPosition);
      }

      // Restore scroll position
      if (state.scrollPosition) {
        editor.setScrollPosition(state.scrollPosition);
      }

      // Restore selection if exists
      if (state.selectionRange) {
        editor.setSelection(state.selectionRange);
      }

      this.logger.info(`🔄 Restored editor state for: ${filePath}`);
    } catch (error) {
      this.logger.error(`❌ Failed to restore editor state for ${filePath}:`, error);
    }
  }

  /**
   * Clear all persisted state
   */
  clearPersistedState(): void {
    sessionStorage.removeItem(this.EDITOR_STATES_KEY);
    sessionStorage.removeItem(this.MODEL_STATES_KEY);
    sessionStorage.removeItem(this.DIRTY_FILES_KEY);

    this.editorStates$.next(new Map());
    this.modelStates$.next(new Map());
    this.dirtyFiles$.next(new Set());

    this.logger.info('🗑️ Cleared all Monaco state');
  }

  /**
   * Setup content change handler
   */
  private setupContentChangeHandler(): void {
    // Listen to content changes and automatically record them
    this.contentChanges$.subscribe(change => {
      this.logger.info(`📝 Content change detected: ${change.filePath} (dirty: ${change.isDirty})`);
    });
  }

  /**
   * Normalize file path
   */
  private normalizeFilePath(filePath: string): string {
    if (!filePath) return '';

    return filePath
      .replace(/\\/g, '/')
      .replace(/^\/+/, '')
      .replace(/\/+$/, '')
      .toLowerCase();
  }

  /**
   * Persist state to sessionStorage
   */
  private persistState(): void {
    try {
      const editorStates = this.editorStates$.value;
      const modelStates = this.modelStates$.value;
      const dirtyFiles = this.dirtyFiles$.value;

      // Convert Maps and Sets to serializable formats
      const editorStatesObj = Object.fromEntries(editorStates);
      const modelStatesObj = Object.fromEntries(modelStates);
      const dirtyFilesArray = Array.from(dirtyFiles);

      sessionStorage.setItem(this.EDITOR_STATES_KEY, JSON.stringify(editorStatesObj));
      sessionStorage.setItem(this.MODEL_STATES_KEY, JSON.stringify(modelStatesObj));
      sessionStorage.setItem(this.DIRTY_FILES_KEY, JSON.stringify(dirtyFilesArray));

      this.logger.info('💾 Monaco state persisted to sessionStorage');
    } catch (error) {
      this.logger.error('❌ Failed to persist Monaco state:', error);
    }
  }

  /**
   * Load persisted state from sessionStorage
   */
  private loadPersistedState(): void {
    try {
      const editorStatesData = sessionStorage.getItem(this.EDITOR_STATES_KEY);
      const modelStatesData = sessionStorage.getItem(this.MODEL_STATES_KEY);
      const dirtyFilesData = sessionStorage.getItem(this.DIRTY_FILES_KEY);

      if (editorStatesData) {
        const editorStatesObj = JSON.parse(editorStatesData);
        const editorStatesMap = new Map<string, MonacoEditorState>(Object.entries(editorStatesObj));
        this.editorStates$.next(editorStatesMap);
        this.logger.info(`📂 Loaded ${editorStatesMap.size} editor states from sessionStorage`);
      }

      if (modelStatesData) {
        const modelStatesObj = JSON.parse(modelStatesData);
        const modelStatesMap = new Map<string, MonacoModelState>(Object.entries(modelStatesObj));
        this.modelStates$.next(modelStatesMap);
        this.logger.info(`📂 Loaded ${modelStatesMap.size} model states from sessionStorage`);
      }

      if (dirtyFilesData) {
        const dirtyFilesArray = JSON.parse(dirtyFilesData) as string[];
        const dirtyFilesSet = new Set(dirtyFilesArray);
        this.dirtyFiles$.next(dirtyFilesSet);
        this.logger.info(`📂 Loaded ${dirtyFilesSet.size} dirty files from sessionStorage`);
      }

      this.logger.info('✅ Monaco state management service initialized');
    } catch (error) {
      this.logger.error('❌ Failed to load persisted Monaco state:', error);
    }
  }
}
