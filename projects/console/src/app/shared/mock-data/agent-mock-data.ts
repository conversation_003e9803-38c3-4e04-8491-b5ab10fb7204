import { CardData } from '../models/card.model';

export const MOCK_AGENTS: CardData[] = [
  {
    id: '1',
    title: 'Spring Boot Documentation',
    tags: [
      { label: 'Individual' },
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '2',
    title: 'Gaia Cloud Foundry to Azure Kubernetes Service',
    tags: [
      { label: 'Individual' },
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'cloud-transform'
  },
  {
    id: '3',
    title: 'BNY_SOW_Risk_Assessment_Agent',
    tags: [
      { label: 'Individual' },
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'client1',
    department: 'data-analytics',
    role: 'solution-architect',
    project: 'data-migration'
  },
  {
    id: '4',
    title: 'BNY_SOW_Cost_Estimation_Agent',
    tags: [
      { label: 'Individual' },
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'organization',
    client: 'client2',
    department: 'cloud-services',
    role: 'tech-lead',
    project: 'chatbot-ai'
  },
  {
    id: '5',
    title: 'BNY_SOW_Compliance_Agent',
    tags: [
      { label: 'Individual' },
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'client3',
    department: 'ui-ux',
    role: 'developer',
    project: 'revamp-demo'
  },
  {
    id: '6',
    title: 'API Documentation Generator',
    tags: [
      { label: 'Team' },
      { label: 'Microsoft' },
      { label: 'DevOps' },
      { label: 'Tech Lead' },
      { label: 'API Gateway' }
    ],
    createdDate: '2/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'microsoft',
    department: 'devops',
    role: 'tech-lead',
    project: 'api-gateway'
  },
  {
    id: '7',
    title: 'AWS Infrastructure Analyzer',
    tags: [
      { label: 'Organization' },
      { label: 'Amazon' },
      { label: 'Cloud Services' },
      { label: 'Solution Architect' },
      { label: 'Cost Optimization' }
    ],
    createdDate: '3/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'organization',
    client: 'amazon',
    department: 'cloud-services',
    role: 'solution-architect',
    project: 'cost-optimization'
  },
  {
    id: '8',
    title: 'React Component Library',
    tags: [
      { label: 'Individual' },
      { label: 'Facebook' },
      { label: 'UI/UX' },
      { label: 'Developer' },
      { label: 'Design System' }
    ],
    createdDate: '2/28/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'facebook',
    department: 'ui-ux',
    role: 'developer',
    project: 'design-system'
  },
  {
    id: '9',
    title: 'ML Model Training Assistant',
    tags: [
      { label: 'Team' },
      { label: 'Google' },
      { label: 'Data Analytics' },
      { label: 'Data Scientist' },
      { label: 'TensorFlow' }
    ],
    createdDate: '1/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'google',
    department: 'data-analytics',
    role: 'data-scientist',
    project: 'tensorflow'
  },
  {
    id: '10',
    title: 'Security Compliance Checker',
    tags: [
      { label: 'Organization' },
      { label: 'JP Morgan' },
      { label: 'Security' },
      { label: 'Security Engineer' },
      { label: 'Compliance' }
    ],
    createdDate: '3/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'organization',
    client: 'jpmorgan',
    department: 'security',
    role: 'security-engineer',
    project: 'compliance'
  },
  {
    id: '11',
    title: 'Database Migration Tool',
    tags: [
      { label: 'Team' },
      { label: 'Oracle' },
      { label: 'Database' },
      { label: 'DBA' },
      { label: 'Migration' }
    ],
    createdDate: '2/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'oracle',
    department: 'database',
    role: 'dba',
    project: 'migration'
  },
  {
    id: '12',
    title: 'Kubernetes Deployment Validator',
    tags: [
      { label: 'Individual' },
      { label: 'RedHat' },
      { label: 'DevOps' },
      { label: 'DevOps Engineer' },
      { label: 'Kubernetes' }
    ],
    createdDate: '1/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'redhat',
    department: 'devops',
    role: 'devops-engineer',
    project: 'kubernetes'
  },
  {
    id: '13',
    title: 'Mobile App Performance Analyzer',
    tags: [
      { label: 'Team' },
      { label: 'Apple' },
      { label: 'Mobile Development' },
      { label: 'Performance Engineer' },
      { label: 'iOS' }
    ],
    createdDate: '3/1/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'apple',
    department: 'mobile-development',
    role: 'performance-engineer',
    project: 'ios'
  },
  {
    id: '14',
    title: 'GraphQL Schema Generator',
    tags: [
      { label: 'Individual' },
      { label: 'Shopify' },
      { label: 'API Development' },
      { label: 'Backend Developer' },
      { label: 'GraphQL' }
    ],
    createdDate: '2/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'shopify',
    department: 'api-development',
    role: 'backend-developer',
    project: 'graphql'
  },
  {
    id: '15',
    title: 'Microservice Architecture Advisor',
    tags: [
      { label: 'Organization' },
      { label: 'Netflix' },
      { label: 'Architecture' },
      { label: 'Solution Architect' },
      { label: 'Microservices' }
    ],
    createdDate: '1/25/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'organization',
    client: 'netflix',
    department: 'architecture',
    role: 'solution-architect',
    project: 'microservices'
  },
  {
    id: '16',
    title: 'CI/CD Pipeline Optimizer',
    tags: [
      { label: 'Team' },
      { label: 'GitLab' },
      { label: 'DevOps' },
      { label: 'DevOps Engineer' },
      { label: 'CI/CD' }
    ],
    createdDate: '3/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'gitlab',
    department: 'devops',
    role: 'devops-engineer',
    project: 'ci-cd'
  },
  {
    id: '17',
    title: 'Blockchain Smart Contract Validator',
    tags: [
      { label: 'Individual' },
      { label: 'Ethereum' },
      { label: 'Blockchain' },
      { label: 'Blockchain Developer' },
      { label: 'Smart Contracts' }
    ],
    createdDate: '2/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'ethereum',
    department: 'blockchain',
    role: 'blockchain-developer',
    project: 'smart-contracts'
  },
  {
    id: '18',
    title: 'Accessibility Compliance Checker',
    tags: [
      { label: 'Team' },
      { label: 'Adobe' },
      { label: 'UI/UX' },
      { label: 'Accessibility Specialist' },
      { label: 'WCAG' }
    ],
    createdDate: '1/30/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'adobe',
    department: 'ui-ux',
    role: 'accessibility-specialist',
    project: 'wcag'
  },
  {
    id: '19',
    title: 'Data Warehouse Schema Designer',
    tags: [
      { label: 'Organization' },
      { label: 'Snowflake' },
      { label: 'Data Engineering' },
      { label: 'Data Engineer' },
      { label: 'Data Warehouse' }
    ],
    createdDate: '3/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'organization',
    client: 'snowflake',
    department: 'data-engineering',
    role: 'data-engineer',
    project: 'data-warehouse'
  },
  {
    id: '20',
    title: 'IoT Device Management System',
    tags: [
      { label: 'Team' },
      { label: 'Siemens' },
      { label: 'IoT' },
      { label: 'IoT Engineer' },
      { label: 'Device Management' }
    ],
    createdDate: '2/25/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'siemens',
    department: 'iot',
    role: 'iot-engineer',
    project: 'device-management'
  },
  {
    id: '21',
    title: 'Natural Language Processing Toolkit',
    tags: [
      { label: 'Individual' },
      { label: 'OpenAI' },
      { label: 'AI/ML' },
      { label: 'ML Engineer' },
      { label: 'NLP' }
    ],
    createdDate: '1/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'openai',
    department: 'ai-ml',
    role: 'ml-engineer',
    project: 'nlp'
  },
  {
    id: '22',
    title: 'Serverless Function Optimizer',
    tags: [
      { label: 'Team' },
      { label: 'AWS' },
      { label: 'Cloud Services' },
      { label: 'Cloud Architect' },
      { label: 'Lambda' }
    ],
    createdDate: '3/25/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'aws',
    department: 'cloud-services',
    role: 'cloud-architect',
    project: 'lambda'
  },
  {
    id: '23',
    title: 'Test Automation Framework',
    tags: [
      { label: 'Organization' },
      { label: 'Salesforce' },
      { label: 'QA' },
      { label: 'QA Engineer' },
      { label: 'Automation' }
    ],
    createdDate: '2/12/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'organization',
    client: 'salesforce',
    department: 'qa',
    role: 'qa-engineer',
    project: 'automation'
  },
  {
    id: '24',
    title: 'Legacy Code Modernization Assistant',
    tags: [
      { label: 'Individual' },
      { label: 'IBM' },
      { label: 'Application Modernization' },
      { label: 'Modernization Specialist' },
      { label: 'Legacy' }
    ],
    createdDate: '1/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'ibm',
    department: 'application-modernization',
    role: 'modernization-specialist',
    project: 'legacy'
  },
  {
    id: '25',
    title: 'Real-time Analytics Dashboard',
    tags: [
      { label: 'Team' },
      { label: 'Tableau' },
      { label: 'Data Analytics' },
      { label: 'Data Analyst' },
      { label: 'Dashboard' }
    ],
    createdDate: '3/8/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'tableau',
    department: 'data-analytics',
    role: 'data-analyst',
    project: 'dashboard'
  },
  {
    id: '26',
    title: 'API Gateway Security Analyzer',
    tags: [
      { label: 'Organization' },
      { label: 'Cisco' },
      { label: 'Security' },
      { label: 'Security Architect' },
      { label: 'API Security' }
    ],
    createdDate: '2/18/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'organization',
    client: 'cisco',
    department: 'security',
    role: 'security-architect',
    project: 'api-security'
  },
  {
    id: '27',
    title: 'Progressive Web App Converter',
    tags: [
      { label: 'Individual' },
      { label: 'Google' },
      { label: 'Web Development' },
      { label: 'Frontend Developer' },
      { label: 'PWA' }
    ],
    createdDate: '1/22/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'google',
    department: 'web-development',
    role: 'frontend-developer',
    project: 'pwa'
  },
  {
    id: '28',
    title: 'Database Query Optimizer',
    tags: [
      { label: 'Team' },
      { label: 'MongoDB' },
      { label: 'Database' },
      { label: 'Database Engineer' },
      { label: 'Query Optimization' }
    ],
    createdDate: '3/12/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'mongodb',
    department: 'database',
    role: 'database-engineer',
    project: 'query-optimization'
  },
  {
    id: '29',
    title: 'Containerization Migration Assistant',
    tags: [
      { label: 'Organization' },
      { label: 'Docker' },
      { label: 'DevOps' },
      { label: 'Container Specialist' },
      { label: 'Containerization' }
    ],
    createdDate: '2/8/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'organization',
    client: 'docker',
    department: 'devops',
    role: 'container-specialist',
    project: 'containerization'
  },
  {
    id: '30',
    title: 'Event-Driven Architecture Designer',
    tags: [
      { label: 'Individual' },
      { label: 'Confluent' },
      { label: 'Architecture' },
      { label: 'Solution Architect' },
      { label: 'Kafka' }
    ],
    createdDate: '1/18/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'confluent',
    department: 'architecture',
    role: 'solution-architect',
    project: 'kafka'
  },
  {
    id: '31',
    title: 'Cloud Cost Optimization Advisor',
    tags: [
      { label: 'Team' },
      { label: 'Microsoft' },
      { label: 'Cloud Services' },
      { label: 'FinOps Engineer' },
      { label: 'Cost Optimization' }
    ],
    createdDate: '3/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'microsoft',
    department: 'cloud-services',
    role: 'finops-engineer',
    project: 'cost-optimization'
  },
  {
    id: '32',
    title: 'DevSecOps Implementation Guide',
    tags: [
      { label: 'Organization' },
      { label: 'Atlassian' },
      { label: 'DevOps' },
      { label: 'DevSecOps Engineer' },
      { label: 'Security Integration' }
    ],
    createdDate: '2/22/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'organization',
    client: 'atlassian',
    department: 'devops',
    role: 'devsecops-engineer',
    project: 'security-integration'
  },
  {
    id: '33',
    title: 'Microservices Communication Analyzer',
    tags: [
      { label: 'Individual' },
      { label: 'Uber' },
      { label: 'Architecture' },
      { label: 'Microservices Architect' },
      { label: 'Service Mesh' }
    ],
    createdDate: '1/28/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'individual',
    client: 'uber',
    department: 'architecture',
    role: 'microservices-architect',
    project: 'service-mesh'
  },
  {
    id: '34',
    title: 'Infrastructure as Code Validator',
    tags: [
      { label: 'Team' },
      { label: 'HashiCorp' },
      { label: 'DevOps' },
      { label: 'Infrastructure Engineer' },
      { label: 'Terraform' }
    ],
    createdDate: '3/18/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Agent' },
      { icon: 'clone', action: 'clone', tooltip: 'Clone Agent' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Agent' }
    ],
    // Filter properties
    userType: 'team',
    client: 'hashicorp',
    department: 'devops',
    role: 'infrastructure-engineer',
    project: 'terraform'
  }
]; 