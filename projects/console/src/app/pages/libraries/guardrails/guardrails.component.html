<div class="guardrails-container">
  <!-- Header section -->
  <div class="guardrails-header">
    <div class="header-content">
      <div class="search-section">
        <app-search-bar></app-search-bar>
      </div>
      <div class="action-buttons">
        <button
          class="action-button"
          [class.active-filter]="isFilterBarVisible"
          (click)="toggleFilterBar()"
        >
          Filters
          <!-- Down arrow when closed, Up arrow when open -->
          <svg
            *ngIf="!isFilterBarVisible"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7 10L12 15L17 10"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <svg
            *ngIf="isFilterBarVisible"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7 15L12 10L17 15"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Filter Bar -->
  <div class="filter-section" *ngIf="isFilterBarVisible">
    <app-filter-bar
      [filterConfig]="guardrailFilterConfig"
      (filterChange)="onFilterChange($event)"
    >
    </app-filter-bar>
  </div>

  <div class="cards-container">
    <!-- Create Guardrail Card - show only on first page -->
    <app-create-card
      *ngIf="showCreateCard"
      label="Create Guardrail"
      (cardClick)="onCreateGuardrail()"
    >
    </app-create-card>

    <!-- No results message -->
    <div class="no-results" *ngIf="filteredGuardrails.length === 0">
      No guardrails found matching your criteria
    </div>

    <!-- Guardrail Data Cards - Now showing paginated cards -->
    <app-data-card
      *ngFor="let guardrail of displayedGuardrails"
      [data]="guardrail"
      (cardClicked)="onCardClicked($event)"
      (actionClicked)="onActionClicked($event)"
    >
    </app-data-card>
  </div>

  <!-- Page Footer with Pagination - use totalPages from the service -->
  <app-page-footer
    *ngIf="filteredGuardrails.length > 0"
    [totalItems]="filteredGuardrails.length + 1"
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  ></app-page-footer>
</div>
