<div class="toast-item" [ngClass]="[type, theme + '-theme']" [@toastAnimation]="animationState">
  <div class="toast-border-animation"></div>
  <div class="toast-content">
    <div class="toast-icon" [innerHTML]="getIcon()"></div>
    <div class="toast-text">
      <div class="toast-message">{{ message }}</div>
    </div>
    <div class="toast-close" (click)="close()">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
  </div>
</div>
