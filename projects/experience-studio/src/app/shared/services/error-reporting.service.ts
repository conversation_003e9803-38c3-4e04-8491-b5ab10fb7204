import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { createLogger } from '../utils/logger';
import { HttpErrorHelperService } from './error-handling/http-error-helper.service';

/**
 * Interface for error reporting request payload
 */
export interface ErrorReportRequest {
  error: string;
}

/**
 * Interface for error reporting response
 */
export interface ErrorReportResponse {
  success: boolean;
  message?: string;
  status?: string;
}

/**
 * Service for reporting build errors to the backend
 * Used by the retry mechanism when BUILD status is FAILED
 */
@Injectable({
  providedIn: 'root'
})
export class ErrorReportingService {
  private readonly http = inject(HttpClient);
  private readonly httpErrorHelper = inject(HttpErrorHelperService);
  private readonly logger = createLogger('ErrorReportingService');

  private readonly apiUrl = environment.apiUrl;
  private readonly errorEndpoint = '/error/build';

  /**
   * Report a build or deploy error to the backend
   * @param projectId The project ID
   * @param statusId The status ID (job ID)
   * @param errorMessage The error message to report
   * @returns Observable of the error report response
   */
  reportBuildError(
    projectId: string,
    statusId: string,
    errorMessage: string
  ): Observable<ErrorReportResponse> {
    if (!projectId || !statusId) {
      this.logger.error('Cannot report build/deploy error: missing project ID or status ID', {
        projectId,
        statusId
      });
      return throwError(() => new Error('Missing required parameters: projectId and statusId are required'));
    }

    if (!errorMessage || errorMessage.trim() === '') {
      this.logger.warn('Empty error message provided, using default message');
      errorMessage = 'Build or deploy process failed. No specific error details available.';
    }

    // Set up query parameters
    const params = new HttpParams()
      .set('projectid', projectId)
      .set('status_id', statusId);

    // Set up request payload
    const payload: ErrorReportRequest = {
      error: errorMessage.trim()
    };

    this.logger.info('Reporting build/deploy error to backend', {
      projectId,
      statusId,
      errorMessageLength: errorMessage.length,
      endpoint: this.apiUrl + this.errorEndpoint
    });

    return this.http.post<ErrorReportResponse>(
      this.apiUrl + this.errorEndpoint,
      payload,
      { params }
    ).pipe(
      map(response => {
        this.logger.info('Build/deploy error reported successfully', {
          projectId,
          statusId,
          response
        });

        return {
          success: true,
          message: response.message || 'Error reported successfully',
          status: response.status || 'success'
        };
      }),
      catchError(error => {
        this.logger.error('Failed to report build/deploy error', {
          projectId,
          statusId,
          error,
          errorMessage: errorMessage.substring(0, 100) + '...'
        });

        // Return a successful response even when error reporting fails
        // This allows the retry mechanism to continue smoothly
        return of({
          success: false,
          message: 'Failed to report build/deploy error. Retry will continue without error reporting.',
          status: 'error'
        });
      })
    );
  }

  /**
   * Report build or deploy error with automatic error message extraction from polling response
   * @param projectId The project ID
   * @param statusId The status ID (job ID)
   * @param pollingResponse The last polling response containing error details
   * @returns Observable of the error report response
   */
  reportBuildErrorFromPollingResponse(
    projectId: string,
    statusId: string,
    pollingResponse: any
  ): Observable<ErrorReportResponse> {
    let errorMessage = '';

    try {
      // Extract error message from polling response
      if (pollingResponse?.details?.log) {
        const logContent = pollingResponse.details.log;

        if (typeof logContent === 'string' && logContent.includes('{') && logContent.includes('}')) {
          try {
            const parsedLog = JSON.parse(logContent);
            errorMessage = parsedLog.message || JSON.stringify(parsedLog);
          } catch (e) {
            errorMessage = logContent;
          }
        } else {
          errorMessage = logContent;
        }
      } else if (pollingResponse?.log) {
        // For new workflow format
        const logContent = pollingResponse.log;
        if (typeof logContent === 'string' && logContent.includes('{') && logContent.includes('}')) {
          try {
            const parsedLog = JSON.parse(logContent);
            errorMessage = parsedLog.message || JSON.stringify(parsedLog);
          } catch (e) {
            errorMessage = logContent;
          }
        } else {
          errorMessage = logContent;
        }
      } else if (pollingResponse?.details?.progress_description) {
        errorMessage = pollingResponse.details.progress_description;
      } else {
        errorMessage = 'Build or deploy process failed. No specific error details available in polling response.';
      }
    } catch (error) {
      this.logger.error('Error extracting error message from polling response:', error);
      errorMessage = 'Build or deploy process failed. Error occurred while extracting error details.';
    }

    return this.reportBuildError(projectId, statusId, errorMessage);
  }

  /**
   * Validate if the provided parameters are sufficient for error reporting
   * @param projectId The project ID
   * @param statusId The status ID
   * @returns True if parameters are valid, false otherwise
   */
  validateErrorReportingParameters(projectId: string, statusId: string): boolean {
    const isValid = !!(projectId && projectId.trim() && statusId && statusId.trim());

    if (!isValid) {
      this.logger.warn('Invalid error reporting parameters', {
        hasProjectId: !!(projectId && projectId.trim()),
        hasStatusId: !!(statusId && statusId.trim())
      });
    }

    return isValid;
  }
}
