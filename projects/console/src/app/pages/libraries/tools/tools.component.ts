import { Component, OnInit } from '@angular/core';
import { CommonModule } from "@angular/common";
import { Router, ActivatedRoute } from '@angular/router';
import SearchBar from '../../../shared/components/search-bar/search-bar.component';
import { CreateCardComponent } from '../../../shared/components/create-card/create-card.component';
import { DataCardComponent } from '../../../shared/components/data-card/data-card.component';
import { CardData } from '../../../shared/models/card.model';
import { MOCK_TOOLS } from '../../../shared/mock-data/tool-mock-data';
import { FilterBarComponent } from '../../../shared/components/filter-bar/filter-bar.component';
import { FilterService } from '../../../shared/services/filter.service';
import { FilterConfig } from '../../../shared/models/filter.model';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';

@Component({
  selector: 'app-tools',
  standalone: true,
  imports: [
    CommonModule, 
    SearchBar, 
    CreateCardComponent, 
    DataCardComponent, 
    FilterBarComponent,
    PageFooterComponent
  ],
  templateUrl: './tools.component.html',
  styleUrl: './tools.component.scss'
})
export class ToolsComponent implements OnInit {
  // Get tools from mock data
  allTools: CardData[] = MOCK_TOOLS;
  filteredTools: CardData[] = [];
  displayedTools: CardData[] = [];
  toolsFilterConfig!: FilterConfig;
  
  // Track filter bar visibility
  isFilterBarVisible: boolean = false;
  
  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 12; // Show 12 cards per page
  totalPages: number = 1;
  
  // Map filter IDs to CardData properties
  private filterPropertyMap: {[key: string]: string} = {
    'userType': 'userType',
    'client': 'client',
    'department': 'department',
    'category': 'category',
    'role': 'role',
    'project': 'project'
  };

  constructor(
    private filterService: FilterService,
    private paginationService: PaginationService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    // Get the filter configuration for tools
    this.toolsFilterConfig = this.filterService.getFilterConfig('tools');
    this.filteredTools = [...this.allTools];
    
    // Check for page parameter in the URL
    const pageParam = this.route.snapshot.queryParamMap.get('page');
    if (pageParam) {
      this.currentPage = parseInt(pageParam, 10);
    }
    
    this.updateDisplayedTools();
  }

  updateDisplayedTools(): void {
    // Use the pagination service to get displayed items and total pages
    const result = this.paginationService.getPaginatedItems(
      this.filteredTools,
      this.currentPage,
      this.itemsPerPage
    );
    
    this.displayedTools = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreateTool(): void {
    console.log('Create Tool clicked');
    // Navigate to create tool page
    this.router.navigate(['/libraries/tools/create']);
  }

  onCardClicked(toolId: string): void {
    console.log(`Tool card clicked: ${toolId}`);
    // Navigate to edit tool page
    this.router.navigate(['/libraries/tools/edit', toolId], {
      queryParams: { returnPage: this.currentPage }
    });
  }

  onActionClicked(event: {action: string, cardId: string}): void {
    console.log(`Action ${event.action} clicked for tool: ${event.cardId}`);
    
    // Handle different actions
    if (event.action === 'execute') {
      console.log(`Executing tool: ${event.cardId}`);
      this.router.navigate(['/libraries/tools/edit', event.cardId], { 
        queryParams: { execute: 'true', returnPage: this.currentPage } 
      });
    } else if (event.action === 'delete') {
      console.log(`Deleting tool: ${event.cardId}`);
      // Implement delete functionality
    }
  }

  toggleFilterBar(): void {
    this.isFilterBarVisible = !this.isFilterBarVisible;
    console.log('Filter bar visibility:', this.isFilterBarVisible);
  }

  onFilterChange(filters: {[key: string]: string}): void {
    // Apply filters to tools
    if (Object.keys(filters).length === 0) {
      this.filteredTools = [...this.allTools];
    } else {
      this.filteredTools = this.filterService.filterData(
        this.allTools, 
        filters, 
        this.filterPropertyMap
      );
    }
    
    // Reset to first page when filters change
    this.currentPage = 1;
    this.updateDisplayedTools();
    
    console.log('Applied filters:', filters);
    console.log('Filtered tools count:', this.filteredTools.length);
  }
  
  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedTools();
    console.log('Page changed to:', this.currentPage);
    
    // Update URL with page parameter
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page: this.currentPage },
      queryParamsHandling: 'merge'
    });
  }
  
  // Helper to check if we should show the create card (only on first page)
  get showCreateCard(): boolean {
    return this.currentPage === 1;
  }
}
