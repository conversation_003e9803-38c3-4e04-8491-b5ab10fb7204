import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  CdkDragDrop,
  DragDropModule,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import { ButtonComponent, CaptionComponent, HeadingComponent, IconsComponent } from '@awe/play-comp-library';
import { AweCardComponent } from '../../components/awe-card/awe-card.component';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';

interface FeatureCard {
  id: string;
  title: string;
  description: string;
  tags: string[];
}

interface FeatureSection {
  id: string;
  title: string; // 'Mo', 'S', 'Co', 'W'
  subtitle: string; // 'MUST HAVE', 'SHOULD HAVE', etc.
  features: FeatureCard[];
}

@Component({
  selector: 'app-feature-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DragDropModule,
    HeadingComponent,
    AweCardComponent,
    IconsComponent,
    AweModalComponent,
    ButtonComponent
  ],
  templateUrl: './feature-list.component.html',
  styleUrls: ['./feature-list.component.scss'], // Corrected property name
})
export class FeatureListComponent implements OnInit {
  // ... (rest of your existing TS code is fine) ...
  roboBallIcon: string = '/icons/robo_ball.svg'; // Not used in current HTML, but keep if needed later
  threeDotsIcon: string = 'icons/three-dot.svg';

  // Modal State
  isEditModalOpen = false;
  selectedFeatureForEdit: FeatureCard | null = null;
  isAddingNewFeature = false; // Track if we're adding new or editing existing
  currentSectionId: string = ''; // Track which section we're adding to
  // Create a working copy for editing to avoid direct mutation until save
  editableFeatureTitle: string = '';
  editableFeatureDescription: string = '';
  editableFeatureTags: string[] = [];
  regeneratePrompt: string = '';

  openDropdownId: string | null = null;

  sections: FeatureSection[] = [
    {
      id: 'must-have',
      title: 'Mo',
      subtitle: 'MUST HAVE',
      features: [
        {
          id: 'must-1',
          title: 'Contactless Payment Capability',
          description:
            'Allows user to make contactless payments using the biometric card Allows user to make contactless payments using the biometric cardAllows user to make contactless payments using the biometric cardAllows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology'],
        },
        {
          id: 'must-2',
          title: 'Secure Payment Processing',
          description:
            'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology'],
        },
      ],
    },
    {
      id: 'should-have',
      title: 'S',
      subtitle: 'SHOULD HAVE',
      features: [
        {
          id: 'should-1',
          title: 'Contactless Payment Capability',
          description:
            'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology'],
        },
        {
          id: 'should-2',
          title: 'Secure Payment Processing',
          description:
            'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology'],
        },
      ],
    },
    {
      id: 'could-have',
      title: 'Co',
      subtitle: 'COULD HAVE',
      features: [
        {
          id: 'could-1',
          title: 'Contactless Payment Capability',
          description:
            'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology'],
        },
      ],
    },
    {
      id: 'wont-have',
      title: 'W',
      subtitle: "WON'T HAVE",
      features: [
        {
          id: 'wont-1',
          title: 'Contactless Payment Capability',
          description:
            'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology'],
        },
        {
          id: 'wont-2',
          title: 'Secure Payment Processing',
          description:
            'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology'],
        },
      ],
    },
  ];

  constructor(private cdRef: ChangeDetectorRef) {}

  ngOnInit(): void {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-menu-container')) {
        // Check for a common parent for dropdown button & menu
        this.closeAllDropdowns();
      }
    });
  }

  getSectionIds(): string[] {
    return this.sections.map((section) => section.id);
  }

  onDrop(event: CdkDragDrop<FeatureCard[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );
    }
  }

  addNewFeature(sectionId: string): void {
    // Open modal for adding new feature
    this.isAddingNewFeature = true;
    this.currentSectionId = sectionId;
    this.selectedFeatureForEdit = null;
    // Clear editable data for new feature
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [''];
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
  }

  deleteFeature(sectionId: string, featureId: string): void {
    const section = this.sections.find((s) => s.id === sectionId);
    if (section) {
      section.features = section.features.filter((f) => f.id !== featureId);
    }
    this.closeAllDropdowns(); // Ensure dropdown closes after deletion
  }

  toggleDropdown(featureId: string, event: Event): void {
    event.stopPropagation();
    this.openDropdownId = this.openDropdownId === featureId ? null : featureId;
  }

  isDropdownOpen(featureId: string): boolean {
    return this.openDropdownId === featureId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  openEditModal(feature: FeatureCard): void {
    this.isAddingNewFeature = false; // We're editing, not adding
    this.selectedFeatureForEdit = { ...feature }; // Edit a copy
    this.currentSectionId = ''; // Not needed for editing
    // Set up editable data
    this.editableFeatureTitle = feature.title;
    this.editableFeatureDescription = feature.description;
    this.editableFeatureTags = [...feature.tags]; // Copy array
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
    this.closeAllDropdowns();
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedFeatureForEdit = null;
    this.isAddingNewFeature = false;
    this.currentSectionId = '';
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [];
    this.regeneratePrompt = '';
  }

  updateFeature(): void {
    // Validate required fields
    if (!this.editableFeatureTitle.trim()) {
      alert('Please enter a feature title');
      return;
    }

    if (this.isAddingNewFeature) {
      // Adding new feature
      const section = this.sections.find((s) => s.id === this.currentSectionId);
      if (section) {
        const newId = `${this.currentSectionId}-feature-${Date.now()}`;
        const newFeature: FeatureCard = {
          id: newId,
          title: this.editableFeatureTitle.trim(),
          description:
            this.editableFeatureDescription.trim() || 'No description provided',
          tags: this.editableFeatureTags
            .filter((tag) => tag.trim() !== '')
            .map((tag) => tag.trim()),
        };
        section.features.push(newFeature);
      }
    } else {
      // Updating existing feature
      if (!this.selectedFeatureForEdit) return;

      // Find the feature in the sections and update it
      for (const section of this.sections) {
        const featureIndex = section.features.findIndex(
          (f) => f.id === this.selectedFeatureForEdit!.id,
        );
        if (featureIndex !== -1) {
          section.features[featureIndex] = {
            ...section.features[featureIndex],
            title: this.editableFeatureTitle.trim(),
            description:
              this.editableFeatureDescription.trim() ||
              'No description provided',
            tags: this.editableFeatureTags
              .filter((tag) => tag.trim() !== '')
              .map((tag) => tag.trim()),
          };
          break;
        }
      }
    }

    this.closeEditModal();
  }

  // Methods for managing editable tags in the modal
  addEditableTag(): void {
    this.editableFeatureTags.push(''); // Add a new empty string to edit
    this.cdRef.detectChanges(); // Ensure ngFor updates
    setTimeout(() => {
      const inputs = document.querySelectorAll('.edit-tag-input');
      const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
      if (lastInput) {
        lastInput.focus();
      }
    });
  }

  removeEditableTag(index: number): void {
    this.editableFeatureTags.splice(index, 1);
  }

  // Required for ngFor with [(ngModel)] on primitive types like string
  trackByFn(index: number, _item: any): any {
    return index;
  }

  // This method is for direct delete from card, called by (click) on delete button
  handleDeleteFeatureFromCard(sectionId: string, featureId: string) {
    this.deleteFeature(sectionId, featureId);
    this.closeAllDropdowns();
  }
}
