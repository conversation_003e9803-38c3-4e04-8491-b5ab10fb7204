import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * Service to manage the state of the vertical stepper component across the application
 * This allows for better state management and coordination between components
 */
@Injectable({
  providedIn: 'root'
})
export class StepperStateService {
  // Subject to track whether the stepper should be reset
  private resetStepperSubject = new BehaviorSubject<boolean>(false);
  
  // Subject to track the current project ID
  private currentProjectIdSubject = new BehaviorSubject<string>('');
  
  // Observable to expose the reset stepper state
  public resetStepper$: Observable<boolean> = this.resetStepperSubject.asObservable();
  
  // Observable to expose the current project ID
  public currentProjectId$: Observable<string> = this.currentProjectIdSubject.asObservable();
  
  constructor() { }
  
  /**
   * Triggers a reset of the stepper component
   * This should be called when starting a new project or when the stepper needs to be reset
   */
  public triggerStepperReset(): void {
    // Set to true to trigger a reset
    this.resetStepperSubject.next(true);
    
    // Reset back to false after a short delay to allow components to react
    setTimeout(() => {
      this.resetStepperSubject.next(false);
    }, 100);
  }
  
  /**
   * Sets the current project ID
   * This helps track when a new project is started
   * @param projectId The ID of the current project
   */
  public setCurrentProjectId(projectId: string): void {
    // Only trigger a reset if the project ID has changed
    const currentId = this.currentProjectIdSubject.getValue();
    if (currentId !== projectId) {
      this.currentProjectIdSubject.next(projectId);
      
      // If we're switching to a new project, trigger a stepper reset
      if (currentId && projectId) {
        this.triggerStepperReset();
      }
    }
  }
  
  /**
   * Gets the current project ID
   * @returns The current project ID
   */
  public getCurrentProjectId(): string {
    return this.currentProjectIdSubject.getValue();
  }
  
  /**
   * Clears the current project ID
   * This should be called when no project is active
   */
  public clearCurrentProjectId(): void {
    this.currentProjectIdSubject.next('');
    this.triggerStepperReset();
  }
}
