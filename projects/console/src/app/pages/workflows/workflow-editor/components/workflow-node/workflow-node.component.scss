.workflow-node {
  min-width: 180px;
  background-color: var(--card-bg);
  border-radius: 8px;
  border: 2px solid var(--agent-card-border);
  overflow: hidden;
  box-shadow: 0 2px 5px var(--card-shadow);
  position: relative;
  
  &.selected {
    border-color: var(--dashboard-primary);
    box-shadow: 0 0 0 2px var(--dashboard-shadow-hover);
  }
  
  .node-header {
    background-color: var(--agent-table-header-bg);
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--agent-card-border);
    
    .node-title {
      font-weight: 500;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: var(--text-color);
    }
    
    .delete-btn {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      padding: 0;
      color: var(--text-secondary);
      cursor: pointer;
      
      &:hover {
        color: var(--delete-icon-color);
      }
      
      svg {
        width: 14px;
        height: 14px;
      }
    }
  }
  
  .node-body {
    padding: 10px 12px;
    
    .node-description {
      margin: 0;
      font-size: 12px;
      color: var(--text-secondary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 200px;
    }
  }
  
  .node-handles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    
    .handle {
      position: absolute;
      width: 12px;
      height: 12px;
      background-color: var(--dashboard-primary);
      border: 2px solid var(--card-bg);
      border-radius: 50%;
      pointer-events: all;
      
      &.handle-left {
        top: 50%;
        left: -6px;
        transform: translateY(-50%);
        cursor: crosshair;
      }
      
      &.handle-right {
        top: 50%;
        right: -6px;
        transform: translateY(-50%);
        cursor: crosshair;
      }
    }
  }
} 