import { Component, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from "@angular/core";
import { CommonModule } from "@angular/common";
import { IconsComponent } from "@awe/play-comp-library";

@Component({
  selector: 'app-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrls: ['./search-bar.component.scss'],
  standalone: true,
  imports: [CommonModule, IconsComponent],
})
export default class SearchBar {
  placeholderTexts: string[] = [
    "What's in your mind?",
    "How can I assist you with the work?",
    "How can I make your day more productive?",
    "What's in your mind?" 
  ];

  constructor() {
    // To achieve the seamless loop, we'll duplicate the first placeholder text
    // in the array if it's not empty. This effectively creates the 4th "slide"
    // needed for the continuous animation without a visible snap back.
    if (this.placeholderTexts.length > 0) {
      this.placeholderTexts.push(this.placeholderTexts[0]);
    }
  }
}