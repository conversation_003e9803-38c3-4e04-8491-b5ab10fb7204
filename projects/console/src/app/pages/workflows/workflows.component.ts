import { Component, OnInit } from '@angular/core';
import { CommonModule } from "@angular/common";
import { Router } from '@angular/router';
import SearchBar from '../../shared/components/search-bar/search-bar.component';
import { PageFooterComponent } from '../../shared/components/page-footer/page-footer.component';
import { FilterBarComponent } from '../../shared/components/filter-bar/filter-bar.component';
import { CreateCardComponent } from '../../shared/components/create-card/create-card.component';
import { DataCardComponent } from '../../shared/components/data-card/data-card.component';
import { FilterService } from '../../shared/services/filter.service';
import { PaginationService } from '../../shared/services/pagination.service';
import { MOCK_WORKFLOWS } from '../../shared/mock-data/workflow-mock-data';
import { CardData } from '../../shared/models/card.model';
import { FilterConfig } from '../../shared/models/filter.model';

@Component({
  selector: 'app-workflows',
  standalone: true,
  imports: [
    CommonModule, 
    SearchBar, 
    PageFooterComponent, 
    FilterBarComponent,
    CreateCardComponent,
    DataCardComponent
  ],
  templateUrl: './workflows.component.html',
  styleUrl: './workflows.component.scss'
})
export class WorkflowsComponent implements OnInit {
  // Data
  allWorkflows: CardData[] = MOCK_WORKFLOWS;
  filteredWorkflows: CardData[] = [];
  displayedWorkflows: CardData[] = [];
  
  // Filter
  workflowFilterConfig!: FilterConfig;
  isFilterBarVisible: boolean = false;
  
  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 12; // Show 12 cards per page
  totalPages: number = 1;
  
  // Filter property mapping
  private filterPropertyMap: {[key: string]: string} = {
    'category': 'category',
    'client': 'client',
    'department': 'department',
    'role': 'role',
    'project': 'project',
    'userType': 'userType'
  };
  
  constructor(
    private filterService: FilterService,
    private paginationService: PaginationService,
    private router: Router
  ) {}
  
  ngOnInit(): void {
    this.workflowFilterConfig = this.filterService.getFilterConfig('workflows');
    this.filteredWorkflows = [...this.allWorkflows];
    this.updateDisplayedWorkflows();
  }
  
  updateDisplayedWorkflows(): void {
    const paginationResult = this.paginationService.getPaginatedItems(
      this.filteredWorkflows,
      this.currentPage,
      this.itemsPerPage
    );
    
    this.displayedWorkflows = paginationResult.displayedItems;
    this.totalPages = paginationResult.totalPages;
  }
  
  onCreateWorkflow(): void {
    // Navigate to workflow creation page
    console.log('Create workflow clicked');
    this.router.navigate(['/launch/workflows/create']);
  }
  
  onCardClicked(workflowId: string): void {
    console.log('Workflow card clicked:', workflowId);
    this.router.navigate(['/launch/workflows/edit', workflowId]);
  }
  
  onActionClicked(event: {action: string, cardId: string}): void {
    console.log('Action clicked:', event);
    // Handle different actions (execute, clone, delete)
    if (event.action === 'execute') {
      console.log('Executing workflow:', event.cardId);
      // Navigate to workflow execution page
      this.router.navigate(['/launch/workflows/execute', event.cardId]);
    } else if (event.action === 'clone') {
      console.log('Cloning workflow:', event.cardId);
      // Implement workflow cloning logic
    } else if (event.action === 'delete') {
      console.log('Deleting workflow:', event.cardId);
      // Implement workflow deletion logic
    }
  }
  
  toggleFilterBar(): void {
    this.isFilterBarVisible = !this.isFilterBarVisible;
    console.log('Filter bar visibility:', this.isFilterBarVisible);
  }
  
  onFilterChange(filters: {[key: string]: string}): void {
    console.log('Filters applied:', filters);
    
    // Apply filters to the workflows
    this.filteredWorkflows = this.filterService.filterData(
      this.allWorkflows,
      filters,
      this.filterPropertyMap
    );
    
    // Reset to first page when filters change
    this.currentPage = 1;
    this.updateDisplayedWorkflows();
  }
  
  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedWorkflows();
  }
  
  get showCreateCard(): boolean {
    return this.currentPage === 1;
  }
} 