export interface CardTag {
  label: string;
  type?: 'primary' | 'secondary'; // For styling differences
}

export interface CardAction {
  icon: string;  // Path to SVG icon or identifier
  action: string; // Identifier for the action 
  tooltip?: string;
}

export interface CardData {
  id: string;
  title: string;
  tags: CardTag[]; // Tags for categorization
  createdDate: string; // Creation date
  actions?: CardAction[]; // Action buttons
  
  // Filter properties
  userType?: string;
  client?: string;
  department?: string;
  role?: string;
  project?: string;
  category?: string; // For prompt categorization
} 