import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

interface CodeFile {
  name: string;
  path: string;
  content: string;
  language: string;
}

@Component({
  selector: 'app-code-viewer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './code-viewer.component.html',
  styleUrls: ['./code-viewer.component.scss']
})
export class CodeViewerComponent implements OnChanges {
  @Input() files: CodeFile[] = [];
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() showFileExplorer = true;
  
  selectedFile: CodeFile | null = null;
  
  ngOnChanges(changes: SimpleChanges) {
    if (changes['files'] && this.files.length > 0 && !this.selectedFile) {
      this.selectedFile = this.files[0];
    }
  }
  
  selectFile(file: CodeFile) {
    this.selectedFile = file;
  }
  
  getLanguageClass(language: string): string {
    return `language-${language}`;
  }
  
  getFileIcon(filename: string): string {
    if (filename.endsWith('.html') || filename.endsWith('.htm')) {
      return '📄 '; // HTML file
    } else if (filename.endsWith('.css')) {
      return '🎨 '; // CSS file
    } else if (filename.endsWith('.js')) {
      return '⚙️ '; // JavaScript file
    } else if (filename.endsWith('.json')) {
      return '📋 '; // JSON file
    } else if (filename.endsWith('.md')) {
      return '📝 '; // Markdown file
    } else if (filename.endsWith('.ts')) {
      return '📘 '; // TypeScript file
    } else {
      return '📄 '; // Default file icon
    }
  }
}