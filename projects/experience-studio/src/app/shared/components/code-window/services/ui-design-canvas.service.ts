import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { createLogger } from '../../../utils/logger';

export interface CanvasViewport {
  zoom: number;
  x: number;
  y: number;
  isDragging: boolean;
  lastMouseX: number;
  lastMouseY: number;
}

export interface CanvasPoint {
  x: number;
  y: number;
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignCanvasService {
  private readonly logger = createLogger('UIDesignCanvasService');

  // Canvas viewport state - Using reference viewport values for perfect alignment
  private readonly viewport$ = new BehaviorSubject<CanvasViewport>({
    zoom: 0.5,        // 50% zoom as per reference
    x: 39.86,         // Position X from reference
    y: 82.95,         // Position Y from reference
    isDragging: false,
    lastMouseX: 0,
    lastMouseY: 0
  });

  // Canvas bounds and constraints
  private readonly minZoom = 0.1;
  private readonly maxZoom = 3.0;
  private readonly zoomStep = 0.1;

  constructor() {
    this.logger.info('UI Design Canvas Service initialized');
  }

  /**
   * Get viewport observable
   */
  get viewport(): Observable<CanvasViewport> {
    return this.viewport$.asObservable();
  }

  /**
   * Get current viewport state
   */
  getViewport(): CanvasViewport {
    return this.viewport$.value;
  }

  /**
   * Update viewport state
   */
  updateViewport(updates: Partial<CanvasViewport>): void {
    const current = this.viewport$.value;
    const updated = { ...current, ...updates };
    this.viewport$.next(updated);
  }

  /**
   * Start canvas dragging
   */
  startDragging(mouseX: number, mouseY: number): void {
    this.updateViewport({
      isDragging: true,
      lastMouseX: mouseX,
      lastMouseY: mouseY
    });
  }

  /**
   * Stop canvas dragging
   */
  stopDragging(): void {
    this.updateViewport({
      isDragging: false
    });
  }

  /**
   * Pan the canvas
   */
  panCanvas(deltaX: number, deltaY: number): void {
    const current = this.viewport$.value;
    this.updateViewport({
      x: current.x + deltaX,
      y: current.y + deltaY
    });
  }

  /**
   * Zoom to specific level
   */
  zoomToLevel(newZoom: number, centerPoint?: CanvasPoint): void {
    const clampedZoom = Math.max(this.minZoom, Math.min(this.maxZoom, newZoom));
    const current = this.viewport$.value;

    if (centerPoint) {
      // Zoom to specific point
      const zoomRatio = clampedZoom / current.zoom;
      const newX = centerPoint.x - (centerPoint.x - current.x) * zoomRatio;
      const newY = centerPoint.y - (centerPoint.y - current.y) * zoomRatio;

      this.updateViewport({
        zoom: clampedZoom,
        x: newX,
        y: newY
      });
    } else {
      // Zoom to center
      this.updateViewport({
        zoom: clampedZoom
      });
    }
  }

  /**
   * Zoom in
   */
  zoomIn(centerPoint?: CanvasPoint): void {
    const current = this.viewport$.value;
    const newZoom = Math.min(this.maxZoom, current.zoom + this.zoomStep);
    this.zoomToLevel(newZoom, centerPoint);
  }

  /**
   * Zoom out
   */
  zoomOut(centerPoint?: CanvasPoint): void {
    const current = this.viewport$.value;
    const newZoom = Math.max(this.minZoom, current.zoom - this.zoomStep);
    this.zoomToLevel(newZoom, centerPoint);
  }

  /**
   * Reset viewport to reference state
   * Uses reference viewport values for perfect alignment
   */
  resetViewport(): void {
    this.viewport$.next({
      zoom: 0.5,        // 50% zoom as per reference
      x: 39.86,         // Position X from reference
      y: 82.95,         // Position Y from reference
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0
    });
  }

  /**
   * Get CSS transform string for canvas
   */
  getTransformStyle(): string {
    const viewport = this.viewport$.value;
    return `translate(${viewport.x}px, ${viewport.y}px) scale(${viewport.zoom})`;
  }

  /**
   * Get zoom percentage
   */
  getZoomPercentage(): number {
    return Math.round(this.viewport$.value.zoom * 100);
  }

  /**
   * Convert screen coordinates to canvas coordinates
   */
  screenToCanvas(screenPoint: CanvasPoint, containerRect: DOMRect): CanvasPoint {
    const viewport = this.viewport$.value;
    return {
      x: (screenPoint.x - containerRect.left - viewport.x) / viewport.zoom,
      y: (screenPoint.y - containerRect.top - viewport.y) / viewport.zoom
    };
  }

  /**
   * Convert canvas coordinates to screen coordinates
   */
  canvasToScreen(canvasPoint: CanvasPoint, containerRect: DOMRect): CanvasPoint {
    const viewport = this.viewport$.value;
    return {
      x: canvasPoint.x * viewport.zoom + viewport.x + containerRect.left,
      y: canvasPoint.y * viewport.zoom + viewport.y + containerRect.top
    };
  }

  /**
   * Check if canvas is at minimum zoom
   */
  isAtMinZoom(): boolean {
    return this.viewport$.value.zoom <= this.minZoom;
  }

  /**
   * Check if canvas is at maximum zoom
   */
  isAtMaxZoom(): boolean {
    return this.viewport$.value.zoom >= this.maxZoom;
  }

  /**
   * Fit content to view
   */
  fitContentToView(contentBounds: { width: number; height: number }, containerSize: { width: number; height: number }): void {
    const padding = 50; // Padding around content
    const availableWidth = containerSize.width - padding * 2;
    const availableHeight = containerSize.height - padding * 2;

    const scaleX = availableWidth / contentBounds.width;
    const scaleY = availableHeight / contentBounds.height;
    const scale = Math.min(scaleX, scaleY, 1); // Don't zoom in beyond 100%

    const centerX = (containerSize.width - contentBounds.width * scale) / 2;
    const centerY = (containerSize.height - contentBounds.height * scale) / 2;

    this.updateViewport({
      zoom: scale,
      x: centerX,
      y: centerY
    });
  }

  /**
   * Clear service state
   */
  clear(): void {
    this.resetViewport();
    this.logger.info('UI Design Canvas Service cleared');
  }
}
