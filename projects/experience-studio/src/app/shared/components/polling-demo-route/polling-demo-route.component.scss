.polling-demo-route {
  display: flex;
  height: 100vh;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Left Panel - Demo Controls */
.demo-controls-panel {
  width: 400px;
  min-width: 400px;
  background: white;
  border-right: 1px solid #dee2e6;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.panel-header {
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecef;

  h2 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
  }
}

/* Cards */
.demo-status-card,
.main-controls,
.step-controls,
.current-state-card,
.data-preview-card,
.instructions-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 16px;

  h3 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
  }
}

/* Demo Status */
.status-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .label {
    font-weight: 500;
    color: #495057;
    font-size: 13px;
  }

  .value {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 3px;
    background: white;
    border: 1px solid #dee2e6;

    &.running {
      background: #d4edda;
      color: #155724;
      border-color: #c3e6cb;
    }

    &.stopped {
      background: #f8d7da;
      color: #721c24;
      border-color: #f5c6cb;
    }

    &.code {
      background: #e9ecef;
      color: #495057;
    }

    &.log {
      background: #2c3e50;
      color: #ecf0f1;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &.in_progress {
      background: #fff3cd;
      color: #856404;
      border-color: #ffeaa7;
    }

    &.completed {
      background: #d4edda;
      color: #155724;
      border-color: #c3e6cb;
    }

    &.failed {
      background: #f8d7da;
      color: #721c24;
      border-color: #f5c6cb;
    }
  }

  &.full-width {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;

    .value {
      width: 100%;
      word-break: break-word;
    }
  }
}

/* Control Buttons */
.control-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.demo-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.primary {
    background: #007bff;
    color: white;
    &:hover:not(:disabled) { background: #0056b3; }
  }

  &.secondary {
    background: #6c757d;
    color: white;
    &:hover:not(:disabled) { background: #545b62; }
  }

  &.success {
    background: #28a745;
    color: white;
    &:hover:not(:disabled) { background: #1e7e34; }
  }

  &.danger {
    background: #dc3545;
    color: white;
    &:hover:not(:disabled) { background: #c82333; }
  }

  &.warning {
    background: #ffc107;
    color: #212529;
    &:hover:not(:disabled) { background: #e0a800; }
  }

  &.small {
    padding: 6px 10px;
    font-size: 11px;
  }
}

/* Steps List */
.steps-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.step-item {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;

  &.active {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
  }

  &.step-completed {
    border-color: #28a745;
    background: #f8fff9;
  }

  &.step-failed {
    border-color: #dc3545;
    background: #fff8f8;
  }

  &.step-in-progress {
    border-color: #ffc107;
    background: #fffdf5;
  }
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.step-info {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  flex: 1;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #6c757d;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.step-details {
  h4 {
    margin: 0 0 4px 0;
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: #6c757d;
    font-size: 12px;
    line-height: 1.4;
  }
}

.step-status {
  .status-indicator {
    font-size: 16px;
  }
}

.step-controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.timeout-control {
  display: flex;
  align-items: center;
  gap: 6px;

  label {
    font-size: 11px;
    color: #6c757d;
    white-space: nowrap;
  }

  input {
    width: 50px;
    padding: 4px 6px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    font-size: 11px;
  }
}

.step-buttons {
  display: flex;
  gap: 6px;
}

/* State Grid */
.state-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Data Items */
.data-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;

  .label {
    font-weight: 500;
    color: #495057;
  }

  .value {
    font-family: 'Monaco', 'Menlo', monospace;
    background: white;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
  }
}

/* Instructions */
.instructions-card {
  ol {
    margin: 0 0 12px 0;
    padding-left: 20px;
    font-size: 13px;
    line-height: 1.5;

    li {
      margin-bottom: 6px;
      color: #495057;

      strong {
        color: #2c3e50;
      }
    }
  }

  .note {
    margin: 0;
    padding: 10px;
    background: #e3f2fd;
    border-left: 3px solid #2196f3;
    border-radius: 3px;
    font-size: 12px;
    line-height: 1.4;
    color: #1565c0;

    strong {
      color: #0d47a1;
    }
  }
}

/* Right Panel - Code Window */
.code-window-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;

  .panel-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;

    h3 {
      margin: 0 0 8px 0;
      color: #2c3e50;
      font-size: 18px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #6c757d;
      font-size: 14px;
    }
  }
}

.code-window-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .demo-controls-panel {
    width: 350px;
    min-width: 350px;
  }
}

@media (max-width: 768px) {
  .polling-demo-route {
    flex-direction: column;
    height: auto;
  }

  .demo-controls-panel {
    width: 100%;
    min-width: auto;
    max-height: 50vh;
  }

  .code-window-panel {
    min-height: 50vh;
  }
}

/* Tab States Display */
.tab-states {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tab-state {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9em;
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;

  &.enabled {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
    font-weight: 500;
  }
}

/* Project Info Display */
.project-info {
  .project-url {
    color: #6c757d;
    font-size: 0.9em;
  }
}

/* Status Indicators */
.status-in_progress {
  color: #ffc107 !important;
  font-weight: 500;
}

.status-completed {
  color: #28a745 !important;
  font-weight: 500;
}

.status-failed {
  color: #dc3545 !important;
  font-weight: 500;
}

.status-pending {
  color: #6c757d !important;
  font-weight: 500;
}
