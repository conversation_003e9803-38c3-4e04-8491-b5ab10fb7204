.theme-toggle-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle-button {
  position: relative;
  width: 60px;
  height: 30px;
  border-radius: 15px;
  border: none;
  padding: 0;
  cursor: pointer;
  background: transparent;
  overflow: hidden;
  transition: all 0.2s ease;
  transform: translateZ(0);
  will-change: transform;
  
  /* Ensure proper positioning for internal elements */
  display: flex;
  align-items: center;
  justify-content: space-around;
  
  /* Remove outline on click, but keep an accessible focus style */
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--input-focus-shadow);
  }
  
  /* Gradient slider background */
  .slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    border-radius: 15px;
    z-index: 0;
    opacity: 0.5;
    transition: opacity 0.2s ease;
  }
  
  /* Icons styling */
  .sun-icon, .moon-icon {
    width: 18px;
    height: 18px;
    z-index: 1;
    transition: opacity 0.2s ease, transform 0.2s ease;
    transform: translateZ(0);
    will-change: transform, opacity;
  }
  
  .sun-icon {
    color: #fad7a3;
    opacity: 1;
    transform: translateX(0);
    stroke-width: 2.5;
    filter: drop-shadow(0 0 1px rgba(255, 152, 0, 0.5));
  }
  
  .moon-icon {
    color: #b0c4de;
    opacity: 0;
    transform: translateX(-20px);
  }
  
  /* Dark theme styles */
  &.dark {
    .slider {
      background: var(--gradient-primary);
      opacity: 0.8;
    }
    
    .sun-icon {
      opacity: 0;
      transform: translateX(20px);
    }
    
    .moon-icon {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  /* Hover effects */
  &:hover {
    .slider {
      opacity: 0.7;
    }
    
    .sun-icon {
      filter: brightness(1.2);
    }
    
    .moon-icon {
      filter: brightness(1.2);
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .theme-toggle-button {
    width: 50px;
    height: 25px;
    
    .sun-icon, .moon-icon {
      width: 15px;
      height: 15px;
    }
  }
} 