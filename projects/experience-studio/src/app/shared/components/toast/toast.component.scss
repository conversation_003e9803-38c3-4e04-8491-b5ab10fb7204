@keyframes border-pulse {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 300% 0%;
  }
}

@keyframes text-glow {
  0% {
    text-shadow: 0 0 4px rgba(255, 255, 255, 0.1);
  }
  50% {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  }
  100% {
    text-shadow: 0 0 4px rgba(255, 255, 255, 0.1);
  }
}

@keyframes toast-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes toast-out {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

.toast-item {
  position: relative;
  display: flex;
  margin-bottom: 16px;
  border-radius: 8px;
  min-width: 300px;
  max-width: 400px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05);
  overflow: hidden;
  animation: toast-in 0.3s ease forwards;
  transform-origin: center bottom;

  &.closing {
    animation: toast-out 0.3s ease forwards;
  }

  .toast-border-animation {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    width: 100%;
    background: linear-gradient(90deg, transparent, currentColor, transparent);
    background-size: 300% 100%;
    animation: border-pulse 1.5s linear infinite;
    z-index: 2;
    filter: blur(0.5px);
    opacity: 0.9;
  }

  .toast-content {
    width: 100%;
    padding: 16px;
    display: flex;
    align-items: flex-start;
  }

  .toast-text {
    flex: 1;
  }

  .toast-message {
    font-size: 14px;
    line-height: 1.5;
    font-weight: 500;
    animation: text-glow 2s ease-in-out infinite;
  }

  &.light-theme {
    background-color: #ffffff;
    color: #333333;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);

    .toast-border-animation {
      opacity: 0.7;
    }

    &.success {
      background-color: #ffffff;
      color: #333333;
      border-left: 4px solid #01B48D;

      .toast-border-animation {
        background: linear-gradient(90deg, transparent, #01B48D, transparent);
        background-size: 300% 100%;
      }

      .toast-icon {
        color: #01B48D;
      }
    }

    &.error {
      background-color: #ffffff;
      color: #333333;
      border-left: 4px solid #F44336;

      .toast-border-animation {
        background: linear-gradient(90deg, transparent, #F44336, transparent);
        background-size: 300% 100%;
      }

      .toast-icon {
        color: #F44336;
      }
    }

    &.warning {
      background-color: #ffffff;
      color: #333333;
      border-left: 4px solid #FF9800;

      .toast-border-animation {
        background: linear-gradient(90deg, transparent, #FF9800, transparent);
        background-size: 300% 100%;
      }

      .toast-icon {
        color: #FF9800;
      }
    }

    &.info {
      background-color: #ffffff;
      color: #333333;
      border-left: 4px solid #2196F3;

      .toast-border-animation {
        background: linear-gradient(90deg, transparent, #2196F3, transparent);
        background-size: 300% 100%;
      }

      .toast-icon {
        color: #2196F3;
      }
    }
  }

  &.dark-theme {
    background-color: #333333;
    color: white;

    .toast-border-animation {
      opacity: 0.7;
    }

    &.success {
      border-left: 4px solid #01B48D;

      .toast-border-animation {
        background: linear-gradient(90deg, transparent, #01B48D, transparent);
        background-size: 300% 100%;
      }

      .toast-icon {
        color: #01B48D;
      }
    }

    &.error {
      border-left: 4px solid #F44336;

      .toast-border-animation {
        background: linear-gradient(90deg, transparent, #F44336, transparent);
        background-size: 300% 100%;
      }

      .toast-icon {
        color: #F44336;
      }
    }

    &.warning {
      border-left: 4px solid #FF9800;

      .toast-border-animation {
        background: linear-gradient(90deg, transparent, #FF9800, transparent);
        background-size: 300% 100%;
      }

      .toast-icon {
        color: #FF9800;
      }
    }

    &.info {
      border-left: 4px solid #2196F3;

      .toast-border-animation {
        background: linear-gradient(90deg, transparent, #2196F3, transparent);
        background-size: 300% 100%;
      }

      .toast-icon {
        color: #2196F3;
      }
    }
  }
}

.toast-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));

  svg {
    width: 24px;
    height: 24px;
    display: block; /* Ensure proper display */
  }

  img {
    width: 24px;
    height: 24px;
    display: block;
    object-fit: contain;
  }
}

.toast-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-left: 8px;
  cursor: pointer;
  opacity: 0.7;
  flex-shrink: 0;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }

  svg {
    width: 16px;
    height: 16px;
    display: block;
  }
}
