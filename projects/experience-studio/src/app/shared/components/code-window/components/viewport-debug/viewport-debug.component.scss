// ===== VIEWPORT DEBUG PANEL =====

.viewport-debug-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: var(--debug-panel-bg, rgba(255, 255, 255, 0.98));
  border: 1px solid var(--debug-panel-border, #e1e5e9);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  z-index: 1000;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  overflow: hidden;
  transition: all 0.3s ease;

  &.collapsed {
    height: auto;
    max-height: none;
  }

  .debug-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: var(--debug-header-bg, #f8fafc);
    border-bottom: 1px solid var(--debug-header-border, #e5e7eb);
    cursor: pointer;
    user-select: none;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--debug-header-color, #1f2937);
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: var(--debug-icon-color, #3b82f6);
      }
    }

    .collapse-btn {
      background: transparent;
      border: none;
      color: var(--debug-collapse-color, #6b7280);
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background: var(--debug-collapse-hover-bg, #e5e7eb);
        color: var(--debug-collapse-hover-color, #374151);
      }

      i {
        font-size: 14px;
      }
    }
  }

  .debug-content {
    max-height: calc(80vh - 60px);
    overflow-y: auto;
    padding: 20px;

    .debug-section {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .info-group {
      background: var(--debug-group-bg, #f9fafb);
      border: 1px solid var(--debug-group-border, #f3f4f6);
      border-radius: 8px;
      padding: 16px;

      h5 {
        margin: 0 0 12px 0;
        font-size: 13px;
        font-weight: 600;
        color: var(--debug-group-title-color, #374151);
        display: flex;
        align-items: center;
        gap: 6px;

        i {
          color: var(--debug-group-icon-color, #6b7280);
          font-size: 12px;
        }
      }

      .info-grid {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px 0;

          .label {
            font-weight: 500;
            color: var(--debug-label-color, #6b7280);
            min-width: 100px;
          }

          .value {
            font-weight: 600;
            color: var(--debug-value-color, #1f2937);
            font-family: 'SF Mono', monospace;
            background: var(--debug-value-bg, #ffffff);
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid var(--debug-value-border, #e5e7eb);
          }
        }
      }

      .formula-breakdown {
        display: flex;
        flex-direction: column;
        gap: 6px;

        .formula-step {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 4px 8px;
          background: var(--debug-formula-bg, #ffffff);
          border-radius: 4px;
          border: 1px solid var(--debug-formula-border, #e5e7eb);

          .formula-label {
            font-weight: 500;
            color: var(--debug-formula-label-color, #6b7280);
            font-size: 11px;
          }

          .formula-value {
            font-weight: 600;
            color: var(--debug-formula-value-color, #059669);
            font-family: 'SF Mono', monospace;
          }
        }
      }

      .control-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;

        .test-btn {
          background: var(--debug-btn-bg, #3b82f6);
          color: var(--debug-btn-color, #ffffff);
          border: none;
          border-radius: 6px;
          padding: 8px 12px;
          font-size: 11px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;

          &:hover {
            background: var(--debug-btn-hover-bg, #2563eb);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }

          &.optimal-btn {
            background: var(--debug-optimal-btn-bg, #f59e0b);
            color: var(--debug-optimal-btn-color, #ffffff);
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);

            &:hover {
              background: var(--debug-optimal-btn-hover-bg, #d97706);
              box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
            }
          }

          i {
            font-size: 12px;
          }
        }
      }
    }

    .refresh-info {
      text-align: center;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid var(--debug-refresh-border, #f3f4f6);

      small {
        color: var(--debug-refresh-color, #9ca3af);
        font-size: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        i {
          animation: spin 2s linear infinite;
        }
      }
    }
  }
}

// ===== DARK THEME SUPPORT =====

:host-context(.dark-theme) {
  .viewport-debug-panel {
    --debug-panel-bg: rgba(42, 42, 42, 0.98);
    --debug-panel-border: #404040;
    --debug-header-bg: #1f1f1f;
    --debug-header-border: #404040;
    --debug-header-color: #e5e7eb;
    --debug-icon-color: #60a5fa;
    --debug-collapse-color: #9ca3af;
    --debug-collapse-hover-bg: #374151;
    --debug-collapse-hover-color: #d1d5db;
    --debug-group-bg: #2a2a2a;
    --debug-group-border: #404040;
    --debug-group-title-color: #d1d5db;
    --debug-group-icon-color: #9ca3af;
    --debug-label-color: #9ca3af;
    --debug-value-color: #e5e7eb;
    --debug-value-bg: #1f1f1f;
    --debug-value-border: #404040;
    --debug-formula-bg: #1f1f1f;
    --debug-formula-border: #404040;
    --debug-formula-label-color: #9ca3af;
    --debug-formula-value-color: #34d399;
    --debug-btn-bg: #3b82f6;
    --debug-btn-color: #ffffff;
    --debug-btn-hover-bg: #2563eb;
    --debug-optimal-btn-bg: #f59e0b;
    --debug-optimal-btn-color: #ffffff;
    --debug-optimal-btn-hover-bg: #d97706;
    --debug-refresh-border: #404040;
    --debug-refresh-color: #6b7280;
  }
}

// ===== ANIMATIONS =====

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// ===== RESPONSIVE DESIGN =====

@media (max-width: 768px) {
  .viewport-debug-panel {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
    max-height: 60vh;
  }
}
