const { ModuleFederationPlugin } = require('webpack').container;

module.exports = {
  output: {
    publicPath: 'auto',
    uniqueName: 'product-studio'
  },
  optimization: {
    runtimeChunk: false
  },
  experiments: {
    outputModule: true
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      }
    ]
  },
  devServer: {
    port: 4202,
    headers: {
      "Access-Control-Allow-Origin": "*"
    },
    client: {
      webSocketURL: 'auto://0.0.0.0:0/ws'
    },
    allowedHosts: 'all'
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'productStudio',
      library: { type: 'module' },
      filename: 'remoteEntry.js',
      exposes: {
        './ProductStudioComponent': './projects/product-studio/src/app/product/product-studio.component.ts',
        './BrainstormerComponent': './projects/product-studio/src/app/product/brainstormer/brainstormer.component.ts',
        './AppComponent': './projects/product-studio/src/app/app.component.ts'
      },
      shared: {
        '@angular/core': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@angular/common': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@angular/router': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@angular/animations': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@awe/play-comp-library': { singleton: true, strictVersion: true, requiredVersion: '^1.0.0' }
      }
    })
  ]
};
