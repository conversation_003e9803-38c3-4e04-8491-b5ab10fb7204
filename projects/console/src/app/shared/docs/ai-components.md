# AI Components Documentation

This document provides comprehensive documentation for the AI-related components available in the <PERSON>sole project. These components have been adapted from the Experience Studio project to provide consistent UI patterns for AI interactions across the platform.

## Table of Contents

1. [ChatInterfaceComponent](#chatinterfacecomponent)
2. [ChatWindowComponent](#chatwindowcomponent)
3. [CodeWindowComponent](#codewindowcomponent)
4. [CodeViewerComponent](#codeviewercomponent)

## ChatInterfaceComponent

The `ChatInterfaceComponent` provides a modern, clean chat interface matching the design from the provided screenshot. This component is highly reusable and can be easily integrated throughout the application.

### Imports

```typescript
import { ChatInterfaceComponent } from '../../shared/components';
```

### Interface

```typescript
export interface ChatMessage {
  from: 'user' | 'ai';
  text: string;
  imageDataUri?: string;
}
```

### Usage

```html
<app-chat-interface
  [placeholder]="'Ask here'"
  [messages]="messages"
  [isLoading]="isLoading"
  [isDisabled]="isProcessing"
  (messageSent)="handleMessageSent($event)"
  (attachmentClicked)="handleAttachment()">
</app-chat-interface>
```

### Inputs

| Name | Type | Default | Description |
|------|------|---------|-------------|
| `theme` | `'light' \| 'dark'` | `'light'` | Theme for the chat interface |
| `placeholder` | `string` | `'Ask here'` | Placeholder text for the input field |
| `messages` | `ChatMessage[]` | `[]` | Array of chat messages to display |
| `isLoading` | `boolean` | `false` | Whether to show loading indicator |
| `isDisabled` | `boolean` | `false` | Whether the input is disabled |

### Prompt Design
The prompt input features a custom design with:
- Gradient border from purple (#6566CD) to pink (#F96CAB)
- 12px border radius
- Mulish font family
- Icons positioned in the bottom-right corner of the input area
- Multi-line textarea with minimum height (96px)

### Outputs

| Name | Type | Description |
|------|------|-------------|
| `messageSent` | `EventEmitter<string>` | Emitted when a message is sent |
| `attachmentClicked` | `EventEmitter<void>` | Emitted when attachment button is clicked |

### Example Implementation

```typescript
import { Component } from '@angular/core';
import { ChatMessage, ChatInterfaceComponent } from '../../shared/components';

@Component({
  selector: 'app-my-page',
  template: `
    <div class="chat-container">
      <app-chat-interface
        [messages]="chatMessages"
        [isLoading]="isProcessing"
        (messageSent)="processMessage($event)">
      </app-chat-interface>
    </div>
  `,
  styles: [`
    .chat-container {
      height: 500px;
      width: 100%;
      border-radius: 12px;
      overflow: hidden;
    }
  `]
})
export class MyPageComponent {
  chatMessages: ChatMessage[] = [
    { from: 'ai', text: 'Hi Akash, this is the tool testing' }
  ];
  isProcessing = false;

  processMessage(message: string): void {
    this.isProcessing = true;
    
    // Simulate API call
    setTimeout(() => {
      this.chatMessages.push({ 
        from: 'ai', 
        text: `Received: "${message}"` 
      });
      this.isProcessing = false;
    }, 1000);
  }
}
```

### Using in Other Components

You can embed the ChatInterfaceComponent within other components. For example, to include it in a side panel:

```typescript
import { Component } from '@angular/core';
import { ChatInterfaceComponent, ChatMessage } from '../../shared/components';

@Component({
  selector: 'app-agent-panel',
  template: `
    <div class="panel-container">
      <div class="panel-header">
        <h3>Agent Assistant</h3>
        <button class="close-button" (click)="closePanel()">×</button>
      </div>
      
      <div class="chat-container">
        <app-chat-interface
          [messages]="chatMessages"
          [isLoading]="isProcessing"
          (messageSent)="handleAgentQuery($event)">
        </app-chat-interface>
      </div>
      
      <div class="panel-footer">
        <button class="action-button" (click)="clearChat()">Clear Chat</button>
      </div>
    </div>
  `,
  styles: [`
    .panel-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      border-left: 1px solid #e0e0e0;
    }
    
    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e0e0e0;
    }
    
    .chat-container {
      flex: 1;
      overflow: hidden;
    }
    
    .panel-footer {
      padding: 12px;
      border-top: 1px solid #e0e0e0;
      text-align: right;
    }
  `]
})
export class AgentPanelComponent {
  chatMessages: ChatMessage[] = [];
  isProcessing = false;
  
  constructor(private agentService: AgentService) {
    this.initializeChat();
  }
  
  initializeChat(): void {
    this.chatMessages = [
      { from: 'ai', text: 'I\'m your agent assistant. How can I help you today?' }
    ];
  }
  
  handleAgentQuery(query: string): void {
    this.isProcessing = true;
    this.agentService.processQuery(query).subscribe(response => {
      this.chatMessages.push({ from: 'ai', text: response });
      this.isProcessing = false;
    });
  }
  
  clearChat(): void {
    this.initializeChat();
  }
  
  closePanel(): void {
    // Handle panel closing logic
  }
}
```

## ChatWindowComponent

The `ChatWindowComponent` provides an interactive chat interface for AI-powered conversations with markdown support and image handling capabilities.

### Imports

```typescript
import { ChatWindowComponent, ChatMessage } from '../../shared/components';
```

### Interface

```typescript
export interface ChatMessage {
  from: 'user' | 'ai';
  text: string;
  imageDataUri?: string;
}
```

### Usage

```html
<app-chat-window
  [theme]="currentTheme"
  [defaultText]="'Ask me'"
  [rightIcons]="rightIcons"
  [(textValue)]="promptText"
  [chatMessages]="messages"
  [showStepper]="isPolling"
  [progress]="progressState"
  [progressDescription]="progressDescription"
  [status]="status"
  [selectedImageDataUri]="selectedImage"
  [isCodeGenerationComplete]="isCodeGenerationComplete"
  (iconClicked)="handleIconClick($event)"
  (enterPressed)="handleSendMessage()">
</app-chat-window>
```

### Inputs

| Name | Type | Default | Description |
|------|------|---------|-------------|
| `theme` | `'light' \| 'dark'` | `'light'` | Theme for the chat window |
| `defaultText` | `string` | `''` | Placeholder text for the input field |
| `leftIcons` | `any[]` | `[]` | Icons to display on the left side of the input |
| `rightIcons` | `any[]` | `[]` | Icons to display on the right side of the input |
| `textValue` | `string` | `''` | Current value of the input field (two-way binding) |
| `chatMessages` | `ChatMessage[]` | `[]` | Array of chat messages to display |
| `showStepper` | `boolean` | `false` | Whether to show the loading animation |
| `progress` | `number` | `0` | Progress percentage (0-100) for stepper |
| `progressDescription` | `string` | `''` | Text description of current progress |
| `status` | `'RUNNING' \| 'COMPLETED' \| 'ERROR'` | `'RUNNING'` | Current status of processing |
| `selectedImageDataUri` | `string \| null` | `null` | Data URI of selected image to add to user message |
| `isCodeGenerationComplete` | `boolean` | `true` | Whether code generation is complete (enables/disables input) |

### Outputs

| Name | Type | Description |
|------|------|-------------|
| `textValueChange` | `EventEmitter<string>` | Emitted when text input changes |
| `enterPressed` | `EventEmitter<void>` | Emitted when Enter key is pressed |
| `iconClicked` | `EventEmitter<{name: string, index: number}>` | Emitted when an icon is clicked |
| `imageTransferred` | `EventEmitter<string>` | Emitted when an image is added to chat |

### Example Implementation

```typescript
import { Component } from '@angular/core';
import { ChatWindowComponent, ChatMessage } from '../../shared/components';

@Component({
  selector: 'app-my-chat',
  template: `
    <app-chat-window
      [theme]="'light'"
      [chatMessages]="messages"
      [(textValue)]="inputText"
      (enterPressed)="sendMessage()">
    </app-chat-window>
  `
})
export class MyChatComponent {
  messages: ChatMessage[] = [
    { from: 'ai', text: 'Hello! How can I help you?' }
  ];
  inputText = '';
  
  sendMessage() {
    if (!this.inputText.trim()) return;
    
    // Add user message
    this.messages.push({
      from: 'user',
      text: this.inputText
    });
    
    const userQuery = this.inputText;
    this.inputText = '';
    
    // Add AI response (simulated)
    setTimeout(() => {
      this.messages.push({
        from: 'ai',
        text: `You said: "${userQuery}". This is a simulated response.`
      });
    }, 1000);
  }
}
```

## CodeWindowComponent

The `CodeWindowComponent` creates a split-screen interface with a chat panel on the left and a code editor/preview panel on the right.

### Imports

```typescript
import { CodeWindowComponent } from '../../shared/components';
```

### Usage

```html
<app-code-window></app-code-window>
```

### Example Implementation

```typescript
import { Component } from '@angular/core';
import { CodeWindowComponent } from '../../shared/components';

@Component({
  selector: 'app-my-code-window',
  template: `
    <div class="code-window-container">
      <app-code-window></app-code-window>
    </div>
  `,
  styles: [`
    .code-window-container {
      width: 100%;
      height: 600px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  `]
})
export class MyCodeWindowComponent {}
```

## CodeViewerComponent

The `CodeViewerComponent` provides a simple code display with syntax highlighting and file navigation.

### Imports

```typescript
import { CodeViewerComponent } from '../../shared/components';
```

### Interface

```typescript
interface CodeFile {
  name: string;
  path: string;
  content: string;
  language: string;
}
```

### Usage

```html
<app-code-viewer
  [files]="codeFiles"
  [theme]="currentTheme"
  [showFileExplorer]="true">
</app-code-viewer>
```

### Inputs

| Name | Type | Default | Description |
|------|------|---------|-------------|
| `files` | `CodeFile[]` | `[]` | Array of code files to display |
| `theme` | `'light' \| 'dark'` | `'light'` | Theme for the code viewer |
| `showFileExplorer` | `boolean` | `true` | Whether to show the file explorer panel |

### Example Implementation

```typescript
import { Component } from '@angular/core';
import { CodeViewerComponent } from '../../shared/components';

@Component({
  selector: 'app-my-code-viewer',
  template: `
    <div class="code-viewer-container">
      <app-code-viewer
        [files]="files"
        [theme]="'light'"
        [showFileExplorer]="true">
      </app-code-viewer>
    </div>
  `,
  styles: [`
    .code-viewer-container {
      width: 100%;
      height: 400px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }
  `]
})
export class MyCodeViewerComponent {
  files = [
    {
      name: 'index.html',
      path: 'src/index.html',
      language: 'html',
      content: `<!DOCTYPE html>
<html>
<head>
  <title>Example</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <h1>Hello World</h1>
  <script src="app.js"></script>
</body>
</html>`
    },
    {
      name: 'styles.css',
      path: 'src/styles.css',
      language: 'css',
      content: `body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
}

h1 {
  color: #333;
}`
    },
    {
      name: 'app.js',
      path: 'src/app.js',
      language: 'javascript',
      content: `document.addEventListener('DOMContentLoaded', () => {
  console.log('Application initialized');
});`
    }
  ];
}
```

## Integration with the Dashboard

The AI components are integrated into the Dashboard to showcase their functionality. The components are added to the dashboard layout before the standard KPI cards and data tables.

```html
<!-- Dashboard Component Template -->
<div class="dashboard-container">
  <!-- Welcome section -->
  <div class="welcome-section">
    <!-- ... -->
  </div>

  <!-- Code Window Component -->
  <div class="code-window-wrapper">
    <app-code-window></app-code-window>
  </div>

  <!-- Main Dashboard Grid Layout -->
  <div class="dashboard-content">
    <!-- ... -->
  </div>
</div>
```

## ChatInterfaceComponent Demo

A dedicated demo page has been created to showcase the ChatInterfaceComponent in a standalone context. This demo allows you to interact with the component and see its features in action.

You can access the demo at:
```
/chat-demo
```

The demo page demonstrates:
- Clean message bubbles with different styling for user and AI messages
- Loading indicators
- Attachment functionality
- Responsive design that works on all screen sizes

## Customization

These components support full theme customization via CSS variables. To customize the appearance of these components, you can override the following variables in your application's styles:

```scss
:root {
  // Chat window variables
  --chat-bg-light: #f8f9fa;
  --chat-bg-dark: #1a1a1a;
  --user-msg-bg-light: #f8d7e3; /* Light purple */
  --user-msg-bg-dark: #4c1d95;
  --ai-msg-bg-light: #f0f2f5; /* Light gray */
  --ai-msg-bg-dark: #1e293b;
  --chat-text-light: #333;
  --chat-text-dark: #f8f9fa;
  --prompt-gradient-start: #6566CD;
  --prompt-gradient-end: #F96CAB;
  
  // Code window variables
  --code-bg-light: #fafafa;
  --code-bg-dark: #1e1e1e;
  --code-text-light: #333;
  --code-text-dark: #e0e0e0;
  --file-explorer-width: 220px;
}
```

## Accessibility

All AI components implement accessibility best practices:

1. All interactive elements are keyboard navigable
2. Proper ARIA attributes are used for non-standard controls
3. Color contrast meets WCAG AA standards
4. Screen reader-friendly markup structure

## Browser Compatibility

These components are compatible with:

- Chrome/Edge (latest 2 versions)
- Firefox (latest 2 versions)
- Safari (latest 2 versions)
- iOS Safari (latest 2 versions)
- Android Chrome (latest 2 versions)

## Performance Considerations

For optimal performance:

1. Limit the number of messages in the chat window (consider pagination for long conversations)
2. Keep code files reasonably sized to prevent rendering delays
3. Consider lazy loading these components if they're not needed immediately on page load

## Design Comparison

The ChatInterfaceComponent has been designed to closely match the provided screenshot, featuring:

- Light purple user message bubbles (right-aligned)
- Light gray AI message bubbles (left-aligned)
- Clean, minimal attachment and send buttons
- Rounded message corners with one angled corner per bubble
- Proper spacing between messages
- Modern input field design with gradient border (purple to pink)
- Input with exactly 12px border radius
- Mulish font family for typography
- Action icons positioned within the input area
- Fixed-height input (56px) for consistent design

This consistent design language ensures users will have a familiar experience throughout the application.
Visual representation:
```
┌─────────────────────────────────────────────────────┐
│                                                     │
│  ┌─────────────────────────────┐                    │
│  │ Hi Akash, this is the tool  │                    │
│  │ testing                     │                    │
│  └─────────────────────────────┘                    │
│                                                     │
│                     ┌─────────────────────────────┐ │
│                     │ Test this input             │ │
│                     └─────────────────────────────┘ │
│                                                     │
│  ┌─────────────────────────────┐                    │
│  │ Here is the output          │                    │
│  └─────────────────────────────┘                    │
│                                                     │
│                                                     │
│  ╭────────────────────────────────────────────╮    │
│  │ Ask here                                    │    │
│  │                                             │    │
│  │                                 📎   ➤      │    │
│  ╰────────────────────────────────────────────╯    │
│       ^ Gradient border (purple → pink)             │
│                                                     │
└─────────────────────────────────────────────────────┘
```

### Key Styling Features

The styling of the ChatInterfaceComponent implements several specific design elements:

1. **Prompt Input**:
   - Gradient border from #6566CD (purple) to #F96CAB (pink)
   - Minimum height of 96px for multi-line input
   - Border radius of 12px
   - Mulish font family

2. **Message Bubbles**:
   - User messages: Light purple background (#f8d7e3), right-aligned
   - AI messages: Light gray background (#f0f2f5), left-aligned
   - One angled corner per bubble (bottom-right for user, bottom-left for AI)

3. **Action Icons**:
   - Positioned in the bottom-right corner of the input area
   - Proper spacing between icons
   - Purple accent color matching the gradient

This consistent design language ensures users will have a familiar experience throughout the application.