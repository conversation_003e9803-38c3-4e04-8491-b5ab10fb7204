<div class="agent-activity">
  <!-- SVG Gradient Definitions for Icons -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <linearGradient id="activityGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#6566CD" />
        <stop offset="100%" stop-color="#F96CAB" />
      </linearGradient>
    </defs>
  </svg>

  <div class="activity-header">
    <h2 id="activity-title">Agentic Activity</h2>
    <button
      class="save-button"
      (click)="onSaveLogs()"
      aria-label="Save activity logs"
      title="Save activity logs"
    >
      Save Logs
      <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true"
      >
        <path
          d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z"
          stroke="url(#activityGradient)"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M17 21v-8H7v8M7 3v5h8"
          stroke="url(#activityGradient)"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>
  </div>

  <div class="activity-content" aria-labelledby="activity-title">
    <!-- Activity Logs -->
    <div class="logs-section">
      <p *ngIf="!activityLogs.length" class="no-logs" aria-live="polite">
        No activity logs available yet.
      </p>

      <div class="activity-text" *ngIf="activityLogs.length">
        If you want to add minimal interactivity, create a script.js file. This
        can include JavaScript to toggle a mobile navigation menu, enable smooth
        scrolling between sections when clicking on navigation links, or animate
        elements when they come into view. Link this script in the HTML before
        the closing &lt;/body&gt; tag using &lt;script
        src="script.js"&gt;&lt;/script&gt;. Once the website structure, styling,
        and interactivity are complete, organize all files into a single project
        folder named something like portfolio-site. The folder should include
        index.html, style.css, script.js (if used), and a subdirectory /images
        for project thumbnails or personal photos. You can zip this folder for
        download or proceed to deploy it online. For deployment, you can host
        the website using services like GitHub Pages, Netlify, or Vercel. To
        deploy with GitHub Pages, initialize a Git repository in the folder,
        commit your files, and push them to a GitHub repository. Then, enable
        GitHub Pages in the repository settings, choosing the main branch and
        root folder for the site source. If using Netlify or Vercel, sign in
        with GitHub and connect your repository directly, then follow their
        instructions to deploy the site. By the end of this process, you'll have
        a clean, responsive, and fully functional HTML portfolio website that
        showcases your work and can be easily shared with potential employers or
        clients.
      </div>

      <div
        class="success-message"
        *ngIf="activityLogs.length"
        role="status"
        aria-live="polite"
      >
        - Agent Pipeline completed successfully -
      </div>
    </div>
  </div>

  <!-- Progress and Controls -->
  <div class="progress-section">
    <div class="progress-info">
      <!-- Visual progress bar -->
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="progress"></div>
      </div>
      <span class="progress-label" aria-live="polite"
        >Progress - {{ progress }}% Complete</span
      >
    </div>

    <div
      class="control-buttons"
      role="toolbar"
      aria-label="Workflow execution controls"
    >
      <button
        class="control-button refresh"
        (click)="onControlAction('play')"
        aria-label="Refresh workflow"
        title="Refresh"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <path
            d="M23 4v6h-6M1 20v-6h6"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>

      <button
        class="control-button stop"
        (click)="onControlAction('stop')"
        aria-label="Stop workflow execution"
        title="Stop"
        [disabled]="!isRunning"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <rect x="4" y="4" width="16" height="16" fill="currentColor" />
        </svg>
      </button>

      <button
        class="control-button play"
        (click)="onControlAction('play')"
        aria-label="Start workflow execution"
        title="Start"
        [disabled]="isRunning"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <path d="M5 3l14 9-14 9V3z" fill="currentColor" />
        </svg>
      </button>
    </div>
  </div>
</div>
