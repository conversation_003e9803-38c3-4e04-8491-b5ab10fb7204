import { Component, Input, Output, EventEmitter, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DropdownItemComponent } from '../dropdown-item/dropdown-item.component';

interface DropdownItem {
  label: string;
  description: string;
  route: string;
  icon: string;
}

@Component({
  selector: 'app-dropdown-menu',
  standalone: true,
  imports: [CommonModule, DropdownItemComponent],
  templateUrl: './dropdown-menu.component.html',
  styleUrl: './dropdown-menu.component.scss'
})
export class DropdownMenuComponent {
  @Input() items: DropdownItem[] = [];
  @Input() visible: boolean = false;
  @Output() itemSelected = new EventEmitter<{route: string, label: string}>();
  
  readonly ITEMS_PER_COLUMN = 3;
  
  constructor(private el: ElementRef) {}

  onItemClick(event: {route: string, label: string}): void {
    console.log('Dropdown menu item clicked:', event);
    this.itemSelected.emit(event);
  }
  
  // Method to split items into columns
  get columns(): DropdownItem[][] {
    const columns: DropdownItem[][] = [];
    const itemsPerColumn = this.ITEMS_PER_COLUMN;
    
    for (let i = 0; i < this.items.length; i += itemsPerColumn) {
      columns.push(this.items.slice(i, i + itemsPerColumn));
    }
    
    return columns;
  }
} 