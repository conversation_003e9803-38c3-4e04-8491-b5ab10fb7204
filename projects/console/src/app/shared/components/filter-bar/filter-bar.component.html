<div class="filter-bar">
  <div *ngFor="let group of filterConfig.filterGroups" 
       class="filter-item" 
       [class.active]="isDropdownOpen(group.id)"
       [class.selected]="group.selectedOption"
       (click)="toggleDropdown(group.id)">
    
    <div class="filter-label">
      <span>{{ getLabelForSelectedOption(group) }}</span>
      
      <button *ngIf="group.selectedOption" 
              class="clear-button" 
              (click)="clearFilter(group, $event)" 
              title="Clear filter">
        &times;
      </button>
      
      <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
    
    <div class="filter-dropdown" *ngIf="isDropdownOpen(group.id)">
      <div class="dropdown-options">
        <div *ngFor="let option of group.options" 
             class="dropdown-option" 
             [class.selected]="group.selectedOption?.value === option.value"
             (click)="selectOption(group, option)">
          {{ option.label }}
        </div>
      </div>
    </div>
  </div>
</div> 