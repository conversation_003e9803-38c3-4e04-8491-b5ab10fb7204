.search-container {
  width: 100%;
  display: flex;
  max-width: 100%;
  height: 50px;
  border-radius: 12px;
  gap: 10px;
  position: relative;
  z-index: 100;
}

.search-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 101;
  transform: scale(0.9);
  color: var(--nav-text);
  transition: color 0.3s ease;
}

/* Themed search input with gradient border */
.search-input {
  width: 100%;
  padding: 10px 16px 10px 48px;
  border-radius: 12px;
  background-color: var(--input-bg);
  font-size: 16px;
  color: var(--input-text);
  outline: none;
  border: double 1px transparent;
  background-image: linear-gradient(var(--input-bg), var(--input-bg)), 
                    var(--gradient-primary);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  height: 100%;
  position: relative;
  z-index: 100;
  transition: all 0.3s ease;
  
  &::placeholder {
    color: var(--input-text);
    opacity: 0.7;
    font-family: inherit;
    font-size: 16px;
    font-weight: 400;
  }
  
  &:focus {
    box-shadow: 0 0 0 3px var(--input-focus-shadow);
    background-image: linear-gradient(var(--input-bg), var(--input-bg)), 
                      linear-gradient(90.78deg, var(--primary-start) 8.06%, var(--primary-end) 73.25%);
  }
  
  &:hover:not(:focus) {
    background-image: linear-gradient(var(--input-bg), var(--input-bg)), 
                      linear-gradient(90.78deg, 
                                     rgba(101, 102, 205, 0.7) 8.06%, 
                                     rgba(227, 10, 109, 0.7) 73.25%);
  }
}

/* Clear button styles */
.clear-button {
  position: absolute;
  right: 16px;
  z-index: 101;
  background: none;
  border: none;
  color: var(--nav-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: all 0.2s ease;
  opacity: 0.7;
  
  &:hover {
    opacity: 1;
    transform: scale(1.1);
    color: var(--text-color);
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--input-focus-shadow);
  }
}

@media (max-width: 1200px) {
  .search-container {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .search-wrapper {
    max-width: 100%;
  }
  
  .search-container {
    height: 45px;
  }
  
  .search-input {
    font-size: 14px;
    padding: 8px 16px 8px 40px;
    
    &::placeholder {
      font-size: 14px;
    }
  }
  
  .search-icon {
    left: 12px;
    transform: scale(0.8);
  }
} 