import { Injectable, ComponentRef, ApplicationRef, createComponent, EnvironmentInjector } from '@angular/core';
import { Subject } from 'rxjs';
import { ToastType } from '../components/toast/toast.component';
import { createLogger } from '../utils/logger';

export interface ToastOptions {
  message: string;
  type: ToastType;
  duration?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

export interface ToastEvent {
  action: 'add' | 'clear';
  toast?: {
    message: string;
    type: ToastType;
    duration: number;
  };
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  toastEvents = new Subject<ToastEvent>();
  private containerRef: ComponentRef<any> | null = null;
  private logger = createLogger('ToastService');

  constructor(
    private appRef: ApplicationRef,
    private injector: EnvironmentInjector
  ) {}

  /**
   * Show a toast notification
   * @param options Toast configuration options
   */
  show(options: ToastOptions): void {
    const { message, type, duration = 5000, position = 'bottom-right' } = options;

    // Create container component if it doesn't exist
    if (!this.containerRef) {
      this.createContainer();

      // Add a small delay to ensure the container is created before adding toasts
      setTimeout(() => {
        this.emitToastEvent(message, type, duration, position);
      }, 100);
    } else {
      this.emitToastEvent(message, type, duration, position);
    }
  }

  /**
   * Emit a toast event
   */
  private emitToastEvent(message: string, type: ToastType, duration: number, position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'): void {
    this.toastEvents.next({
      action: 'add',
      toast: {
        message,
        type,
        duration
      },
      position
    });
  }

  /**
   * Show a success toast for completed operations
   * @param message The message to display
   * @param duration Optional duration in ms
   */
  success(message: string, duration?: number): void {
    this.show({
      message,
      type: 'success',
      duration: duration || 4000
    });
  }

  /**
   * Show an error toast for failed operations
   * @param message The message to display
   * @param duration Optional duration in ms
   */
  error(message: string, duration?: number): void {
    this.show({
      message,
      type: 'error',
      duration: duration || 6000 // Longer duration for errors
    });
  }

  /**
   * Show a warning toast for actions that require attention
   * @param message The message to display
   * @param duration Optional duration in ms
   */
  warning(message: string, duration?: number): void {
    this.show({
      message,
      type: 'warning',
      duration: duration || 5000
    });
  }

  /**
   * Show an info toast for general information
   * @param message The message to display
   * @param duration Optional duration in ms
   */
  info(message: string, duration?: number): void {
    this.show({
      message,
      type: 'info',
      duration: duration || 4000
    });
  }

  /**
   * Show a toast for code generation state updates
   * @param state The current state of code generation
   * @param details Additional details about the state
   */
  codeGenerationState(state: string, details?: string): void {
    let message = '';
    let type: ToastType = 'info';
    let duration = 5000;

    switch (state.toUpperCase()) {
      case 'INITIALIZING':
        message = 'Analyzing your request and preparing code generation...';
        break;
      case 'LAYOUT_ANALYZED':
        message = 'UI layout successfully analyzed. Creating component structure...';
        type = 'success';
        break;
      case 'PAGES_GENERATED':
        message = 'Pages structure created. Generating component code...';
        type = 'success';
        break;
      case 'COMPONENTS_CREATED':
        message = 'Components generated. Finalizing application build...';
        type = 'success';
        break;
      case 'BUILD_SUCCEEDED':
        message = 'Application successfully built and ready to preview';
        type = 'success';
        break;
      case 'FAILED':
        message = details || 'Unable to complete code generation. Please check your inputs and try again.';
        type = 'error';
        duration = 7000;
        break;
      default:
        message = details || `Processing: ${state}`;
    }

    this.show({
      message,
      type,
      duration
    });
  }

  /**
   * Show a toast for file upload operations
   * @param success Whether the upload was successful
   * @param fileName The name of the file
   * @param errorDetails Optional error details
   */
  fileUpload(success: boolean, fileName: string, errorDetails?: string): void {
    if (success) {
      this.success(`Image "${fileName}" uploaded and ready for processing`);
    } else {
      this.error(errorDetails || `Unable to process image "${fileName}". Please try a different format or image.`);
    }
  }

  /**
   * Show a toast for prompt enhancement operations
   * @param success Whether the enhancement was successful
   * @param details Additional details
   */
  promptEnhancement(success: boolean, details?: string): void {
    if (success) {
      this.success(details || 'Prompt enhanced with additional details to improve generation quality');
    } else {
      this.warning(details || 'Unable to enhance prompt. Please add more specific details to your request.');
    }
  }

  /**
   * Show a toast for generation process updates
   * @param stage The current stage of generation
   * @param details Additional details
   */
  generationProcess(stage: 'start' | 'progress' | 'complete' | 'error', details?: string): void {
    switch (stage) {
      case 'start':
        this.info(details || 'Starting generation process. This may take a few moments...');
        break;
      case 'progress':
        this.info(details || 'Processing your request. Please wait while we generate your content...');
        break;
      case 'complete':
        this.success(details || 'Generation completed. Your content is now ready to view.');
        break;
      case 'error':
        this.error(details || 'Unable to complete generation. Please check your inputs and try again.');
        break;
    }
  }

  /**
   * Show a toast for connection status updates
   * @param status The connection status
   * @param details Additional details
   */
  connectionStatus(status: 'connected' | 'disconnected' | 'error', details?: string): void {
    switch (status) {
      case 'connected':
        this.success(details || 'Server connection established. Your request is being processed.');
        break;
      case 'disconnected':
        this.warning(details || 'Connection temporarily lost. Attempting to reconnect automatically...');
        break;
      case 'error':
        this.error(details || 'Unable to connect to server. Please check your network and try again.');
        break;
    }
  }

  /**
   * Show a toast for form validation status
   * @param isValid Whether the form is valid
   * @param details Additional details
   */
  formValidation(isValid: boolean, details?: string): void {
    if (isValid) {
      this.success(details || 'All inputs validated. Proceeding with your request.');
    } else {
      this.warning(details || 'Please complete all required information before proceeding.');
    }
  }

  /**
   * Show a toast for application state updates
   * @param state The current application state
   * @param details Additional details
   */
  applicationState(state: 'loading' | 'ready' | 'saving' | 'saved' | 'error', details?: string): void {
    switch (state) {
      case 'loading':
        this.info(details || 'Loading application resources...');
        break;
      case 'ready':
        this.success(details || 'Application ready');
        break;
      case 'saving':
        this.info(details || 'Saving your changes...');
        break;
      case 'saved':
        this.success(details || 'Changes saved successfully');
        break;
      case 'error':
        this.error(details || 'An error occurred. Please try again.');
        break;
    }
  }

  /**
   * Clear all toasts
   */
  clear(): void {
    this.toastEvents.next({ action: 'clear' });
  }

  /**
   * Create the toast container component
   */
  private createContainer(): void {
    // Dynamically import the container component
    import('../components/toast/toast-container.component').then(module => {
      // Create the container component
      this.containerRef = createComponent(module.ToastContainerComponent, {
        environmentInjector: this.injector
      });

      // Add to the DOMF
      document.body.appendChild(this.containerRef.location.nativeElement);

      // Attach to the application
      this.appRef.attachView(this.containerRef.hostView);
    });
  }
}
