<div class="filter-tabs">
  <div class="tabs-wrapper">
    <!-- Visible Tabs Container -->
    <div class="tabs-container">
      <button *ngFor="let tab of visibleTabs" class="tab-item" [class.active]="isActiveTab(tab.id)"
        (click)="onTabClick(tab.id)">
        <i *ngIf="tab.icon" [class]="tab.icon"></i>
        <span>{{ tab.label }}</span>
      </button>
    </div>

    <!-- Filters Dropdown (Always Visible) -->
    <div class="filter-dropdown-container">
      <button class="tab-item filter-dropdown-btn" [class.active]="showDropdown" (click)="toggleDropdown($event)">
        <i class="fas fa-filter"></i>
        <span>Filters</span>
        <i class="fas fa-chevron-down ms-2"></i>
      </button>

      <!-- Dropdown Menu -->
      <div class="filter-dropdown" *ngIf="showDropdown">
        <button *ngFor="let tab of dropdownTabs" class="dropdown-item" [class.active]="isActiveTab(tab.id)"
          (click)="onTabClick(tab.id, $event)">
          <i *ngIf="tab.icon" [class]="tab.icon"></i>
          <span>{{ tab.label }}</span>
        </button>
      </div>
    </div>
  </div>
</div>