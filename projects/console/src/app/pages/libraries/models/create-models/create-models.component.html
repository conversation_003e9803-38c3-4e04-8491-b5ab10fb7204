<div class="create-models-container">
  <form [formGroup]="modelForm">
    <div class="form-layout">
      <!-- Left Column -->
      <div class="left-column">
        <!-- Model Details Card -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <app-form-field
              label="Model Name"
              id="modelName"
              [control]="getControl('name')"
            ></app-form-field>

            <app-form-field
              label="Description"
              id="description"
              [control]="getControl('description')"
              type="textarea"
            ></app-form-field>
          </div>
        </app-card>

        <!-- Assign Filters Card -->
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <h3 class="section-title">Assign Filters</h3>

            <app-form-field
              label="Organization"
              id="organization"
              [control]="getControl('organization')"
            ></app-form-field>

            <app-form-field
              label="Domain"
              id="domain"
              [control]="getControl('domain')"
            ></app-form-field>

            <app-form-field
              label="Project"
              id="project"
              [control]="getControl('project')"
            ></app-form-field>

            <app-form-field
              label="Team"
              id="team"
              [control]="getControl('team')"
            ></app-form-field>
          </div>
        </app-card>
      </div>

      <!-- Right Column -->
      <div class="right-column">
        <app-card
          customClass="solid-card"
          [noPadding]="false"
          [isClickable]="false"
          [noHoverEffect]="true"
        >
          <div class="card-content">
            <div class="model-selection-container">
              <div class="engine-selection">
                <h3 class="selection-title">AI Engine</h3>
                <p class="selection-description">
                  Choose the preferred AI Engine
                </p>
                <div class="dropdown-container">
                  <select
                    class="dropdown-select"
                    [formControl]="getControl('aiEngine')"
                  >
                    <option
                      *ngFor="let engine of aiEngines"
                      [value]="engine.value"
                    >
                      {{ engine.label }}
                    </option>
                  </select>
                </div>
              </div>

              <div class="model-selection">
                <h3 class="selection-title">Model Name</h3>
                <p class="selection-description">
                  Choose the preferred Model Engine
                </p>
                <div class="dropdown-container">
                  <select
                    class="dropdown-select"
                    [formControl]="getControl('modelName')"
                  >
                    <option
                      *ngFor="let model of modelNames"
                      [value]="model.value"
                    >
                      {{ model.label }}
                    </option>
                  </select>
                </div>
              </div>
            </div>

            <div class="parameters-container">
              <h3 class="section-title">Configure Parameters</h3>

              <div class="parameter-form">
                <div class="param-row">
                  <div class="param-field">
                    <label class="param-label">Model Name</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('name')"
                      placeholder=""
                    />
                  </div>

                  <div class="param-field">
                    <label class="param-label">Model Type</label>
                    <div class="radio-group">
                      <label class="radio-option">
                        <input
                          type="radio"
                          value="Generative"
                          [formControl]="getControl('modelType')"
                        />
                        <span class="radio-label">Generative</span>
                      </label>
                      <label class="radio-option">
                        <input
                          type="radio"
                          value="Embedding"
                          [formControl]="getControl('modelType')"
                        />
                        <span class="radio-label">Embedding</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div class="param-row">
                  <div class="param-field">
                    <label class="param-label">GCP Project Id</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('gcpProjectId')"
                      placeholder=""
                    />
                  </div>

                  <div class="param-field">
                    <label class="param-label">GCP Location</label>
                    <input
                      type="text"
                      class="param-input"
                      [formControl]="getControl('gcpLocation')"
                      placeholder=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Buttons at bottom of right column -->
          <div class="right-column-buttons">
            <button type="button" class="exit-button" (click)="onExit()">
              Exit
            </button>
            <button type="button" class="save-button" (click)="onSave()">
              {{ isEditMode ? "Update" : "Save" }}
            </button>
          </div>
        </app-card>
      </div>
    </div>
  </form>
</div>
