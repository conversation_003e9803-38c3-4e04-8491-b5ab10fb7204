import { Buttons, FileAttachOption, IconOption } from '../modals/image-to-code.modal';

export const promptContentConstants = {
  acceptedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
  techOptions: [
    { name: 'React', icon: '/assets/icons/awe_react.svg', value: 'react', isLocalSvg: true },
    {
      name: 'Angular (Coming Soon)',
      icon: '/assets/icons/awe_angular.svg',
      value: 'angular',
      isLocalSvg: true,
      disabled: true,
    },
    {
      name: 'Vue (Coming Soon)',
      icon: '/assets/icons/awe_vue.svg',
      value: 'vue',
      isLocalSvg: true,
      disabled: true,
    },
  ],
  animatedTexts: [
    'a web app that..',
    'an internal tool that..',
    'an e-commerce site with..',
    'a portfolio website for my..',
    'a booking system for..',
    'a social media platform for..',
    'a customer support portal with..',
    'a real-time analytics dashboard for..',
    'a landing page for my..',
    "a dashboard to.."
  ],
  // Animated texts to show when an image is uploaded
  imageUploadAnimatedTexts: [
    'an application based on this image..',
    'a web app that looks like this image..',
    'a UI similar to this design..',
    'a responsive interface based on this mockup..',
    'a functional app from this wireframe..',
    'code that implements this design..',
    'a working prototype of this UI..',
    'an interactive version of this mockup..',
    'a component that matches this design..',
    'a pixel-perfect implementation of this image..' 
  ],
};
export const buttonLabels: Buttons[] = [
  {
    label: '✨Create a food delivery app with a modern user-friendly UI ',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create an eCommerce dashboard using provided layout',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a Spotify clone with a Doraemon-inspired color themes',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
];

export const iconOptions: IconOption[] = [
  { name: 'Angular', icon: 'awe_modules', value: 'angular' },
  { name: 'React', icon: 'awe_react', value: 'react' },
  { name: 'Vue', icon: 'awe_toggled_button', value: 'vue' },
  { name: 'Vue', icon: 'awe_toggled_button', value: 'vue' },
];

export const designOptions: IconOption[] = [
  {
    name: 'Tailwind',
    icon: '/assets/icons/awe_tailwind.svg',
    value: 'tailwind',
    isLocalSvg: true,
  },
  {
    name: 'Material UI (Coming Soon)',
    icon: '/assets/icons/awe_material.svg',
    value: 'material',
    isLocalSvg: true,
    disabled: true,
  },
  {
    name: 'Bootstrap (Coming Soon)',
    icon: '/assets/icons/awe_bootstrap.svg',
    value: 'bootstrap',
    isLocalSvg: true,
    disabled: true,
  },
];

export const fileOptions: FileAttachOption[] = [
  { name: 'Attach file from Computer', icon: 'awe_upload', value: 'computer' },
  { name: 'Attach file from Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
];
