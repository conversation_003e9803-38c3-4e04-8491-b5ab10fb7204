<div class="code-window-container">
  <div class="splitscreen-container" [class.is-resizing]="isResizing">
    <div class="left-panel" [class.collapsed]="isLeftPanelCollapsed">
      <div class="panel-header">
        <div class="header-left">
          <button class="home-button" title="Navigate to home page">
            <span class="icon">🏠</span>
          </button>
          <button class="toggle-panel-button" (click)="toggleLeftPanel()" title="Toggle left panel">
            <span class="icon">⬅️</span>
          </button>
        </div>
      </div>
      <div class="panel-content">
        <!-- Chat Window Component -->
        <app-chat-window
          [theme]="currentTheme"
          [defaultText]="'Ask me'"
          [rightIcons]="rightIcons"
          [(textValue)]="lightPrompt"
          [chatMessages]="lightMessages"
          [isCodeGenerationComplete]="isCodeGenerationComplete"
          (iconClicked)="handleIconClick($event)"
          (enterPressed)="handleEnhancedSendLight()">
        </app-chat-window>
      </div>
    </div>

    <div class="right-panel">
      <div class="panel-header">
        <div class="header-left">
          <button 
            *ngIf="isLeftPanelCollapsed"
            class="toggle-panel-button" 
            (click)="toggleLeftPanel()"
            title="Toggle left panel">
            <span class="icon">➡️</span>
          </button>

          <!-- Tabs -->
          <div class="tabs-container">
            <button
              class="tab-button"
              [class.active]="isPreviewActive"
              (click)="onTabClick('preview')"
              title="View preview">
              <span>Preview</span>
            </button>
            
            <button
              class="tab-button"
              [class.active]="isCodeActive"
              (click)="onTabClick('code')"
              title="View code editor">
              <span>Code</span>
            </button>
          </div>
        </div>
      </div>
      
      <div class="panel-content">
        <!-- Loading View -->
        <div *ngIf="isLoading" class="loading-view">
          <div class="loading-spinner"></div>
          <div class="loading-text">Loading project...</div>
        </div>

        <!-- Editor View -->
        <div *ngIf="currentView === 'editor' && !isLoading" class="editor-view">
          <app-code-viewer
            [files]="files"
            [theme]="currentTheme"
            [showFileExplorer]="true">
          </app-code-viewer>
        </div>

        <!-- Preview View -->
        <div *ngIf="currentView === 'preview' && !isLoading" class="preview-view">
          <div class="preview-iframe-container">
            <!-- Display static message instead of actual iframe -->
            <div class="preview-placeholder">
              <h2>Preview Content</h2>
              <p>This is a placeholder for the actual preview that would display the rendered HTML, CSS, and JavaScript.</p>
              <button>Click Me</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>