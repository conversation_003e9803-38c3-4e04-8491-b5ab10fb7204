<div class="agent-output">
  <!-- SVG Gradient Definitions for Icons -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#6566CD" />
        <stop offset="100%" stop-color="#F96CAB" />
      </linearGradient>
    </defs>
  </svg>

  <div class="output-header">
    <h3 id="agent-output-title">Agent Output</h3>
    <button
      class="export-button"
      (click)="exportOutput()"
      aria-label="Export agent outputs"
      title="Export agent outputs"
    >
      Export
      <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true"
      >
        <path
          d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"
          stroke="url(#gradient)"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M7 10l5 5 5-5M12 15V3"
          stroke="url(#gradient)"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>
  </div>

  <div class="output-content" aria-labelledby="agent-output-title">
    <!-- No outputs message -->
    <div *ngIf="!outputs.length" class="no-outputs" aria-live="polite">
      No agent outputs available yet.
    </div>

    <!-- Output cards -->
    <div *ngFor="let output of outputs; let i = index" class="output-card">
      <div class="output-card-header">
        <div class="output-info">
          <h4 class="output-title" [id]="'output-title-' + i">
            {{ output.title }}
          </h4>
          <div class="output-subtitle">{{ output.agentName }} Output</div>
        </div>
        <div class="output-actions">
          <button
            class="action-button copy-button"
            (click)="copyToClipboard(output.content)"
            aria-label="Copy to clipboard"
            title="Copy to clipboard"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <rect
                x="9"
                y="9"
                width="13"
                height="13"
                rx="2"
                ry="2"
                stroke="url(#gradient)"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1"
                stroke="url(#gradient)"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <button
            class="action-button preview-button"
            (click)="previewOutput(output)"
            aria-label="Preview output"
            title="Preview output"
          >
            Preview
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
                stroke="url(#gradient)"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <circle
                cx="12"
                cy="12"
                r="3"
                stroke="url(#gradient)"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- Code or text content based on type -->
      <div
        class="output-card-content"
        [ngClass]="getContentType(output)"
        [attr.aria-labelledby]="'output-title-' + i"
      >
        <pre><code>{{ output.content }}</code></pre>
      </div>

      <!-- Timestamp and metadata -->
      <div class="output-card-footer">
        <div class="output-timestamp">Generated: {{ output.timestamp }}</div>
      </div>
    </div>
  </div>
</div>
