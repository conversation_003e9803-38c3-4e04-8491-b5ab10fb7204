/**
 * Test file to verify URL extraction and sanitization implementation
 * Tests the requirements for DEPLOY + COMPLETED state with ref_code metadata
 */

import { TestBed } from '@angular/core/testing';
import { DomSanitizer } from '@angular/platform-browser';
import { NewPollingResponseProcessorService } from './new-polling-response-processor.service';
import { CodeWindowComponent } from '../components/code-window/code-window.component';

describe('URL Extraction and Sanitization', () => {
  let pollingService: NewPollingResponseProcessorService;
  let component: CodeWindowComponent;
  let sanitizer: DomSanitizer;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        NewPollingResponseProcessorService,
        // Mock DomSanitizer for testing
        {
          provide: DomSanitizer,
          useValue: {
            bypassSecurityTrustResourceUrl: jasmine.createSpy('bypassSecurityTrustResourceUrl').and.returnValue('sanitized-url')
          }
        }
      ]
    });

    pollingService = TestBed.inject(NewPollingResponseProcessorService);
    sanitizer = TestBed.inject(DomSanitizer);
  });

  describe('URL Extraction from ref_code metadata', () => {
    it('should extract URL when progress is DEPLOY and status is COMPLETED', (done) => {
      // **TEST CASE 1**: Valid DEPLOY + COMPLETED with ref_code URL
      const testResponse = {
        progress: 'DEPLOY',
        status: 'COMPLETED' as const,
        log: 'Deployment completed successfully',
        progress_description: 'Application deployed to production',
        history: [],
        metadata: [
          {
            type: 'ref_code' as const,
            data: 'https://mlo-a774-9290.netlify.app'
          }
        ]
      };

      // Subscribe to preview URL changes
      pollingService.previewUrl$.subscribe(url => {
        if (url) {
          expect(url).toBe('https://mlo-a774-9290.netlify.app');
          done();
        }
      });

      // Process the response
      pollingService.processNewWorkflowResponse(testResponse);
    });

    it('should not extract URL when progress is not DEPLOY', (done) => {
      // **TEST CASE 2**: Wrong progress state
      const testResponse = {
        progress: 'BUILD',
        status: 'COMPLETED' as const,
        log: 'Build completed',
        progress_description: 'Build process finished',
        history: [],
        metadata: [
          {
            type: 'ref_code' as const,
            data: 'https://mlo-a774-9290.netlify.app'
          }
        ]
      };

      let urlEmitted = false;
      pollingService.previewUrl$.subscribe(url => {
        if (url && url !== '') {
          urlEmitted = true;
        }
      });

      // Process the response
      pollingService.processNewWorkflowResponse(testResponse);

      // Wait a bit and verify no URL was emitted
      setTimeout(() => {
        expect(urlEmitted).toBe(false);
        done();
      }, 100);
    });

    it('should not extract URL when status is not COMPLETED', (done) => {
      // **TEST CASE 3**: Wrong status
      const testResponse = {
        progress: 'DEPLOY',
        status: 'IN_PROGRESS' as const,
        log: 'Deployment in progress',
        progress_description: 'Deploying application',
        history: [],
        metadata: [
          {
            type: 'ref_code' as const,
            data: 'https://mlo-a774-9290.netlify.app'
          }
        ]
      };

      let urlEmitted = false;
      pollingService.previewUrl$.subscribe(url => {
        if (url && url !== '') {
          urlEmitted = true;
        }
      });

      // Process the response
      pollingService.processNewWorkflowResponse(testResponse);

      // Wait a bit and verify no URL was emitted
      setTimeout(() => {
        expect(urlEmitted).toBe(false);
        done();
      }, 100);
    });

    it('should not extract URL when ref_code metadata is missing', (done) => {
      // **TEST CASE 4**: Missing ref_code metadata
      const testResponse = {
        progress: 'DEPLOY',
        status: 'COMPLETED' as const,
        log: 'Deployment completed',
        progress_description: 'Application deployed',
        history: [],
        metadata: [
          {
            type: 'artifact' as const,
            data: 'some other data'
          }
        ]
      };

      let urlEmitted = false;
      pollingService.previewUrl$.subscribe(url => {
        if (url && url !== '') {
          urlEmitted = true;
        }
      });

      // Process the response
      pollingService.processNewWorkflowResponse(testResponse);

      // Wait a bit and verify no URL was emitted
      setTimeout(() => {
        expect(urlEmitted).toBe(false);
        done();
      }, 100);
    });

    it('should validate URL format and reject invalid URLs', (done) => {
      // **TEST CASE 5**: Invalid URL format
      const testResponse = {
        progress: 'DEPLOY',
        status: 'COMPLETED' as const,
        log: 'Deployment completed',
        progress_description: 'Application deployed',
        history: [],
        metadata: [
          {
            type: 'ref_code' as const,
            data: 'invalid-url-format'
          }
        ]
      };

      let urlEmitted = false;
      pollingService.previewUrl$.subscribe(url => {
        if (url && url !== '') {
          urlEmitted = true;
        }
      });

      // Process the response
      pollingService.processNewWorkflowResponse(testResponse);

      // Wait a bit and verify no URL was emitted due to validation failure
      setTimeout(() => {
        expect(urlEmitted).toBe(false);
        done();
      }, 100);
    });
  });

  describe('URL Sanitization', () => {
    it('should sanitize URLs using DomSanitizer.bypassSecurityTrustResourceUrl', () => {
      // This test would need to be integrated with the component test
      // since the sanitization happens in the component, not the service
      const testUrl = 'https://mlo-a774-9290.netlify.app';
      
      // Call the sanitizer directly to verify it works
      const sanitizedUrl = sanitizer.bypassSecurityTrustResourceUrl(testUrl);
      
      expect(sanitizer.bypassSecurityTrustResourceUrl).toHaveBeenCalledWith(testUrl);
      expect(sanitizedUrl).toBe('sanitized-url');
    });
  });

  describe('Error Handling', () => {
    it('should handle empty metadata array gracefully', (done) => {
      const testResponse = {
        progress: 'DEPLOY',
        status: 'COMPLETED' as const,
        log: 'Deployment completed',
        progress_description: 'Application deployed',
        history: [],
        metadata: []
      };

      let urlEmitted = false;
      pollingService.previewUrl$.subscribe(url => {
        if (url && url !== '') {
          urlEmitted = true;
        }
      });

      // Process the response
      pollingService.processNewWorkflowResponse(testResponse);

      // Wait a bit and verify no URL was emitted
      setTimeout(() => {
        expect(urlEmitted).toBe(false);
        done();
      }, 100);
    });

    it('should handle null/undefined metadata gracefully', (done) => {
      const testResponse = {
        progress: 'DEPLOY',
        status: 'COMPLETED' as const,
        log: 'Deployment completed',
        progress_description: 'Application deployed',
        history: [],
        metadata: null as any
      };

      let urlEmitted = false;
      pollingService.previewUrl$.subscribe(url => {
        if (url && url !== '') {
          urlEmitted = true;
        }
      });

      // Process the response
      pollingService.processNewWorkflowResponse(testResponse);

      // Wait a bit and verify no URL was emitted
      setTimeout(() => {
        expect(urlEmitted).toBe(false);
        done();
      }, 100);
    });
  });
});
