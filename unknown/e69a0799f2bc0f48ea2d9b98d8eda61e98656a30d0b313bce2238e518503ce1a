import { Component, Input, OnInit, Output,  } from '@angular/core';
import { ButtonComponent, HeaderComponent, HeadingComponent } from '@awe/play-comp-library';
import { CommonModule } from '@angular/common';
import { UserProfile } from '../../models/user-profile.model';
import { ThemeServiceService } from '../../services/auth-config-service/theme-service.service';
import { AppConstants } from '../../appConstants';
// import { ThemeServiceService } from '../../services/theme-service.service';

@Component({
  selector: 'app-product-nav-bar',
  standalone: true,
  imports: [HeaderComponent, CommonModule, ButtonComponent, HeadingComponent],
  templateUrl: './product-nav-bar.component.html',
  styleUrl: './product-nav-bar.component.scss'
})
export class ProductNavBarComponent  implements OnInit {
  // @Output() logout = new EventEmitter<void>();
  @Input() userProfile?: UserProfile;
  showProfileMenu = false;
  themeToggleIcon = '';
  themeMenuIcon = '';
  logoSrc = '';

  constructor(private themeService: ThemeServiceService) {}

  ngOnInit(): void {
    this.updateThemeAssets();
    new MutationObserver(() => this.updateThemeAssets()).observe(document.body, {
      attributes: true,
      attributeFilter: ['class'],
    });
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.updateThemeAssets();
  }

  toggleProfileMenu(): void {
    this.showProfileMenu = !this.showProfileMenu;
  }

  onLogout(): void {
    // this.logout.emit();
    this.showProfileMenu = false;
  }

  getProfileImage(): string {
    return this.userProfile?.photoUrl || `svgs/user-avatar.svg`;
  }

  getDisplayName(): string {
    return this.userProfile?.displayName || 'User Profile';
  }

  getEmail(): string {
    return this.userProfile?.mail || this.userProfile?.userPrincipalName || '<EMAIL>';
  }

  private updateThemeAssets(): void {
    const currentTheme = this.themeService.getCurrentTheme();
    this.logoSrc = `svgs/ascendion-logo-${currentTheme}.svg`;
    this.themeToggleIcon = `svgs/theme-toggle-${currentTheme}.svg`;
    this.themeMenuIcon = `svgs/menu-${currentTheme}.svg`;
  }
}
