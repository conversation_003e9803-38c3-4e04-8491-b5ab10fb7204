import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { map, distinctUntilChanged } from 'rxjs/operators';
import { CanvasService, CanvasViewport } from './canvas.service';
import { NodeManagementService, NodeBounds } from './node-management.service';

// Viewport bounds interface
export interface ViewportBounds {
  left: number;
  top: number;
  right: number;
  bottom: number;
  width: number;
  height: number;
}

// Minimap configuration
export interface MinimapConfig {
  enabled: boolean;
  width: number;
  height: number;
  scale: number;
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

// Viewport statistics
export interface ViewportStats {
  totalNodes: number;
  visibleNodes: number;
  zoomLevel: number;
  canvasCenter: { x: number; y: number };
  contentBounds: { minX: number; minY: number; maxX: number; maxY: number };
}

@Injectable({
  providedIn: 'root'
})
export class ViewportService {
  // Default minimap configuration
  private readonly DEFAULT_MINIMAP: MinimapConfig = {
    enabled: false,
    width: 200,
    height: 150,
    scale: 0.1,
    position: 'bottom-right'
  };

  // State subjects
  private containerSizeSubject = new BehaviorSubject<{ width: number; height: number }>({ width: 0, height: 0 });
  private minimapConfigSubject = new BehaviorSubject<MinimapConfig>(this.DEFAULT_MINIMAP);
  private viewportPaddingSubject = new BehaviorSubject<number>(100);

  // Public observables
  containerSize$: Observable<{ width: number; height: number }> = this.containerSizeSubject.asObservable();
  minimapConfig$: Observable<MinimapConfig> = this.minimapConfigSubject.asObservable();
  viewportPadding$: Observable<number> = this.viewportPaddingSubject.asObservable();

  // Computed observables
  viewportBounds$: Observable<ViewportBounds>;
  nodeBounds$: Observable<NodeBounds>;
  viewportStats$: Observable<ViewportStats>;

  constructor(
    private canvasService: CanvasService,
    private nodeManagementService: NodeManagementService
  ) {
    // Initialize computed observables
    this.viewportBounds$ = this.createViewportBoundsObservable();
    this.nodeBounds$ = this.createNodeBoundsObservable();
    this.viewportStats$ = this.createViewportStatsObservable();

    // Subscribe to viewport bounds changes to update node visibility
    this.nodeBounds$.subscribe(bounds => {
      this.nodeManagementService.updateNodeVisibility(bounds);
    });
  }

  /**
   * Create viewport bounds observable
   */
  private createViewportBoundsObservable(): Observable<ViewportBounds> {
    return combineLatest([
      this.canvasService.viewport$,
      this.containerSize$,
      this.viewportPadding$
    ]).pipe(
      map(([viewport, containerSize, padding]) => {
        return this.calculateViewportBounds(viewport, containerSize, padding);
      }),
      distinctUntilChanged((a, b) => 
        a.left === b.left && a.top === b.top && 
        a.right === b.right && a.bottom === b.bottom
      )
    );
  }

  /**
   * Create node bounds observable for visibility culling
   */
  private createNodeBoundsObservable(): Observable<NodeBounds> {
    return this.viewportBounds$.pipe(
      map(bounds => ({
        left: bounds.left,
        top: bounds.top,
        right: bounds.right,
        bottom: bounds.bottom
      }))
    );
  }

  /**
   * Create viewport statistics observable
   */
  private createViewportStatsObservable(): Observable<ViewportStats> {
    return combineLatest([
      this.nodeManagementService.nodes$,
      this.canvasService.viewport$,
      this.containerSize$
    ]).pipe(
      map(([nodes, viewport, containerSize]) => {
        const visibleNodes = nodes.filter(node => node.visible).length;
        const contentBounds = this.nodeManagementService.getContentBounds();
        
        return {
          totalNodes: nodes.length,
          visibleNodes,
          zoomLevel: Math.round(viewport.zoom * 100),
          canvasCenter: {
            x: (containerSize.width / 2 - viewport.x) / viewport.zoom,
            y: (containerSize.height / 2 - viewport.y) / viewport.zoom
          },
          contentBounds
        };
      })
    );
  }

  /**
   * Calculate viewport bounds in canvas coordinates
   */
  private calculateViewportBounds(
    viewport: CanvasViewport, 
    containerSize: { width: number; height: number }, 
    padding: number
  ): ViewportBounds {
    // Calculate visible area in canvas coordinates
    const left = (-viewport.x - padding) / viewport.zoom;
    const top = (-viewport.y - padding) / viewport.zoom;
    const right = (containerSize.width - viewport.x + padding) / viewport.zoom;
    const bottom = (containerSize.height - viewport.y + padding) / viewport.zoom;

    return {
      left,
      top,
      right,
      bottom,
      width: right - left,
      height: bottom - top
    };
  }

  /**
   * Update container size
   */
  updateContainerSize(width: number, height: number): void {
    this.containerSizeSubject.next({ width, height });
  }

  /**
   * Get current container size
   */
  getContainerSize(): { width: number; height: number } {
    return this.containerSizeSubject.getValue();
  }

  /**
   * Update minimap configuration
   */
  updateMinimapConfig(config: Partial<MinimapConfig>): void {
    const current = this.minimapConfigSubject.getValue();
    this.minimapConfigSubject.next({
      ...current,
      ...config
    });
  }

  /**
   * Toggle minimap visibility
   */
  toggleMinimap(): void {
    const current = this.minimapConfigSubject.getValue();
    this.updateMinimapConfig({ enabled: !current.enabled });
  }

  /**
   * Get minimap configuration
   */
  getMinimapConfig(): MinimapConfig {
    return this.minimapConfigSubject.getValue();
  }

  /**
   * Update viewport padding
   */
  updateViewportPadding(padding: number): void {
    this.viewportPaddingSubject.next(padding);
  }

  /**
   * Fit all content to view
   */
  fitContentToView(): void {
    const containerSize = this.getContainerSize();
    const contentBounds = this.nodeManagementService.getContentBounds();
    
    if (containerSize.width > 0 && containerSize.height > 0) {
      this.canvasService.fitToView(contentBounds, containerSize);
    }
  }

  /**
   * Center view on specific node
   */
  centerOnNode(nodeId: string): void {
    const node = this.nodeManagementService.getNode(nodeId);
    if (!node) return;

    const containerSize = this.getContainerSize();
    const nodeCenterX = node.position.x + node.data.width / 2;
    const nodeCenterY = node.position.y + node.data.height / 2;
    
    const viewport = this.canvasService.getViewport();
    const newX = containerSize.width / 2 - nodeCenterX * viewport.zoom;
    const newY = containerSize.height / 2 - nodeCenterY * viewport.zoom;
    
    this.canvasService.updateViewport({
      x: newX,
      y: newY
    });
  }

  /**
   * Center view on selected nodes
   */
  centerOnSelection(): void {
    const selectedNodes = this.nodeManagementService.getNodes().filter(node => node.selected);
    
    if (selectedNodes.length === 0) return;

    // Calculate bounds of selected nodes
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    selectedNodes.forEach(node => {
      minX = Math.min(minX, node.position.x);
      minY = Math.min(minY, node.position.y);
      maxX = Math.max(maxX, node.position.x + node.data.width);
      maxY = Math.max(maxY, node.position.y + node.data.height);
    });

    const centerX = (minX + maxX) / 2;
    const centerY = (minY + maxY) / 2;
    
    const containerSize = this.getContainerSize();
    const viewport = this.canvasService.getViewport();
    const newX = containerSize.width / 2 - centerX * viewport.zoom;
    const newY = containerSize.height / 2 - centerY * viewport.zoom;
    
    this.canvasService.updateViewport({
      x: newX,
      y: newY
    });
  }

  /**
   * Zoom to fit selected nodes
   */
  zoomToFitSelection(): void {
    const selectedNodes = this.nodeManagementService.getNodes().filter(node => node.selected);
    
    if (selectedNodes.length === 0) return;

    // Calculate bounds of selected nodes
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    selectedNodes.forEach(node => {
      minX = Math.min(minX, node.position.x);
      minY = Math.min(minY, node.position.y);
      maxX = Math.max(maxX, node.position.x + node.data.width);
      maxY = Math.max(maxY, node.position.y + node.data.height);
    });

    const containerSize = this.getContainerSize();
    this.canvasService.fitToView({ minX, minY, maxX, maxY }, containerSize);
  }

  /**
   * Check if point is within viewport
   */
  isPointInViewport(x: number, y: number): boolean {
    const bounds = this.calculateViewportBounds(
      this.canvasService.getViewport(),
      this.getContainerSize(),
      0 // No padding for point check
    );
    
    return x >= bounds.left && x <= bounds.right && 
           y >= bounds.top && y <= bounds.bottom;
  }

  /**
   * Get viewport center in canvas coordinates
   */
  getViewportCenter(): { x: number; y: number } {
    const viewport = this.canvasService.getViewport();
    const containerSize = this.getContainerSize();
    
    return {
      x: (containerSize.width / 2 - viewport.x) / viewport.zoom,
      y: (containerSize.height / 2 - viewport.y) / viewport.zoom
    };
  }

  /**
   * Calculate minimap viewport rectangle
   */
  getMinimapViewportRect(): { x: number; y: number; width: number; height: number } {
    const viewport = this.canvasService.getViewport();
    const containerSize = this.getContainerSize();
    const minimapConfig = this.getMinimapConfig();
    
    const viewportBounds = this.calculateViewportBounds(viewport, containerSize, 0);
    
    return {
      x: viewportBounds.left * minimapConfig.scale,
      y: viewportBounds.top * minimapConfig.scale,
      width: viewportBounds.width * minimapConfig.scale,
      height: viewportBounds.height * minimapConfig.scale
    };
  }

  /**
   * Convert minimap coordinates to canvas coordinates
   */
  minimapToCanvasPosition(minimapX: number, minimapY: number): { x: number; y: number } {
    const minimapConfig = this.getMinimapConfig();
    return {
      x: minimapX / minimapConfig.scale,
      y: minimapY / minimapConfig.scale
    };
  }

  /**
   * Pan to minimap position
   */
  panToMinimapPosition(minimapX: number, minimapY: number): void {
    const canvasPos = this.minimapToCanvasPosition(minimapX, minimapY);
    const containerSize = this.getContainerSize();
    const viewport = this.canvasService.getViewport();
    
    const newX = containerSize.width / 2 - canvasPos.x * viewport.zoom;
    const newY = containerSize.height / 2 - canvasPos.y * viewport.zoom;
    
    this.canvasService.updateViewport({
      x: newX,
      y: newY
    });
  }
}
