.accordion-item {
  border-radius: 0.5rem;
  background-color: var(--card-bg);
  box-shadow: 0 2px 5px var(--card-shadow);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
  font-family: 'Mulish', sans-serif;

  &:hover {
    box-shadow: 0 4px 8px var(--card-hover-shadow);
  }
}

.accordion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  cursor: pointer;
  background-color: var(--card-bg);
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--dropdown-hover-bg);
  }
}

.accordion-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-color);
  font-family: 'Mulish', sans-serif;
}

.toggle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  position: relative;
  border: none;
  cursor: pointer;
  width: 30px;
  height: 30px;
  padding: 0;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  
  /* Create gradient border with pseudo-element */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-radius: 4px;
    padding: 1px; /* Border width */
    background: var(--button-gradient);
    -webkit-mask: 
      linear-gradient(#fff 0 0) content-box, 
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }
  
  /* The SVG is styled directly in the HTML with the gradient */
  svg {
    z-index: 1; /* Ensure the SVG is above the border */
    transition: transform 0.3s ease; /* Move the transition to the SVG */
  }

  &:hover {
    background-color: var(--dropdown-hover-bg);
  }

  &.open {
    svg {
      transform: rotate(180deg); /* Rotate only the SVG when open */
    }
  }
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;

  &.open {
    max-height: 1000px; // Adjust based on your content
    transition: max-height 0.5s ease-in;
  }
}

.content-inner {
  padding: 0 1.5rem 1.5rem;
  font-family: 'Mulish', sans-serif;
} 