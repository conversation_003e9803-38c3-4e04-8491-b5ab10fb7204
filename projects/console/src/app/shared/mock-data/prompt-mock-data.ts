import { CardData } from '../models/card.model';

export const MOCK_PROMPTS: CardData[] = [
  {
    id: '1',
    title: 'PL1 Code Conversion to .Net',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'code-conversion',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '2',
    title: 'Upgrade Angular package.json',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'upgrade',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '3',
    title: 'SQL_TO_SNOWFLAKE',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'conversion',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '4',
    title: 'TEST_SCNERIOS_TO_SELENIUM_TEST_SCRIPT_GENERATION',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'test-generation',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '5',
    title: 'TEST_STEPS_GENERATION',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'test-generation',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '6',
    title: 'Generate Unit Tests',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'test-generation',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '7',
    title: 'Ruby to Springboot',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'code-conversion',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '8',
    title: 'API Documentation Generator',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'documentation',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '9',
    title: 'React to Angular Migration',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'migration',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '10',
    title: 'Java to Python Converter',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'code-conversion',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '11',
    title: 'Code Review Assistant',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'code-review',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '12',
    title: 'Performance Optimization Guide',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'optimization',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '13',
    title: 'Security Audit Checklist',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'security',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '14',
    title: 'Database Schema Generator',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'generation',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '15',
    title: 'Infrastructure as Code Template',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'infrastructure',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '16',
    title: 'Cloud Migration Plan',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'cloud',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '17',
    title: 'API Design Standards',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'standards',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '18',
    title: 'Code Documentation Generator',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'documentation',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '19',
    title: 'DevOps Pipeline Setup',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'devops',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '20',
    title: 'Microservices Architecture Guide',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'architecture',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '21',
    title: 'AI Model Training Workflow',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'ai',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '22',
    title: 'Data Migration Strategy',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'data',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '23',
    title: 'UI/UX Design Guidelines',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'design',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  },
  {
    id: '24',
    title: 'Mobile App Framework Selection',
    tags: [
      { label: 'Ascendion' },
      { label: 'Platform Engineering' },
      { label: 'Digital Ascender' },
      { label: 'Revamp Demo' }
    ],
    createdDate: '1/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ],
    // Filter properties
    category: 'mobile',
    client: 'ascendion',
    department: 'platform-engineering',
    role: 'digital-ascender',
    project: 'revamp-demo'
  }
]; 