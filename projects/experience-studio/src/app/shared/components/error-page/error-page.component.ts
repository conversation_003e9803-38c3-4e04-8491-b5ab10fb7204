import { Component, Input, OnInit, Output, EventEmitter, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject } from 'rxjs';
import { trigger, state, style, animate, transition, keyframes, query, stagger } from '@angular/animations';
// import { createLogger } from '../../../core/utils/logger';
import {createLogger} from '../../utils/logger';

@Component({
  selector: 'app-error-page',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './error-page.component.html',
  styleUrls: ['./error-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('eyesBlink', [
      state('open', style({
        transform: 'scaleY(1)'
      })),
      state('closed', style({
        transform: 'scaleY(0.1)'
      })),
      transition('open <=> closed', animate('300ms ease-in-out'))
    ]),
    trigger('robotShake', [
      state('idle', style({
        transform: 'translateX(0)'
      })),
      state('shaking', style({
        transform: 'translateX(0)'
      })),
      transition('idle => shaking', [
        animate('500ms ease-in-out', keyframes([
          style({ transform: 'translateX(0)', offset: 0 }),
          style({ transform: 'translateX(-5px)', offset: 0.1 }),
          style({ transform: 'translateX(5px)', offset: 0.2 }),
          style({ transform: 'translateX(-5px)', offset: 0.3 }),
          style({ transform: 'translateX(5px)', offset: 0.4 }),
          style({ transform: 'translateX(-5px)', offset: 0.5 }),
          style({ transform: 'translateX(5px)', offset: 0.6 }),
          style({ transform: 'translateX(-5px)', offset: 0.7 }),
          style({ transform: 'translateX(5px)', offset: 0.8 }),
          style({ transform: 'translateX(-5px)', offset: 0.9 }),
          style({ transform: 'translateX(0)', offset: 1 })
        ]))
      ])
    ]),
    trigger('fadeInUp', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('500ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ]),
    trigger('errorTextAnimation', [
      transition(':enter', [
        query('.error-char-container', [
          style({ opacity: 0, transform: 'translateY(20px)' }),
          stagger(100, [
            animate('500ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
          ])
        ], { optional: true })
      ])
    ]),
    trigger('pulseAnimation', [
      state('normal', style({
        transform: 'scale(1)'
      })),
      state('pulse', style({
        transform: 'scale(1)'
      })),
      transition('normal => pulse', [
        animate('1000ms ease-in-out', keyframes([
          style({ transform: 'scale(1)', offset: 0 }),
          style({ transform: 'scale(1.05)', offset: 0.5 }),
          style({ transform: 'scale(1)', offset: 1 })
        ]))
      ])
    ]),
    trigger('rotateAnimation', [
      state('normal', style({
        transform: 'rotate(0deg)'
      })),
      state('rotating', style({
        transform: 'rotate(360deg)'
      })),
      transition('normal => rotating', [
        animate('1000ms ease-in-out')
      ])
    ])
  ]
})
export class ErrorPageComponent implements OnInit, OnDestroy {

  @Input() errorDescription: string = '';
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() terminalOutput: string = '';
  @Input() showTerminal: boolean = false;
  @Input() progressState: string = '';

  @Output() retry = new EventEmitter<void>();
  @Output() goHome = new EventEmitter<void>();
  @Output() showDetails = new EventEmitter<void>();

  // Reactive properties using BehaviorSubjects
  eyesState$ = new BehaviorSubject<'open' | 'closed'>('open');
  robotState$ = new BehaviorSubject<'idle' | 'shaking'>('idle');
  pulseState$ = new BehaviorSubject<'normal' | 'pulse'>('normal');
  rotateState$ = new BehaviorSubject<'normal' | 'rotating'>('normal');
  retryCount$ = new BehaviorSubject<number>(0);
  homeButtonEnabled$ = new BehaviorSubject<boolean>(false);
  errorLetters$ = new BehaviorSubject<{ char: string, state: 'normal' | 'hovered', animationTrigger: string }[]>([]);
  animatedDescription$ = new BehaviorSubject<string>('');
  private typingInterval: any;
  private blinkInterval: any;
  private shakeInterval: any;
  private pulseInterval: any;
  private rotateInterval: any;

  // Logger instance
  private logger = createLogger('ErrorPageComponent');

  constructor() { }

  ngOnInit(): void {
    // Initialize the ERROR letters array using reactive pattern
    this.errorLetters$.next([
      { char: 'E', state: 'normal', animationTrigger: '' },
      { char: 'R', state: 'normal', animationTrigger: '' },
      { char: 'R', state: 'normal', animationTrigger: '' },
      { char: 'O', state: 'normal', animationTrigger: '' },
      { char: 'R', state: 'normal', animationTrigger: '' }
    ]);

    // Start the animations
    this.startBlinking();
    this.startShaking();
    this.startPulsing();
    this.startRotating();

    // Start typing animation for error description
    this.startTypingAnimation();
  }

  ngOnDestroy(): void {
    // Clear all intervals to prevent memory leaks
    if (this.blinkInterval) clearInterval(this.blinkInterval);
    if (this.shakeInterval) clearInterval(this.shakeInterval);
    if (this.pulseInterval) clearInterval(this.pulseInterval);
    if (this.rotateInterval) clearInterval(this.rotateInterval);
    if (this.typingInterval) clearInterval(this.typingInterval);
  }

  startBlinking(): void {
    // Blink every 3 seconds using reactive pattern
    this.blinkInterval = setInterval(() => {
      this.eyesState$.next('closed');
      setTimeout(() => {
        this.eyesState$.next('open');
      }, 300);
    }, 3000);
  }

  startShaking(): void {
    // Shake the robot every 5 seconds using reactive pattern
    this.shakeInterval = setInterval(() => {
      this.robotState$.next('shaking');
      setTimeout(() => {
        this.robotState$.next('idle');
      }, 500);
    }, 5000);
  }

  startPulsing(): void {
    // Pulse animation every 3 seconds using reactive pattern
    this.pulseInterval = setInterval(() => {
      this.pulseState$.next('pulse');
      setTimeout(() => {
        this.pulseState$.next('normal');
      }, 1000);
    }, 3000);
  }

  startRotating(): void {
    // Rotate animation every 4 seconds using reactive pattern
    this.rotateInterval = setInterval(() => {
      this.rotateState$.next('rotating');
      setTimeout(() => {
        this.rotateState$.next('normal');
      }, 1000);
    }, 4000);
  }

  startTypingAnimation(): void {
    // Clear any existing interval
    if (this.typingInterval) clearInterval(this.typingInterval);

    // Reset the animated description using reactive pattern
    this.animatedDescription$.next('');

    // Get the full description
    const fullDescription = this.errorDescription;
    let charIndex = 0;

    // Start typing animation using reactive pattern
    this.typingInterval = setInterval(() => {
      if (charIndex < fullDescription.length) {
        const currentText = this.animatedDescription$.value + fullDescription.charAt(charIndex);
        this.animatedDescription$.next(currentText);
        charIndex++;
      } else {
        // Stop the interval when done
        clearInterval(this.typingInterval);
      }
    }, 30); // Adjust speed as needed
  }

  onLetterMouseEnter(index: number): void {
    const currentLetters = this.errorLetters$.value;
    currentLetters[index].state = 'hovered';
    this.errorLetters$.next([...currentLetters]);
  }

  onLetterMouseLeave(index: number): void {
    const currentLetters = this.errorLetters$.value;
    currentLetters[index].state = 'normal';
    this.errorLetters$.next([...currentLetters]);
  }

  onRetryClick(): void {
    // Increment retry counter using reactive pattern
    const currentRetryCount = this.retryCount$.value + 1;
    this.retryCount$.next(currentRetryCount);

    // Enable home button after 3 retries
    if (currentRetryCount >= 3 && !this.homeButtonEnabled$.value) {
      this.homeButtonEnabled$.next(true);

      // Celebrate by animating all letters at once
      this.celebrateHomeButtonEnabled();
    }

    // Trigger shake animation before emitting retry event using reactive pattern
    this.robotState$.next('shaking');
    setTimeout(() => {
      this.robotState$.next('idle');
      this.retry.emit();
    }, 500);
  }

  /**
   * Celebrate when the home button becomes enabled by animating all letters
   */
  celebrateHomeButtonEnabled(): void {
    // Animate each letter in sequence using reactive pattern
    const currentLetters = this.errorLetters$.value;
    currentLetters.forEach((letter, index) => {
      setTimeout(() => {
        // Set letter to hovered state
        const updatedLetters = this.errorLetters$.value;
        updatedLetters[index].state = 'hovered';
        this.errorLetters$.next([...updatedLetters]);

        // Reset after animation
        setTimeout(() => {
          const resetLetters = this.errorLetters$.value;
          resetLetters[index].state = 'normal';
          this.errorLetters$.next([...resetLetters]);
        }, 500);
      }, index * 200); // Stagger the animations
    });
  }

  onGoHomeClick(): void {
    // Only allow going home if the button is enabled (after 3 retries) using reactive pattern
    if (this.homeButtonEnabled$.value) {
      this.goHome.emit();
    } else {
      // Shake animation to indicate button is disabled using reactive pattern
      this.robotState$.next('shaking');
      setTimeout(() => {
        this.robotState$.next('idle');
      }, 500);
    }
  }

  onShowDetailsClick(): void {
    this.showDetails.emit();
    this.showTerminal = !this.showTerminal;
  }

  // Parse and format JSON error messages for better display
  parseErrorMessage(message: string): string {
    try {
      if (message && message.includes('{') && message.includes('}')) {
        const jsonStartIndex = message.indexOf('{');
        const jsonEndIndex = message.lastIndexOf('}') + 1;
        const jsonPart = message.substring(jsonStartIndex, jsonEndIndex);
        const parsedData = JSON.parse(jsonPart);

        if (parsedData.message) {
          return parsedData.message;
        }
      }
      return message;
    } catch (e) {
      return message;
    }
  }
}
