<!-- recent-creation.component.html -->
<div class="recent-creation-wrapper">
  <!-- Toggle buttons for Recent/All view -->
  <div class="toggle-layout">
    <div class="toogle-container">
      <button
        class="toggle-btn recent-tab"
        [ngClass]="{ active: currentCategory === 'recent' }"
        (click)="switchCategory('recent')">
        Recent
      </button>
      <button
        class="toggle-btn all-tab"
        [ngClass]="{ active: currentCategory === 'all' }"
        (click)="switchCategory('all')">
        All
      </button>
    </div>
  </div>

  <!-- Cards grid container with slide animation -->
  <div class="cards-container">
    <div
      class="cards-grid slide-recent"
      [ngClass]="{
        'slide-recent': currentCategory === 'recent',
        'slide-all': currentCategory === 'all',
      }">
      <div class="card-wrapper" *ngFor="let option of getCurrentOptions(); trackBy: trackByFn">
        <awe-cards
          [ngClass]="{ selected: isSelected(option.id), 'skeleton-card': isLoading }"
          (click)="handleSelection(option.id)">
          <div content class="card-content">
            <!-- Skeleton loader -->
            <ng-container *ngIf="isLoading; else loadedContent">
              <div class="skeleton-title"></div>
              <div class="skeleton-text"></div>
              <div class="skeleton-text"></div>
              <div class="skeleton-text"></div>
            </ng-container>

            <!-- Actual content -->
            <ng-template #loadedContent>
              <awe-heading variant="s1" type="bold" id="title">{{ option.heading }}</awe-heading>
              <awe-body-text>{{ option.description }}</awe-body-text>
              <div class="action-timestamp-container">
                <div
                  class="card-action-btn"
                  [title]="option.actionText || getDefaultActionText(option.type)">
                  {{ option.actionText || getDefaultActionText(option.type) }}
                </div>
                <div class="timestamp" [title]="option.timestamp">{{ option.timestamp }}</div>
              </div>
            </ng-template>
          </div>
        </awe-cards>
      </div>
    </div>
  </div>
</div>
