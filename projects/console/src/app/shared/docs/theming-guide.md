# Application Theming Guide

This guide explains how to use the application's theming system to create consistent, visually appealing UI components that support both light and dark themes.

## Table of Contents

1. [Theme Variables](#theme-variables)
2. [Using Theme Variables](#using-theme-variables)
3. [Components with Theme Support](#components-with-theme-support)
4. [Theme Toggle Component](#theme-toggle-component)
5. [Creating Theme-Compatible Components](#creating-theme-compatible-components)
6. [Best Practices](#best-practices)

## Theme Variables

The application uses CSS variables defined in `_variables.scss` to manage theming. These variables are defined for both light and dark themes:

```scss
// Light theme colors (default)
:root {
  --bg-color: #ffffff;
  --text-color: #333333;
  --text-secondary: #666666;
  // ... more variables
}

// Dark theme colors
.dark-theme {
  --bg-color: #121212;
  --text-color: #f5f5f5;
  --text-secondary: #b0b0b0;
  // ... more variables
}
```

## Using Theme Variables

To use theme variables in your styles:

```scss
.my-component {
  background-color: var(--bg-color);
  color: var(--text-color);
  border: 1px solid var(--input-border);
  transition: all 0.3s ease; // Always add transitions for smooth theme switching
}
```

## Components with Theme Support

The following components have built-in theme support:

### FormFieldComponent

A versatile form field component that supports multiple input types.

```html
<app-form-field
  label="Username"
  placeholder="Enter username"
  [control]="usernameControl"
  type="text"
  id="username"
  [disabled]="false"
  [readonly]="false"
  hint="Your unique username"
></app-form-field>
```

Supported input types:
- `text`
- `textarea`
- `email`
- `password`
- `number`
- `select`
- `checkbox`

### ButtonComponent

A customizable button component with multiple variants and states.

```html
<app-button
  variant="primary"
  size="medium"
  [fullWidth]="false"
  [disabled]="false"
  [loading]="isLoading"
  (buttonClick)="onButtonClick()"
>
  Submit
</app-button>
```

Supported variants:
- `primary` - Main action buttons
- `secondary` - Secondary actions
- `outline` - Less prominent actions
- `text` - Minimal styling
- `danger` - Destructive actions

Supported sizes:
- `small`
- `medium`
- `large`

### ModalComponent

A modal dialog component for displaying content in a focused overlay.

```html
<app-modal
  title="Confirm Action"
  [isOpen]="isModalOpen"
  size="medium"
  [closeOnClickOutside]="true"
  [showCloseButton]="true"
  (close)="closeModal()"
>
  <p>Modal content goes here</p>
  
  <div modal-footer>
    <app-button variant="outline" (buttonClick)="closeModal()">Cancel</app-button>
    <app-button variant="primary" (buttonClick)="confirmAction()">Confirm</app-button>
  </div>
</app-modal>
```

Supported sizes:
- `small` (400px)
- `medium` (600px)
- `large` (800px)
- `fullscreen` (90% of viewport)

### CardComponent

A versatile card component for displaying content in a container.

```html
<app-card [clickable]="true" [fixedHeight]="false" (cardClick)="onCardClick()">
  <div class="card-header">
    <h3>Card Title</h3>
  </div>
  
  <div class="card-body">
    Content goes here
  </div>
  
  <div class="card-footer">
    Footer content
  </div>
</app-card>
```

### SearchBarComponent

A search input component with theme support.

```html
<app-search-bar
  placeholder="Search..."
  [control]="searchControl"
  (searchChange)="onSearch($event)"
></app-search-bar>
```

### DataCardComponent

A specialized card for displaying data items with tags and actions.

```html
<app-data-card
  [data]="item"
  (cardClick)="onCardClick(item)"
  (actionClick)="onActionClick($event, item)"
></app-data-card>
```

### NavItemComponent

Navigation item component with support for dropdown menus.

```html
<app-nav-item
  label="Dashboard"
  route="/dashboard"
  [selected]="isSelected"
  [hasDropdown]="false"
  (navigateEvent)="navigateTo($event)"
></app-nav-item>
```

## Theme Toggle Component

The application includes a dedicated Theme Toggle component:

```html
<app-theme-toggle></app-theme-toggle>
```

This component provides a visually appealing toggle switch that users can click to switch between light and dark themes. The component automatically saves the user's preference to localStorage.

## Creating Theme-Compatible Components

When creating new components, follow these guidelines:

1. Use theme variables for all colors, backgrounds, and borders
2. Add transition effects for smooth theme switching
3. Test in both light and dark themes
4. Use the `:host-context(.dark-theme)` selector for dark theme specific styles

Example:

```scss
.my-component {
  background-color: var(--card-bg);
  color: var(--text-color);
  border: 1px solid var(--card-border);
  transition: all 0.3s ease;
}

// Dark theme specific adjustments if needed
:host-context(.dark-theme) {
  .my-component {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
}
```

## Best Practices

1. **Consistent Transitions**: Always use the same transition duration for theme changes (0.3s)
2. **Use Semantic Variables**: Use the appropriate semantic variables rather than the raw color values
3. **Test in Both Themes**: Always check your components in both light and dark themes
4. **Accessibility**: Ensure sufficient contrast between text and background colors in both themes
5. **Glass Effect**: Use the standard glass effect pattern for components that need it:
   ```scss
   &::before {
     content: '';
     position: absolute;
     inset: 0;
     z-index: -1;
     background: var(--card-bg);
     opacity: 0.9;
     border-radius: inherit;
   }
   ```
6. **Responsive Design**: Make sure components look good at all screen sizes in both themes
7. **GPU Acceleration**: Use `transform` and `opacity` for animations to leverage GPU acceleration
8. **Avoid Hard-Coded Colors**: Never use hard-coded color values - always use theme variables

## Theme Variables Reference

Here's a quick reference of commonly used theme variables:

### Layout
- `--bg-color`: Main background color
- `--bg-image`: Background image URL
- `--card-bg`: Card background color
- `--card-border`: Card border color
- `--card-shadow`: Card box shadow
- `--card-hover-shadow`: Card box shadow on hover

### Typography
- `--text-color`: Primary text color
- `--text-secondary`: Secondary text color
- `--text-muted`: Muted text color

### Forms
- `--input-bg`: Input background color
- `--input-text`: Input text color
- `--input-border`: Input border color
- `--input-focus-border`: Input border color when focused
- `--input-focus-shadow`: Input shadow when focused

### Interactive Elements
- `--primary-start`: Primary gradient start color
- `--primary-end`: Primary gradient end color
- `--gradient-primary`: Full primary gradient
- `--error-color`: Error/danger color
- `--success-color`: Success color
- `--warning-color`: Warning color

### Navigation
- `--nav-text`: Navigation text color
- `--nav-hover`: Navigation hover background
- `--nav-active`: Navigation active background
- `--nav-active-text`: Navigation active text color 