.suggested-prompt-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.card-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  flex: 1;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px;
  color: var(--text-color, #333333);
}

.prompt-selection-row {
  display: flex;
  align-items: center;
  gap: 16px;
  
  .select-container {
    flex: 1;
  }
}

.edit-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: transparent;
  border: 1px solid var(--primary-start, #6566CD);
  border-radius: 6px;
  color: var(--primary-start, #6566CD);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: rgba(101, 102, 205, 0.05);
  }
  
  svg {
    stroke: var(--primary-start, #6566CD);
  }
}

.two-column-row {
  display: flex;
  gap: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
  
  .column {
    flex: 1;
  }
}

.field-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.field-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: var(--text-color, #333333);
}

.field-content {
  padding: 16px;
  background-color: var(--input-bg, #F8F8F9);
  border-radius: 8px;
  border: 1px solid var(--input-border, #E0E0E0);
  
  p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
    color: var(--input-text, #666666);
  }
}

.enhance-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: transparent;
  border: 1px solid var(--primary-start, #6566CD);
  border-radius: 6px;
  color: var(--primary-start, #6566CD);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: rgba(101, 102, 205, 0.05);
  }
  
  svg {
    stroke: var(--primary-start, #6566CD);
    width: 14px;
    height: 14px;
  }
} 