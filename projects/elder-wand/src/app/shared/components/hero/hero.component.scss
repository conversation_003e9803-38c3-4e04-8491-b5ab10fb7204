.hero-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-family: 'Mulish', -apple-system, 'Roboto', 'Helvetica', sans-serif;
  margin-bottom: 6.2rem;
  margin-top: 6.2rem;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.greeting-section {
  max-width: 800px;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.greeting-header {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  white-space: nowrap;

  awe-icons svg {
    height: 35px;
    width: 27px;
    margin-right: 10px;
    animation: blink 3s ease-in-out infinite;
    /* Apply blink animation to the SVG */
  }
}

.greeting-title {
  color: var(--Neutral-N-1050, #14161F);
  text-align: center;
  font-family: Mulish;
  font-size: 56px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -1.064px;
  white-space: nowrap;
  animation: fadeIn 6s ease-in-out forwards;
  /* Slower fade-in animation */
}

.name {
  background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 118.04%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: Mulish;
  font-size: 56px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -1.064px;
  white-space: nowrap;
}

.greeting-message {
  margin: 0;
  max-width: 800px;
}

p {
  color: var(--Text-Body, #33364D);
  text-align: center;
  font-family: Mulish;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: 150%;
  letter-spacing: -0.456px;
}

.search-bar-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes blink {

  0%,
  100% {
    opacity: 0;
  }

  50% {
    opacity: 0.8;
    /* Slightly reduce opacity for smoother blink */
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .greeting-title {
    font-size: 36px;
  }

  .greeting-message {
    font-size: 18px;
  }
}