import { Component, OnInit } from '@angular/core';
import { AgentsComponent } from '../../shared/components/agents/agents.component';
import { AnalyticsComponent } from '../../shared/components/analytics/analytics.component';
import { NewsBlogsComponent } from '../../shared/components/news-blogs/news-blogs.component';
import { StudiosComponent } from '../../shared/components/studios/studios.component';
import { TrustedClientsComponent } from '../../shared/components/trusted-clients/trusted-clients.component';
import { FooterComponent } from '../../shared/components/footer/footer.component';
import { agentsData } from '../../../assets/data/agents.mock';
import { salesAgent } from '../../../assets/data/flows.mock';
import { projectTeam } from '../../../assets/data/project-team.mock';
import { Agent } from '../../shared/interfaces/agent-list.interface';
import { GlobalStoreService } from '../../shared/service/global-store.service';

@Component({
  selector: 'app-launchpad-home',
  standalone: true,
  imports: [
    FooterComponent,
    TrustedClientsComponent,
    NewsBlogsComponent,
    AgentsComponent,
    StudiosComponent,
    AnalyticsComponent,
  ],
  templateUrl: './launchpad-home.component.html',
  styleUrl: './launchpad-home.component.scss',
})
export class LaunchpadHomeComponent implements OnInit {
  showCount = 8;
  agentsData: Agent[] = [];
  selectedUser: any;

  constructor(private readonly globalStoreService: GlobalStoreService) {}

  ngOnInit() {
    this.globalStoreService.selectedUser.subscribe((user) => {
      this.selectedUser = user;
      this.updateAgentsList();
    });
  }

  private updateAgentsList() {
    if (this.selectedUser?.type === 'Sales') {
      this.agentsData = salesAgent.slice(0, this.showCount);
    } else if (this.selectedUser?.type === 'Project Team') {
      this.agentsData = projectTeam.slice(0, this.showCount);
    } else {
      this.agentsData = agentsData.slice(0, this.showCount);
    }
  }
}
