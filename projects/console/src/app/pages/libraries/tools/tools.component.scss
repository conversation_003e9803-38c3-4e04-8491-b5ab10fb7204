@import '../../../shared/styles/header.scss';
@import '../../../shared/styles/cards.scss';

.tools-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 15px 0;
  position: relative; /* Add positioning context for absolute elements */
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px); /* Fixed height instead of min-height */
  overflow: hidden; /* Prevent container overflow */
}

.tools-header {
  @extend .component-header;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  position: relative;
  z-index: 100; /* Lower than filter-section but still above base elements */
  flex: 1;
  overflow-y: auto;
  margin-top: 24px; /* Default margin when filter is not present */
  max-height: calc(100vh - 300px); /* Set a max height that leaves room for header and footer */
  padding-bottom: 20px; /* Reduced padding since we don't need to scroll to the bottom anymore */
}

/* Make sure all cards have the same height */
app-create-card, app-data-card {
  height: 100%;
  display: block;
}

.filter-section {
  width: 100%;
  overflow: visible;
  transition: max-height 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
  animation: fadeIn 0.3s ease-out;
  position: relative;
  z-index: 500; /* Higher z-index to ensure it's above other content */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 20px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(200, 200, 200, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  color: #666D99;
  font-size: 16px;
}

/* Footer positioning - apply to the actual component instead of using ::ng-deep */
app-page-footer {
  position: sticky;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 200;
  margin-top: auto;
  flex-shrink: 0; /* Prevent footer from shrinking */
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 992px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 576px) {
  .cards-container {
    grid-template-columns: 1fr;
  }
}
