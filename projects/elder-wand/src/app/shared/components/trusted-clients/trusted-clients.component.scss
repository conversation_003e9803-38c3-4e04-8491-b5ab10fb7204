::ng-deep .heading.h2 {
  text-align: center;
  display: inline-flex;
  gap: 8px;

}

.trusted-clients {
  .trusted-clients-container {
    padding-right: 10%;
    padding-left: 10%;
  }

  .header {
    .star {
      font-size: 28px;
    }

    .title-text {
      font-family: Mulish;
      font-size: 48px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.912px;
    }

    .gradient-text {
      background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 118.04%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .description {
      color: var(--Text-Caption, #666D99);
      text-align: center;
      font-family: Mulish;
      font-size: 24px;
      font-style: normal;
      font-weight: 500;
      line-height: 150%;
      /* 36px */
    }
  }

  .logo-container {
    padding: 20px 40px;
    gap: 40px;

    .logo-item {
      flex: 0 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        height: auto;
        width: 100%;
        max-width: 200px;
        object-fit: contain;
      }
    }
  }
}