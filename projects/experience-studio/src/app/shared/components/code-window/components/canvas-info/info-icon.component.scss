// ===== INFO ICON COMPONENT STYLES =====

.info-icon {
  display: block;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));

  // Default light theme colors - Gray/Black on white background
  --info-icon-stroke: #374151;
  --info-icon-fill: rgba(55, 65, 81, 0.1);
  --info-icon-color: #374151;

  // Icon elements
  .icon-circle {
    transition: all 0.3s ease;
  }

  .icon-dot {
    transition: all 0.3s ease;
  }

  .icon-line {
    transition: all 0.3s ease;
  }

  // Hover state
  &:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));

    .icon-circle {
      --info-icon-stroke: #111827;
      --info-icon-fill: rgba(17, 24, 39, 0.15);
    }

    .icon-dot,
    .icon-line {
      --info-icon-color: #111827;
    }
  }

  // Active state
  &.active {
    transform: scale(1.1);
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.2));

    .icon-circle {
      --info-icon-stroke: #111827;
      --info-icon-fill: rgba(17, 24, 39, 0.2);
    }

    .icon-dot,
    .icon-line {
      --info-icon-color: #111827;
    }
  }
}

// ===== LIGHT THEME SPECIFIC =====

:host {
  .info-icon {
    // Light theme: Gray/Black icon on white background
    --info-icon-stroke: #374151;
    --info-icon-fill: rgba(55, 65, 81, 0.1);
    --info-icon-color: #374151;
  }
}

// ===== DARK THEME SPECIFIC =====

:host-context(.dark-theme) {
  .info-icon {
    // Dark theme: Light gray/white icon on dark background
    --info-icon-stroke: #e5e7eb;
    --info-icon-fill: rgba(229, 231, 235, 0.15);
    --info-icon-color: #e5e7eb;

    // Enhanced shadow for dark mode visibility
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4));

    &:hover {
      filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.5));

      .icon-circle {
        --info-icon-stroke: #f9fafb;
        --info-icon-fill: rgba(249, 250, 251, 0.2);
      }

      .icon-dot,
      .icon-line {
        --info-icon-color: #f9fafb;
      }
    }

    &.active {
      filter: drop-shadow(0 3px 8px rgba(0, 0, 0, 0.6));

      .icon-circle {
        --info-icon-stroke: #f9fafb;
        --info-icon-fill: rgba(249, 250, 251, 0.25);
      }

      .icon-dot,
      .icon-line {
        --info-icon-color: #f9fafb;
      }
    }
  }
}

// ===== ACCESSIBILITY =====

@media (prefers-reduced-motion: reduce) {
  .info-icon {
    transition: none;

    &:hover,
    &.active {
      transform: none;
    }
  }
}

// ===== HIGH CONTRAST MODE =====

@media (prefers-contrast: high) {
  .info-icon {
    --info-icon-stroke: currentColor;
    --info-icon-fill: transparent;
    --info-icon-color: currentColor;

    filter: none;

    &:hover,
    &.active {
      filter: none;
      --info-icon-stroke: currentColor;
      --info-icon-color: currentColor;
    }
  }
}

// ===== RESPONSIVE SIZING =====

@media (max-width: 768px) {
  .info-icon {
    // Slightly larger on mobile for better touch targets
    transform: scale(1.1);

    &:hover {
      transform: scale(1.15);
    }

    &.active {
      transform: scale(1.2);
    }
  }
}

@media (max-width: 480px) {
  .info-icon {
    // Even larger on small mobile devices
    transform: scale(1.2);

    &:hover {
      transform: scale(1.25);
    }

    &.active {
      transform: scale(1.3);
    }
  }
}
