import { Injectable } from '@angular/core';
import { FilterConfig, FilterGroup } from '../models/filter.model';

@Injectable({
  providedIn: 'root'
})
export class FilterService {
  private filterConfigs: { [key: string]: FilterConfig } = {};

  constructor() {
    // Initialize with default filter configurations
    this.initializeDefaultFilters();
  }

  private initializeDefaultFilters(): void {
    // Agent filters configuration
    this.filterConfigs['agents'] = {
      filterGroups: [
        {
          id: 'agentType',
          label: 'Select Agent type',
          options: [
            { value: 'individual', label: 'Individual' },
            { value: 'team', label: 'Team' },
            { value: 'organization', label: 'Organization' }
          ]
        },
        {
          id: 'organization',
          label: 'Select Organisation',
          options: [
            { value: 'ascendion', label: 'Ascendion' },
            { value: 'client1', label: 'Client 1' },
            { value: 'client2', label: 'Client 2' },
            { value: 'client3', label: 'Client 3' }
          ]
        },
        {
          id: 'domain',
          label: 'Select Domain',
          options: [
            { value: 'platform-engineering', label: 'Platform Engineering' },
            { value: 'data-analytics', label: 'Data Analytics' },
            { value: 'cloud-services', label: 'Cloud Services' },
            { value: 'ui-ux', label: 'UI/UX' }
          ]
        },
        {
          id: 'project',
          label: 'Select Project',
          options: [
            { value: 'digital-ascender', label: 'Digital Ascender' },
            { value: 'solution-architect', label: 'Solution Architect' },
            { value: 'tech-lead', label: 'Tech Lead' },
            { value: 'developer', label: 'Developer' }
          ]
        },
        {
          id: 'team',
          label: 'Select Team',
          options: [
            { value: 'revamp-demo', label: 'Revamp Demo' },
            { value: 'chatbot-ai', label: 'Chatbot AI' },
            { value: 'data-migration', label: 'Data Migration' },
            { value: 'cloud-transform', label: 'Cloud Transformation' }
          ]
        },
        {
          id: 'relevance',
          label: 'Select Relevance',
          options: [
            { value: 'all', label: 'All' },
            { value: 'custom', label: 'Custom' },
            { value: 'today', label: 'Today' },
            { value: 'yesterday', label: 'Yesterday' },
            { value: 'last-week', label: 'Last Week' },
            { value: 'last-month', label: 'Last Month' }
          ]
        }
      ]
    };

    // Guardrails filters configuration
    this.filterConfigs['guardrails'] = {
      filterGroups: [
        {
          id: 'guardrailType',
          label: 'Guardrail Type',
          options: [
            { value: 'safety', label: 'Safety' },
            { value: 'security', label: 'Security' },
            { value: 'compliance', label: 'Compliance' },
            { value: 'domain', label: 'Domain' }
          ]
        },
        {
          id: 'domain',
          label: 'Domain',
          options: [
            { value: 'content', label: 'Content' },
            { value: 'healthcare', label: 'Healthcare' },
            { value: 'finance', label: 'Finance' },
            { value: 'pii', label: 'PII' },
            { value: 'legal', label: 'Legal' },
            { value: 'education', label: 'Education' },
            { value: 'code', label: 'Code' },
            { value: 'pharma', label: 'Pharma' },
            { value: 'language', label: 'Language' },
            { value: 'medical', label: 'Medical' },
            { value: 'ip', label: 'IP' },
            { value: 'government', label: 'Government' }
          ]
        },
        {
          id: 'category',
          label: 'Category',
          options: [
            { value: 'ai-safety', label: 'AI Safety' },
            { value: 'hipaa', label: 'HIPAA' },
            { value: 'banking', label: 'Banking' },
            { value: 'data-protection', label: 'Data Protection' },
            { value: 'compliance', label: 'Compliance' },
            { value: 'academic', label: 'Academic' },
            { value: 'eu', label: 'EU' },
            { value: 'cybersecurity', label: 'Cybersecurity' },
            { value: 'moderation', label: 'Moderation' },
            { value: 'fraud', label: 'Fraud' },
            { value: 'communication', label: 'Communication' },
            { value: 'california', label: 'California' },
            { value: 'healthcare', label: 'Healthcare' },
            { value: 'protection', label: 'Protection' },
            { value: 'security', label: 'Security' },
            { value: 'legal', label: 'Legal' },
            { value: 'finance', label: 'Finance' }
          ]
        },
        {
          id: 'complexity',
          label: 'Complexity',
          options: [
            { value: 'basic', label: 'Basic' },
            { value: 'intermediate', label: 'Intermediate' },
            { value: 'advanced', label: 'Advanced' }
          ]
        },
        {
          id: 'access',
          label: 'Access',
          options: [
            { value: 'public', label: 'Public' },
            { value: 'team', label: 'Team' },
            { value: 'private', label: 'Private' },
            { value: 'organization', label: 'Organization' }
          ]
        }
      ]
    };

    // Prompt filters configuration
    this.filterConfigs['prompts'] = {
      filterGroups: [
        {
          id: 'organization',
          label: 'Ascendion',
          options: [
            { value: 'ascendion', label: 'Ascendion' }
          ]
        },
        {
          id: 'domain',
          label: 'Platform Engineering',
          options: [
            { value: 'platform-engineering', label: 'Platform Engineering' }
          ]
        },
        {
          id: 'project',
          label: 'Console Revamp',
          options: [
            { value: 'digital-ascender', label: 'Digital Ascender' }
          ]
        },
        {
          id: 'team',
          label: 'Alpha Bravo',
          options: [
            { value: 'revamp-demo', label: 'Revamp Demo' }
          ]
        },
        {
          id: 'relevance',
          label: 'Recently Used',
          options: [
            { value: 'all', label: 'All' },
            { value: 'custom', label: 'Custom' },
            { value: 'today', label: 'Today' },
            { value: 'yesterday', label: 'Yesterday' },
            { value: 'last-week', label: 'Last Week' },
            { value: 'last-month', label: 'Last Month' }
          ]
        }
      ]
    };
    
    // Models filters configuration
    this.filterConfigs['models'] = {
      filterGroups: [
        {
          id: 'modelType',
          label: 'Model Type',
          options: [
            { value: 'enterprise', label: 'Enterprise' },
            { value: 'community', label: 'Community' }
          ]
        },
        {
          id: 'provider',
          label: 'Provider',
          options: [
            { value: 'openai', label: 'OpenAI' },
            { value: 'anthropic', label: 'Anthropic' },
            { value: 'meta', label: 'Meta' },
            { value: 'google', label: 'Google' },
            { value: 'mistral-ai', label: 'Mistral AI' },
            { value: 'stability-ai', label: 'Stability AI' }
          ]
        },
        {
          id: 'category',
          label: 'Category',
          options: [
            { value: 'llm', label: 'LLM' },
            { value: 'computer-vision', label: 'Computer Vision' },
            { value: 'audio', label: 'Audio' },
            { value: 'nlp', label: 'NLP' },
            { value: 'multimodal', label: 'Multimodal' }
          ]
        },
        {
          id: 'capability',
          label: 'Capability',
          options: [
            { value: 'general-purpose', label: 'General Purpose' },
            { value: 'multimodal', label: 'Multimodal' },
            { value: 'image-generation', label: 'Image Generation' },
            { value: 'video-generation', label: 'Video Generation' },
            { value: 'speech-to-text', label: 'Speech-to-Text' },
            { value: 'music-generation', label: 'Music Generation' },
            { value: 'object-detection', label: 'Object Detection' },
            { value: 'image-classification', label: 'Image Classification' },
            { value: 'embedding', label: 'Embedding' }
          ]
        },
        {
          id: 'license',
          label: 'License',
          options: [
            { value: 'commercial', label: 'Commercial' },
            { value: 'open-source', label: 'Open Source' }
          ]
        }
      ]
    };

    // Workflows filters configuration
    this.filterConfigs['workflows'] = {
      filterGroups: [
        {
          id: 'userType',
          label: 'User Type',
          options: [
            { value: 'business', label: 'Business' },
            { value: 'enterprise', label: 'Enterprise' },
            { value: 'education', label: 'Education' }
          ]
        },
        {
          id: 'client',
          label: 'Client',
          options: [
            { value: 'retail', label: 'Retail' },
            { value: 'financial', label: 'Financial' },
            { value: 'healthcare', label: 'Healthcare' },
            { value: 'manufacturing', label: 'Manufacturing' },
            { value: 'services', label: 'Services' },
            { value: 'insurance', label: 'Insurance' },
            { value: 'utilities', label: 'Utilities' }
          ]
        },
        {
          id: 'department',
          label: 'Department',
          options: [
            { value: 'sales', label: 'Sales' },
            { value: 'marketing', label: 'Marketing' },
            { value: 'operations', label: 'Operations' },
            { value: 'customer-support', label: 'Customer Support' },
            { value: 'hr', label: 'Human Resources' },
            { value: 'accounting', label: 'Accounting' },
            { value: 'legal', label: 'Legal' }
          ]
        },
        {
          id: 'category',
          label: 'Category',
          options: [
            { value: 'automation', label: 'Automation' },
            { value: 'analysis', label: 'Analysis' },
            { value: 'recommendation', label: 'Recommendation' },
            { value: 'generation', label: 'Generation' },
            { value: 'optimization', label: 'Optimization' },
            { value: 'prediction', label: 'Prediction' },
            { value: 'detection', label: 'Detection' },
            { value: 'personalization', label: 'Personalization' },
            { value: 'summarization', label: 'Summarization' }
          ]
        }
      ]
    };

    // Tools filters configuration
    this.filterConfigs['tools'] = {
      filterGroups: [
        {
          id: 'userType',
          label: 'User Type',
          options: [
            { value: 'developer', label: 'Developer' },
            { value: 'analyst', label: 'Analyst' },
            { value: 'business', label: 'Business' }
          ]
        },
        {
          id: 'client',
          label: 'Client Type',
          options: [
            { value: 'technology', label: 'Technology' },
            { value: 'financial', label: 'Financial' },
            { value: 'legal', label: 'Legal' },
            { value: 'services', label: 'Services' },
            { value: 'publishing', label: 'Publishing' },
            { value: 'retail', label: 'Retail' },
            { value: 'marketing', label: 'Marketing' },
            { value: 'business-intelligence', label: 'Business Intelligence' }
          ]
        },
        {
          id: 'department',
          label: 'Department',
          options: [
            { value: 'engineering', label: 'Engineering' },
            { value: 'data-science', label: 'Data Science' },
            { value: 'document-management', label: 'Document Management' },
            { value: 'ai-research', label: 'AI Research' },
            { value: 'communications', label: 'Communications' },
            { value: 'content-creation', label: 'Content Creation' },
            { value: 'database-admin', label: 'Database Administration' },
            { value: 'operations', label: 'Operations' },
            { value: 'finance', label: 'Finance' },
            { value: 'digital-marketing', label: 'Digital Marketing' },
            { value: 'data-analysis', label: 'Data Analysis' }
          ]
        },
        {
          id: 'category',
          label: 'Category',
          options: [
            { value: 'integration', label: 'Integration' },
            { value: 'extraction', label: 'Extraction' },
            { value: 'processing', label: 'Processing' },
            { value: 'ai', label: 'AI' },
            { value: 'communication', label: 'Communication' },
            { value: 'document', label: 'Document' },
            { value: 'migration', label: 'Migration' },
            { value: 'management', label: 'Management' },
            { value: 'finance', label: 'Finance' },
            { value: 'analysis', label: 'Analysis' },
            { value: 'visualization', label: 'Visualization' }
          ]
        }
      ]
    };

    // Add more filter configurations as needed
    // this.filterConfigs['workflows'] = {...};
  }

  getFilterConfig(key: string): FilterConfig {
    return this.filterConfigs[key] || { filterGroups: [] };
  }

  // Filter data based on selected filters
  filterData<T>(data: T[], filters: {[key: string]: string}, propertyMap: {[key: string]: string}): T[] {
    if (!data || data.length === 0 || Object.keys(filters).length === 0) {
      return data;
    }

    return data.filter(item => {
      // Check if the item passes all selected filters
      return Object.entries(filters).every(([filterId, filterValue]) => {
        const propertyName = propertyMap[filterId];
        if (!propertyName) {
          return true; // Skip filters that don't have a property mapping
        }

        // Handle nested properties using dot notation (e.g., "user.type")
        if (propertyName.includes('.')) {
          const props = propertyName.split('.');
          let value: any = item;
          
          for (const prop of props) {
            if (value === null || value === undefined) return false;
            value = value[prop as keyof typeof value];
          }
          
          return Array.isArray(value) 
            ? value.includes(filterValue) 
            : value === filterValue;
        }
        
        // Handle simple properties
        const itemValue = (item as any)[propertyName];
        return Array.isArray(itemValue) 
          ? itemValue.includes(filterValue) 
          : itemValue === filterValue;
      });
    });
  }
} 