import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError, timeout } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { UserSignatureService } from './user-signature.service';
import { cacheHelpers } from '../interceptors/cache.interceptor';
import { createLogger } from '../utils/logger';
import { ApiRetryService } from './api-retry.service';

export interface GenerateUIDesignRequest {
  prompt: string;
  // Commented out for wireframe-generation/generate API - only prompt needed
  // input_prompt: string;
  // title: string;
  // application_target: 'mobile' | 'web';
}

export interface GenerateUIDesignResponse {
  job_id: string;
  project_id: string;
  status: string;
  message?: string;
}

// New interfaces for the /intro API
export interface IntroAPIRequest {
  code: IntroCodeItem[];
  user_request: string;
}

export interface IntroCodeItem {
  fileName: string;
  content: string;
}

export interface IntroAPIResponse {
  message?: string;
  text?: string;
  content?: string;
  // The API might return the intro text in different fields
}

// Type for handling both string and object responses from intro API
export type IntroAPIResponseType = string | IntroAPIResponse;

export interface UIDesignData {
  cardTitle: string;
  prompt: string;
  applicationTarget: 'mobile' | 'web';
  timestamp: string;
}

export interface UIDesignPage {
  fileName: string;
  content: string;
}

export interface UIDesignResponseData {
  pages: UIDesignPage[];
  jobId: string;
  projectId: string;
}

@Injectable({
  providedIn: 'root'
})
export class GenerateUIDesignService {
  private apiUrl = environment.apiUrl;
  private endpoint = '/wireframe-generation/generate';
  private introEndpoint = '/wireframe-generation/intro';
  private logger = createLogger('GenerateUIDesignService');

  // BehaviorSubjects for state management
  private cardTitleSubject = new BehaviorSubject<string>('');
  private promptDataSubject = new BehaviorSubject<string>('');
  private applicationTargetSubject = new BehaviorSubject<'mobile' | 'web'>('mobile'); // Default to mobile
  private uiDesignDataSubject = new BehaviorSubject<UIDesignData | null>(null);
  private uiDesignResponseSubject = new BehaviorSubject<UIDesignResponseData | null>(null);
  private showOverviewTabSubject = new BehaviorSubject<boolean>(false);

  // Public observables
  public cardTitle$ = this.cardTitleSubject.asObservable();
  public promptData$ = this.promptDataSubject.asObservable();
  public applicationTarget$ = this.applicationTargetSubject.asObservable();
  public uiDesignData$ = this.uiDesignDataSubject.asObservable();
  public uiDesignResponse$ = this.uiDesignResponseSubject.asObservable();
  public showOverviewTab$ = this.showOverviewTabSubject.asObservable();

  constructor(
    private http: HttpClient,
    private userSignatureService: UserSignatureService,
    private apiRetryService: ApiRetryService
  ) {}

  /**
   * Set the selected card title
   * @param title The card title
   */
  setCardTitle(title: string): void {
    this.cardTitleSubject.next(title);
    this.logger.info('Card title set:', title);
  }

  /**
   * Get the current card title
   * @returns Current card title
   */
  getCardTitle(): string {
    return this.cardTitleSubject.getValue();
  }

  /**
   * Set the user prompt data
   * @param prompt The user prompt
   */
  setPromptData(prompt: string): void {
    this.promptDataSubject.next(prompt);
    this.logger.info('Prompt data set:', prompt);
  }

  /**
   * Get the current prompt data
   * @returns Current prompt data
   */
  getPromptData(): string {
    return this.promptDataSubject.getValue();
  }

  /**
   * Set the application target (mobile or web)
   * @param target The application target
   */
  setApplicationTarget(target: 'mobile' | 'web'): void {
    this.applicationTargetSubject.next(target);
    this.logger.info('Application target set:', target);
  }

  /**
   * Get the current application target
   * @returns Current application target
   */
  getApplicationTarget(): 'mobile' | 'web' {
    return this.applicationTargetSubject.getValue();
  }

  /**
   * Set complete UI design data
   * @param data Complete UI design data
   */
  setUIDesignData(data: UIDesignData): void {
    this.uiDesignDataSubject.next(data);
    this.logger.info('UI Design data set:', data);
  }

  /**
   * Get the current UI design data
   * @returns Current UI design data
   */
  getUIDesignData(): UIDesignData | null {
    return this.uiDesignDataSubject.getValue();
  }

  /**
   * Store API response to prevent duplicate calls
   * @param response The API response to store
   */
  // storeAPIResponse(response: GenerateUIDesignResponse): void {
  //   const currentData = this.getUIDesignData();
  //   if (currentData) {
  //     const updatedData: UIDesignData = {
  //       ...currentData,
  //       apiResponse: response
  //     };
  //     this.setUIDesignData(updatedData);
  //     this.logger.info('API response stored to prevent duplicate calls:', response);
  //   }
  // }

  // /**
  //  * Check if API response already exists (to prevent duplicate calls)
  //  * @returns True if API response exists, false otherwise
  //  */
  // hasAPIResponse(): boolean {
  //   const currentData = this.getUIDesignData();
  //   return !!(currentData?.apiResponse);
  // }

  // /**
  //  * Get stored API response
  //  * @returns Stored API response or null
  //  */
  // getAPIResponse(): GenerateUIDesignResponse | null {
  //   const currentData = this.getUIDesignData();
  //   return currentData?.apiResponse || null;
  // }

  /**
   * Reset all stored data
   */
  resetData(): void {
    this.cardTitleSubject.next('');
    this.promptDataSubject.next('');
    this.applicationTargetSubject.next('mobile'); // Reset to mobile default
    this.uiDesignDataSubject.next(null);
    this.uiDesignResponseSubject.next(null);
    this.showOverviewTabSubject.next(false);
    this.logger.info('All UI Design data reset');
  }

  /**
   * Set UI Design response data (pages from API)
   * @param responseData The response data with pages
   */
  setUIDesignResponse(responseData: UIDesignResponseData): void {
    this.uiDesignResponseSubject.next(responseData);
    this.showOverviewTabSubject.next(true);
    this.logger.info('UI Design response set:', responseData);
  }

  /**
   * Get the current UI Design response data
   * @returns Current UI Design response data
   */
  getUIDesignResponse(): UIDesignResponseData | null {
    return this.uiDesignResponseSubject.getValue();
  }

  /**
   * Show/hide overview tab
   * @param show Whether to show the overview tab
   */
  setShowOverviewTab(show: boolean): void {
    this.showOverviewTabSubject.next(show);
    this.logger.info('Overview tab visibility set:', show);
  }

  /**
   * Get current overview tab visibility
   * @returns Current overview tab visibility
   */
  getShowOverviewTab(): boolean {
    return this.showOverviewTabSubject.getValue();
  }

  /**
   * Generate UI Design via API
   * 🚨 CRITICAL: NO TIMEOUT - wireframe-generation/generate can take longer than 4 minutes
   * Removed all client-side timeouts to prevent 499 errors
   * @param request The UI design generation request
   * @param userSignature Optional user signature
   * @returns Observable with the generation response
   */
  generateUIDesign(request: GenerateUIDesignRequest, userSignature?: string): Observable<GenerateUIDesignResponse> {
    // If userSignature is not provided, use the UserSignatureService to get it
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();

    // Add user_signature to query params
    const params = new HttpParams().set('user_signature', signature);

    // Disable caching for generation requests
    const context = cacheHelpers.disableCache();

    this.logger.info('🚀 Generating UI Design with NO CLIENT-SIDE TIMEOUT to prevent 499 errors');
    this.logger.info('API endpoint:', this.apiUrl + this.endpoint);
    this.logger.info('Request payload (JSON body):', JSON.stringify(request));
    this.logger.info('Query params:', params.toString());

    // Create the HTTP request observable
    const httpRequest = this.http.post<GenerateUIDesignResponse>(this.apiUrl + this.endpoint, request, {
      params,
      context,
      headers: {
        'Content-Type': 'application/json'
        // Removed 'Connection' and 'Keep-Alive' headers as they are forbidden by browsers
        // Browser automatically manages connection keep-alive for optimal performance
      }
    })
      .pipe(
        // 🛡️ CRITICAL: NO TIMEOUT to prevent 499 client errors
        // Let the server determine when to timeout, not the client
        // timeout() operator removed completely to prevent client-side timeouts
        map((response: any) => {
          this.logger.info('✅ UI Design generation response received:', response);

          // Handle different response formats
          if (typeof response === 'string') {
            try {
              return JSON.parse(response);
            } catch (e) {
              this.logger.error('Failed to parse response string:', e);
              return response;
            }
          } else if (response && typeof response === 'object') {
            return response;
          }

          return response;
        }),
        catchError(error => {
          this.logger.error('❌ Error generating UI Design:', error);

          // Enhanced 499 error handling
          if (error.status === 499) {
            this.logger.error('🚫 499 Client Closed Request - connection was closed before completion');
            this.logger.error('This typically happens when the request takes longer than browser/network timeout');

            // Create a more descriptive error for 499
            const enhancedError = new Error('Request was cancelled due to timeout. The operation may still be processing on the server.');
            (enhancedError as any).status = 499;
            (enhancedError as any).originalError = error;
            throw enhancedError;
          }

          // Check if it's a network/connection error
          if (error.status === 0) {
            this.logger.error('🌐 Network error - connection failed or was interrupted');
            const networkError = new Error('Network connection failed. Please check your internet connection and try again.');
            (networkError as any).status = 0;
            (networkError as any).originalError = error;
            throw networkError;
          }

          throw error;
        })
      );

    // Apply retry logic for 499 errors and network failures
    return this.apiRetryService.withRetry(httpRequest, this.apiRetryService.getWireframeRetryConfig());
  }

  /**
   * Call the intro API to get contextual messaging for UI Design generation
   * This runs in parallel with the main generation API
   *
   * @param userRequest - The user's prompt/request
   * @param codeItems - Optional array of code items (for regeneration)
   * @returns Observable<string> - The intro text response
   */
  generateIntroMessage(userRequest: string, codeItems: IntroCodeItem[] = []): Observable<string> {
    this.logger.info('🎭 Calling intro API for contextual messaging');
    this.logger.info('🎭 User request:', userRequest);
    this.logger.info('🎭 Code items count:', codeItems.length);

    // Validate input parameters
    if (!userRequest || userRequest.trim().length === 0) {
      this.logger.warn('🎭 Empty user request provided, using fallback');
      return of('Preparing your UI design...');
    }

    // Build and validate the request
    const request: IntroAPIRequest = {
      code: codeItems,
      user_request: userRequest.trim()
    };

    // Log the request payload for debugging
    this.logger.info('🎭 Intro API request payload:', {
      user_request: request.user_request,
      code_items_count: request.code.length,
      code_items_preview: request.code.slice(0, 2).map(item => ({
        fileName: item.fileName,
        contentLength: item.content.length
      }))
    });

    // Disable caching for intro requests
    const context = cacheHelpers.disableCache();

    this.logger.info('🎭 Intro API endpoint:', this.apiUrl + this.introEndpoint);
    this.logger.info('🎭 Intro request payload:', JSON.stringify(request));

    return this.http.post<IntroAPIResponseType>(this.apiUrl + this.introEndpoint, request, {
      context,
      headers: {
        'Content-Type': 'application/json'
      }
    }).pipe(
      map(response => {
        this.logger.info('🎭 Intro API response received:', response);

        // Handle both string and object response formats
        let introText: string = '';

        if (typeof response === 'string') {
          // Direct string response (new format)
          introText = response.trim();
          this.logger.info('🎭 Received direct string response from intro API');
        } else if (response && typeof response === 'object') {
          // Object response (legacy format)
          introText = response.message || response.text || response.content || '';
          this.logger.info('🎭 Received object response from intro API, extracted text from fields');
        } else {
          this.logger.warn('🎭 Unexpected response format from intro API:', typeof response);
        }

        // Validate extracted text
        if (!introText || introText.trim().length === 0) {
          this.logger.warn('🎭 Intro API response contains no text content');
          return 'Preparing your UI design...';
        }

        // Clean and validate the intro text
        const cleanedText = this.sanitizeIntroText(introText);
        this.logger.info('🎭 Successfully extracted and cleaned intro text:', {
          originalLength: introText.length,
          cleanedLength: cleanedText.length,
          preview: cleanedText.substring(0, 100) + (cleanedText.length > 100 ? '...' : '')
        });

        return cleanedText;
      }),
      catchError(error => {
        this.logger.error('🎭 Intro API call failed:', error);

        // Log detailed error information for debugging
        this.logger.error('🎭 Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          url: error.url
        });

        // Return fallback message instead of throwing error
        // This ensures the main generation flow continues even if intro fails
        const fallbackMessage = this.getFallbackIntroMessage(error);
        this.logger.info('🎭 Using fallback intro message:', fallbackMessage);

        return of(fallbackMessage);
      }),
      timeout(30000), // 30 second timeout for intro API
      catchError(timeoutError => {
        this.logger.error('🎭 Intro API timeout after 30 seconds:', timeoutError);
        return of('Preparing your UI design...');
      })
    );
  }

  /**
   * Sanitize and clean intro text from API response
   * @param text - Raw intro text from API
   * @returns Cleaned and validated intro text
   */
  private sanitizeIntroText(text: string): string {
    if (!text || typeof text !== 'string') {
      return 'Preparing your UI design...';
    }

    // Remove any potential HTML tags or unwanted characters
    let cleanedText = text
      .trim()
      // Remove HTML tags if any
      .replace(/<[^>]*>/g, '')
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove any control characters
      .replace(/[\x00-\x1F\x7F]/g, '')
      // Trim again after cleaning
      .trim();

    // Validate length (reasonable limits for intro text)
    if (cleanedText.length === 0) {
      this.logger.warn('🎭 Intro text is empty after sanitization');
      return 'Preparing your UI design...';
    }

    if (cleanedText.length > 500) {
      this.logger.warn('🎭 Intro text is very long, truncating:', cleanedText.length);
      cleanedText = cleanedText.substring(0, 497) + '...';
    }

    // Ensure the text ends with proper punctuation for better UX
    if (cleanedText.length > 0 && !/[.!?]$/.test(cleanedText)) {
      cleanedText += '.';
    }

    return cleanedText;
  }

  /**
   * Get appropriate fallback intro message based on error type
   * @param error - The error that occurred
   * @returns Appropriate fallback message
   */
  private getFallbackIntroMessage(error: any): string {
    // Provide different fallback messages based on error type
    if (error.status === 0) {
      // Network error
      return 'Preparing your UI design...';
    } else if (error.status >= 500) {
      // Server error
      return 'Getting ready to create your design...';
    } else if (error.status === 404) {
      // Not found
      return 'Setting up your design generation...';
    } else if (error.status >= 400 && error.status < 500) {
      // Client error
      return 'Initializing design creation...';
    } else {
      // Unknown error
      return 'Preparing your UI design...';
    }
  }

  /**
   * Build the API request from current state
   * @returns The API request object
   */
  buildAPIRequest(): GenerateUIDesignRequest {
    // const cardTitle = this.getCardTitle();
    const prompt = this.getPromptData();
    // const applicationTarget = this.getApplicationTarget();

    // Map card title to proper title format for API
    // const apiTitle = cardTitle === 'Generate UI Design' ? 'UI' : cardTitle;

    // For wireframe-generation/generate API - only prompt is needed
    return {
      prompt: prompt
      // Commented out for wireframe-generation/generate API
      // input_prompt: prompt,
      // title: apiTitle,
      // application_target: applicationTarget
    };
  }

  /**
   * Store and generate UI design in one call
   * @param cardTitle The card title
   * @param prompt The user prompt
   * @param applicationTarget The application target (mobile/web)
   * @param userSignature Optional user signature
   * @returns Observable with the generation response
   */
  storeAndGenerate(
    cardTitle: string,
    prompt: string,
    applicationTarget: 'mobile' | 'web' = 'mobile', // Default to mobile
    userSignature?: string
  ): Observable<GenerateUIDesignResponse> {
    // Store the data
    this.setCardTitle(cardTitle);
    this.setPromptData(prompt);
    this.setApplicationTarget(applicationTarget);

    // Set complete UI design data
    const uiDesignData: UIDesignData = {
      cardTitle,
      prompt,
      applicationTarget,
      timestamp: new Date().toISOString()
    };
    this.setUIDesignData(uiDesignData);

    // Build and send the request
    const request = this.buildAPIRequest();
    this.logger.info('Generated API request payload:', request);
    return this.generateUIDesign(request, userSignature);
  }

  /**
   * Create a dummy payload for testing/demonstration purposes
   * @param prompt Optional custom prompt
   * @param applicationTarget Optional application target
   * @returns Dummy API request payload
   */
  createDummyPayload(
    prompt: string = 'Create a modern dashboard interface with clean design and intuitive navigation'
    // applicationTarget: 'mobile' | 'web' = 'mobile' // Commented out for wireframe-generation/generate API
  ): GenerateUIDesignRequest {
    return {
      prompt: prompt
      // Commented out for wireframe-generation/generate API
      // input_prompt: prompt,
      // title: 'UI',
      // application_target: applicationTarget
    };
  }

  /**
   * Get example payloads for different scenarios
   * @returns Array of example payloads
   */
  getExamplePayloads(): { description: string; payload: GenerateUIDesignRequest }[] {
    return [
      {
        description: 'Web Dashboard UI Design',
        payload: {
          prompt: 'Create a modern analytics dashboard with data visualization charts, user metrics, and a clean sidebar navigation'
          // Commented out for wireframe-generation/generate API
          // input_prompt: 'Create a modern analytics dashboard with data visualization charts, user metrics, and a clean sidebar navigation',
          // title: 'UI',
          // application_target: 'web'
        }
      },
      {
        description: 'Mobile App UI Design',
        payload: {
          prompt: 'Design a mobile e-commerce app interface with product listings, search functionality, and shopping cart'
          // Commented out for wireframe-generation/generate API
          // input_prompt: 'Design a mobile e-commerce app interface with product listings, search functionality, and shopping cart',
          // title: 'UI',
          // application_target: 'mobile'
        }
      },
      {
        description: 'Web Landing Page UI Design',
        payload: {
          prompt: 'Create a SaaS product landing page with hero section, feature highlights, pricing table, and call-to-action buttons'
          // Commented out for wireframe-generation/generate API
          // input_prompt: 'Create a SaaS product landing page with hero section, feature highlights, pricing table, and call-to-action buttons',
          // title: 'UI',
          // application_target: 'web'
        }
      },
      {
        description: 'Mobile Social Media UI Design',
        payload: {
          prompt: 'Design a social media app interface with news feed, user profiles, messaging, and photo sharing capabilities'
          // Commented out for wireframe-generation/generate API
          // input_prompt: 'Design a social media app interface with news feed, user profiles, messaging, and photo sharing capabilities',
          // title: 'UI',
          // application_target: 'mobile'
        }
      }
    ];
  }

  /**
   * Log the current state and API payload for debugging
   */
  debugCurrentState(): void {
    const currentData = this.getUIDesignData();
    const apiPayload = this.buildAPIRequest();

    this.logger.info('=== UI Design Service Debug Info ===');
    this.logger.info('Current UI Design Data:', currentData);
    this.logger.info('Generated API Payload:', apiPayload);
    this.logger.info('Card Title:', this.getCardTitle());
    this.logger.info('Prompt Data:', this.getPromptData());
    this.logger.info('Application Target:', this.getApplicationTarget());
    this.logger.info('=====================================');
  }

  /**
   * Test method to simulate intro API response handling
   * @param mockResponse - Mock response to test (string or object)
   * @returns Processed intro text
   */
  public testIntroResponseHandling(mockResponse: IntroAPIResponseType): string {
    this.logger.info('🧪 Testing intro response handling with mock data:', mockResponse);

    let introText: string = '';

    if (typeof mockResponse === 'string') {
      // Direct string response (new format)
      introText = mockResponse.trim();
      this.logger.info('🧪 Processed as string response');
    } else if (mockResponse && typeof mockResponse === 'object') {
      // Object response (legacy format)
      introText = mockResponse.message || mockResponse.text || mockResponse.content || '';
      this.logger.info('🧪 Processed as object response');
    }

    const cleanedText = this.sanitizeIntroText(introText);
    this.logger.info('🧪 Test result:', {
      input: mockResponse,
      extracted: introText,
      cleaned: cleanedText
    });

    return cleanedText;
  }
}
