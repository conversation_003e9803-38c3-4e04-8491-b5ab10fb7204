import { DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

/**
 * Modern Angular 19 subscription management utility using DestroyRef
 * Provides consistent subscription cleanup patterns across the application
 */
export class SubscriptionManager {
  private destroyRef = inject(DestroyRef);

  /**
   * Subscribe to an observable with automatic cleanup using DestroyRef
   * @param observable$ The observable to subscribe to
   * @param next The next callback function
   * @param error Optional error callback function
   * @param complete Optional complete callback function
   */
  subscribe<T>(
    observable$: Observable<T>,
    next: (value: T) => void,
    error?: (error: any) => void,
    complete?: () => void
  ): void {
    observable$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({ next, error, complete });
  }

  /**
   * Get the takeUntilDestroyed operator for manual pipe usage
   * @returns The takeUntilDestroyed operator configured with this instance's DestroyRef
   */
  takeUntilDestroyed() {
    return takeUntilDestroyed(this.destroyRef);
  }
}

/**
 * Legacy subscription management utility for components that need backward compatibility
 * Uses the traditional destroy$ Subject pattern
 */
export class LegacySubscriptionManager {
  private destroy$ = new Subject<void>();

  /**
   * Subscribe to an observable with automatic cleanup using destroy$ subject
   * @param observable$ The observable to subscribe to
   * @param next The next callback function
   * @param error Optional error callback function
   * @param complete Optional complete callback function
   */
  subscribe<T>(
    observable$: Observable<T>,
    next: (value: T) => void,
    error?: (error: any) => void,
    complete?: () => void
  ): void {
    observable$
      .pipe(takeUntil(this.destroy$))
      .subscribe({ next, error, complete });
  }

  /**
   * Get the takeUntil operator for manual pipe usage
   * @returns The takeUntil operator configured with this instance's destroy$ subject
   */
  takeUntil() {
    return takeUntil(this.destroy$);
  }

  /**
   * Manually trigger cleanup - call this in ngOnDestroy
   */
  destroy(): void {             
    this.destroy$.next();
    this.destroy$.complete();
  }
}

/**
 * Service-level subscription management utility
 * For services that need to manage subscriptions across their lifecycle
 */
export class ServiceSubscriptionManager {
  private destroy$ = new Subject<void>();
  private isDestroyed = false;

  /**
   * Subscribe to an observable with automatic cleanup
   * @param observable$ The observable to subscribe to
   * @param next The next callback function
   * @param error Optional error callback function
   * @param complete Optional complete callback function
   */
  subscribe<T>(
    observable$: Observable<T>,
    next: (value: T) => void,
    error?: (error: any) => void,
    complete?: () => void
  ): void {
    if (this.isDestroyed) {
      console.warn('Attempting to subscribe after ServiceSubscriptionManager has been destroyed');
      return;
    }

    observable$
      .pipe(takeUntil(this.destroy$))
      .subscribe({ next, error, complete });
  }

  /**
   * Get the takeUntil operator for manual pipe usage
   * @returns The takeUntil operator configured with this instance's destroy$ subject
   */
  takeUntil() {
    return takeUntil(this.destroy$);
  }

  /**
   * Manually trigger cleanup - call this when the service needs to clean up
   */
  destroy(): void {
    if (!this.isDestroyed) {
      this.destroy$.next();
      this.destroy$.complete();
      this.isDestroyed = true;
    }
  }

  /**
   * Check if the manager has been destroyed
   */
  get destroyed(): boolean {
    return this.isDestroyed;
  }
}

/**
 * Directive subscription management utility
 * Specifically designed for Angular directives
 */
export class DirectiveSubscriptionManager {
  private destroyRef = inject(DestroyRef);

  /**
   * Subscribe to an observable with automatic cleanup using DestroyRef
   * @param observable$ The observable to subscribe to
   * @param next The next callback function
   * @param error Optional error callback function
   * @param complete Optional complete callback function
   */
  subscribe<T>(
    observable$: Observable<T>,
    next: (value: T) => void,
    error?: (error: any) => void,
    complete?: () => void
  ): void {
    observable$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({ next, error, complete });
  }

  /**
   * Get the takeUntilDestroyed operator for manual pipe usage
   * @returns The takeUntilDestroyed operator configured with this instance's DestroyRef
   */
  takeUntilDestroyed() {
    return takeUntilDestroyed(this.destroyRef);
  }
}

/**
 * Observer cleanup utility for managing DOM observers
 */
export class ObserverManager {
  private observers: Array<{
    observer: MutationObserver | IntersectionObserver | ResizeObserver;
    type: string;
  }> = [];

  /**
   * Register a MutationObserver for automatic cleanup
   * @param target The target element to observe
   * @param callback The callback function
   * @param options The observer options
   * @returns The created MutationObserver
   */
  createMutationObserver(
    target: Node,
    callback: MutationCallback,
    options?: MutationObserverInit
  ): MutationObserver {
    const observer = new MutationObserver(callback);
    observer.observe(target, options);
    this.observers.push({ observer, type: 'mutation' });
    return observer;
  }

  /**
   * Register an IntersectionObserver for automatic cleanup
   * @param callback The callback function
   * @param options The observer options
   * @returns The created IntersectionObserver
   */
  createIntersectionObserver(
    callback: IntersectionObserverCallback,
    options?: IntersectionObserverInit
  ): IntersectionObserver {
    const observer = new IntersectionObserver(callback, options);
    this.observers.push({ observer, type: 'intersection' });
    return observer;
  }

  /**
   * Clean up all registered observers
   */
  cleanup(): void {
    this.observers.forEach(({ observer }) => {
      observer.disconnect();
    });
    this.observers = [];
  }
}
