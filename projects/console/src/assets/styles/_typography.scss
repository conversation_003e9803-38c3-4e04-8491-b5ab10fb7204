/* Typography Styles */

/* Heading styles */
h1, .h1 {
  font-family: var(--font-family);
  font-weight: var(--font-weight-bold);
  font-size: 2.25rem; // 36px
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}

h2, .h2 {
  font-family: var(--font-family);
  font-weight: var(--font-weight-semibold);
  font-size: 1.75rem; // 28px
  line-height: 1.25;
  margin-bottom: 1.25rem;
  color: var(--text-color);
}

h3, .h3 {
  font-family: var(--font-family);
  font-weight: var(--font-weight-semibold);
  font-size: 1.5rem; // 24px
  line-height: 1.3;
  margin-bottom: 1rem;
  color: var(--text-color);
}

h4, .h4 {
  font-family: var(--font-family);
  font-weight: var(--font-weight-medium);
  font-size: 1.25rem; // 20px
  line-height: 1.35;
  margin-bottom: 0.75rem;
  color: var(--text-color);
}

h5, .h5 {
  font-family: var(--font-family);
  font-weight: var(--font-weight-medium);
  font-size: 1.125rem; // 18px
  line-height: 1.4;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

h6, .h6 {
  font-family: var(--font-family);
  font-weight: var(--font-weight-medium);
  font-size: 1rem; // 16px
  line-height: 1.5;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

/* Body text styles */
p, .body-text {
  font-family: var(--font-family);
  font-weight: var(--font-weight-regular);
  font-size: 1rem; // 16px
  line-height: 1.5;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.body-text-small {
  font-family: var(--font-family);
  font-weight: var(--font-weight-regular);
  font-size: 0.875rem; // 14px
  line-height: 1.5;
  color: var(--text-color);
}

.body-text-extra-small {
  font-family: var(--font-family);
  font-weight: var(--font-weight-regular);
  font-size: 0.75rem; // 12px
  line-height: 1.5;
  color: var(--text-color);
}

/* Text utilities */
.text-light {
  font-weight: var(--font-weight-light);
}

.text-regular {
  font-weight: var(--font-weight-regular);
}

.text-medium {
  font-weight: var(--font-weight-medium);
}

.text-semibold {
  font-weight: var(--font-weight-semibold);
}

.text-bold {
  font-weight: var(--font-weight-bold);
}

.text-extrabold {
  font-weight: var(--font-weight-extrabold);
}

/* Special text styles */
.caption {
  font-size: 0.75rem;
  line-height: 1.4;
  color: var(--text-secondary);
  font-weight: var(--font-weight-regular);
}

.overline {
  font-size: 0.75rem;
  line-height: 1.4;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.label {
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  line-height: 1.4;
  color: var(--text-color);
}

/* Text colors */
.text-primary {
  color: var(--primary-start);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.text-info {
  color: var(--info-color);
}

/* Responsive typography */
@media (max-width: 768px) {
  h1, .h1 {
    font-size: 2rem; // 32px
  }
  
  h2, .h2 {
    font-size: 1.5rem; // 24px
  }
  
  h3, .h3 {
    font-size: 1.25rem; // 20px
  }
  
  h4, .h4 {
    font-size: 1.125rem; // 18px
  }
  
  h5, .h5 {
    font-size: 1rem; // 16px
  }
}

@media (max-width: 576px) {
  h1, .h1 {
    font-size: 1.75rem; // 28px
  }
  
  h2, .h2 {
    font-size: 1.375rem; // 22px
  }
}

/* Code font - exception to Mulish */
.code, code, pre {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
} 