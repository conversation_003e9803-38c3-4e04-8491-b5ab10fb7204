import { Directive, ElementRef, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TypewriterService } from '../services/typewriter.service';
import { DirectiveSubscriptionManager } from '../utils/subscription-management.util';

@Directive({
  selector: '[appTypewriterPlaceholder]',
  standalone: true
})
export class TypewriterPlaceholderDirective implements OnInit, OnDestroy {
  @Input() texts: string[] = [];
  @Input() staticText: string = 'Ask Studio to create';
  @Input() typingSpeed: number = 40;
  @Input() erasingSpeed: number = 30;
  @Input() pauseBeforeErasing: number = 400;
  @Input() pauseBeforeTyping: number = 200;

  private subscriptionManager = new DirectiveSubscriptionManager();

  constructor(
    private el: ElementRef<HTMLTextAreaElement>,
    private typewriterService: TypewriterService
  ) {}

  ngOnInit(): void {
    if (this.texts.length === 0) {
      return;
    }

    this.typewriterService.startTypewriter(
      this.texts,
      this.staticText,
      this.typingSpeed,
      this.erasingSpeed,
      this.pauseBeforeErasing,
      this.pauseBeforeTyping
    );

    setTimeout(() => {
      const textarea = this.el.nativeElement.querySelector('textarea');
      if (textarea) {
        this.subscriptionManager.subscribe(
          this.typewriterService.placeholder$,
          placeholder => {
            textarea.setAttribute('placeholder', placeholder);
          }
        );
      }
    }, 0);
  }

  ngOnDestroy(): void {
    this.typewriterService.stopTypewriter();
  }
}
