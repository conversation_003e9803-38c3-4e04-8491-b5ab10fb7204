.card-container {
  background: var(--card-bg);
  border-radius: 12px;
  border: 1px solid var(--card-border);
  box-shadow: 0 4px 16px var(--card-shadow);
  overflow: hidden;
  transition: all 0.3s ease;
  height: auto;
  padding: 20px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  color: var(--text-color);
  position: relative;
  
  /* Glass effect enhancement */
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: -1;
    background: var(--card-bg);
    opacity: 0.9;
    border-radius: inherit;
  }
  
  &.clickable {
    cursor: pointer;
    
    &:hover:not(.no-hover) {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px var(--card-hover-shadow);
      border-color: var(--card-hover-border);
    }
    
    &:active:not(.no-hover) {
      transform: translateY(0);
      transition: all 0.1s ease;
    }
  }
  
  &.fixed-height {
    height: 190px !important; /* Force fixed height with !important */
    min-height: 190px !important; /* Force minimum height with !important */
    max-height: 190px !important; /* Force maximum height with !important */
    display: flex;
    flex-direction: column;
  }
  
  &.no-padding {
    padding: 0;
  }
  
  &:hover:not(.no-hover):not(.clickable) {
    box-shadow: 0 6px 20px var(--card-hover-shadow);
  }
}

/* Card content area */
:host ::ng-deep {
  .card-header {
    margin-bottom: 16px;
    
    h1, h2, h3, h4, h5, h6 {
      color: var(--text-color);
      margin: 0;
    }
  }
  
  .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .card-footer {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-container {
    padding: 16px;
    
    &.fixed-height {
      height: 190px; /* Keep same height on medium screens */
      min-height: 190px;
      max-height: 190px;
    }
  }
}

@media (max-width: 576px) {
  .card-container {
    padding: 12px;
    
    &.fixed-height {
      height: 190px; /* Keep same height on small screens */
      min-height: 190px;
      max-height: 190px;
    }
  }
} 