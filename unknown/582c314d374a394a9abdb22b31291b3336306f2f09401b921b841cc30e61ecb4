name: Azure Static Web Apps CI/CD

trigger:
  branches:
    include:
      - main
  paths:
    include:
      - "projects/console/*"
      - "package.json"
      - "package-lock.json"
      - "angular.json"
      - "tsconfig.json"

variables:
  - group: elder-wand-variables # Variable group containing shared variables
  - name: npm_config_cache
    value: $(Pipeline.Workspace)/.npm

jobs:
  - job: build_console
    displayName: "Build Console"
    pool:
      vmImage: "ubuntu-latest"
    steps:
      - template: templates/node-build-steps.yml

      - script: |
          # Replace environment variables in the production environment file
          sed -i "s|\$(CONSOLE_URL)|$(CONSOLE_URL)|g" projects/console/src/environments/environment.prod.ts
          sed -i "s|\$(API_URL)|$(API_URL)|g" projects/console/src/environments/environment.prod.ts

          # Build with environment variables
          CONSOLE_URL="$(CONSOLE_URL)" npm run build:console
          echo "Checking build output directory:"
          ls -la dist
          echo "Checking console build directory:"
          ls -la dist/console
        displayName: "Build Console"
        env:
          NODE_ENV: production
          CONSOLE_URL: $(CONSOLE_URL)
          API_URL: $(API_URL)
          NODE_AUTH_TOKEN: $(Azure_DevOPS_PAT)

      - task: CopyFiles@2
        inputs:
          sourceFolder: 'dist/console'
          contents: '**/*'
          targetFolder: '$(Build.ArtifactStagingDirectory)/console'
        displayName: 'Copy Build Artifacts'

      - task: PublishBuildArtifacts@1
        inputs:
          pathToPublish: '$(Build.ArtifactStagingDirectory)/console'
          artifactName: 'console'
        displayName: 'Publish Build Artifacts'

  - job: deploy_console
    displayName: "Deploy Console"
    dependsOn: build_console
    condition: succeeded()
    pool:
      vmImage: "ubuntu-latest"
    steps:
      - checkout: self
        displayName: 'Checkout Repository'

      - download: current
        artifact: 'console'
        displayName: 'Download Build Artifacts'

      - script: |
          echo "Current directory:"
          pwd
          echo "Pipeline workspace:"
          echo $(Pipeline.Workspace)
          echo "Checking downloaded artifacts:"
          ls -la $(Pipeline.Workspace)/console
          echo "Checking working directory:"
          ls -la $(Pipeline.Workspace)/s
          echo "Checking root directory:"
          ls -la /
        displayName: 'Verify Artifacts'

      - script: |
          # Create deployment directory
          mkdir -p $(Pipeline.Workspace)/deploy
          # Copy artifacts to deployment directory
          cp -r $(Pipeline.Workspace)/console/* $(Pipeline.Workspace)/deploy/
          echo "Checking deployment directory:"
          $(Pipeline.Workspace)/deploy
          ls -la $(Pipeline.Workspace)/deploy
        displayName: 'Prepare Deployment Directory'

      - task: AzureStaticWebApp@0
        inputs:
          azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_CONSOLE)
          app_location: "/"
          cwd: "$(Pipeline.Workspace)/deploy"
          output_location: ""
          skip_app_build: true
          api_location: ""
          deployment_source: "local"
        displayName: "Deploy Console to Azure Static Web Apps"

