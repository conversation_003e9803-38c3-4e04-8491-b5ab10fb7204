<div class="agents-container">
  <div class="agents-header">
    <div class="title-wrapper">
      <awe-icons iconName="stars" color="#F96CAB"></awe-icons>
      <h1>Agents</h1>
    </div>
  </div>

  <app-filter-tabs *ngIf="!showExploreButton" [tabs]="filterTabs" [activeTab]="activeFilter"
    (tabChange)="onFilterChange($event)">
  </app-filter-tabs>

  <div class="agents-grid row">
    <div class="col-12 col-md-6 col-lg-3" *ngFor="let agent of agents; let i = index">
      <div class="agent-card" (click)="showAgentDetails(agent)" (mouseenter)="onArrowHover(i, agent.studio.type)" (mouseleave)="onArrowLeave(i)"
        [style.border-color]="agentHoverState[i] ? agentHoverState[i].borderColor : 'transparent'">
        <div class="card-content">
          <div class="card-header">
            <h2>{{ agent.title }}</h2>
            <div class="rating">
              <awe-icons iconName="agent_star" color="#FFD700"></awe-icons>
              {{ agent.rating }}
            </div>
          </div>

          <p class="description">{{ agent.description | truncate: 75 }}</p>

          <div class="card-footer">
            <div class="users">
              <awe-icons iconName="awe_profile" iconColor="disable"></awe-icons>
              {{ agent.users }}
            </div>
            <div class="studio" [style.background-color]="agent.studio.backgroundColor"
              [style.color]="agent.studio.textColor">
              {{ agent.studio.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="explore-more" *ngIf="showExploreButton">
    <awe-button label="Explore More" variant="primary" class="w-100" width="366px"
      gradient="linear-gradient(45deg,#6566CD,#F96CAB)" (click)="navigate()"></awe-button>
  </div>

  <!-- Overlay and Agent Details Panel -->
  <div class="agent-details-overlay" *ngIf="selectedAgent" (click)="closeAgentDetails($event)">
    <div class="agent-details-panel" (click)="$event.stopPropagation()">
      <div class="details-header">
        <button class="close-btn" (click)="closeAgentDetails($event)">
          <span>×</span>
        </button>
      </div>

      <div class="details-content">
        <div class="upper-content">
          <div>
            <div class="details-title">
              <!-- <span class="sparkle">✨</span> -->
              <h2>{{ selectedAgent.title }}</h2>
            </div>

            <p class="details-description">
              Effortlessly convert Ruby code to Spring Boot with optimised
              migration
            </p>

            <div class="details-metrics">
              <div class="metric">
                <span class="label-1">Category</span>
                <div>
                  <img src="/svgs/code.svg " alt="code" />
                </div>
                <span class="label">Code</span>
              </div>

              <div class="metric">
                <span class="label-1">Developed by</span>
                <img src="/svgs/user-logo.svg " alt="user-logo" />
                <div class="developer">
                  <span class="label">{{ selectedAgent.studio.name }}</span>
                </div>
              </div>

              <div class="metric">
                <span class="label-1">Relevancy</span>
                <div class="score">9.5/10</div>
                <span class="label">Score</span>
              </div>

              <div class="metric">
                <span class="label-1">Agent</span>
                <div class="rating">
                  <span class="score">4.5</span>
                  <img class="star1" src="/svgs/kid_star.svg " alt="Kid Star" />
                </div>
                <span class="label">Rating</span>
              </div>
            </div>
          </div>

          <div>
            <div class="details-section">
              <h3>What it's for</h3>
              <p>
                A agent that converts Ruby code to Spring Boot can be highly
                beneficial for organizations migrating from Ruby on Rails to
                Java Spring Boot. However, the effectiveness depends on several
                factors, including the complexity of the application, language
                differences, and the capabilities of the conversion agent.
              </p>
            </div>

            <div class="details-section-2">
              <h3>Type</h3>
              <div class="type-badge">
                <img src="/svgs/icon.svg " alt="icon" />
                <span class="code-mig">Code Migration</span>
              </div>
            </div>
          </div>
          <div class="action-button">
            <button class="go-to-playground-btn" (click)="goToPlayground()">
              Go to Playground
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
