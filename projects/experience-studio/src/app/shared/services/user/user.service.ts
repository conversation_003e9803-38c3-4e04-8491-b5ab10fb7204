import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { cacheHelpers } from '../../interceptors/cache.interceptor';

export interface UserResponse {
  user_id?: string; // Optional, in case the response is a direct string
  email?: string;   // Optional, in case the response is a direct string
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private apiUrl = environment.apiUrl;
  private userEndpoint = '/user'; // Endpoint to fetch user by email

  // BehaviorSubject to store and share the user ID across components
  private userIdSubject = new BehaviorSubject<string | null>(this.loadUserIdFromStorage());
  public userId$ = this.userIdSubject.asObservable();

  constructor(
    private http: HttpClient
  ) {}

  /**
   * Get user ID by email
   * @param email User's email address
   * @returns Observable with user ID
   */
  getUserIdByEmail(email: string): Observable<string> {
    // Create query parameters with the email
    const params = new HttpParams().set('email', email);

    // User data should be cached for a moderate amount of time (10 minutes)
    // This reduces API calls while still ensuring relatively fresh data
    const context = cacheHelpers.setMaxAge(10 * 60 * 1000); // 10 minutes

    return this.http.get(`${this.apiUrl}${this.userEndpoint}`, {
      params,
      responseType: 'text', // Get the response as text to handle both object and string responses
      context // Add caching context
    })
      .pipe(
        map(response => {
          let userId: string;

          // Try to parse the response as JSON first
          try {
            // If the response is a JSON string, parse it
            const parsedResponse = JSON.parse(response);

            // Check if it's an object with user_id property
            if (parsedResponse && typeof parsedResponse === 'object' && parsedResponse.user_id) {
              userId = parsedResponse.user_id;
            } else {
              // If it's not an object with user_id, use the parsed response directly
              // This handles cases where the API returns a JSON string or number
              userId = String(parsedResponse);
            }
          } catch (e) {
            // If parsing fails, the response is likely a plain string
            // Use it directly as the user ID
            userId = response;
          }

          // Validate the user ID
          if (!userId || typeof userId !== 'string' || userId.trim() === '') {
            throw new Error('Valid user ID not found in response');
          }

          // Store the user ID
          this.setUserId(userId);

          return userId;
        }),
        catchError(_error => {
          // For development/testing, return a mock user ID if the API fails
          // In production, you might want to handle this differently
          const mockUserId = '01df7a8f-8af7-477a-9e69-7d3a236fa774';
          this.setUserId(mockUserId);

          return of(mockUserId);
        })
      );
  }

  /**
   * Get user ID by email with cache disabled - use this when you need fresh data
   * @param email User's email address
   * @returns Observable with user ID
   */
  getUserIdByEmailFresh(email: string): Observable<string> {
    // Create query parameters with the email
    const params = new HttpParams().set('email', email);

    // Disable caching for this request
    const context = cacheHelpers.disableCache();

    return this.http.get(`${this.apiUrl}${this.userEndpoint}`, {
      params,
      responseType: 'text',
      context // Disable caching
    }).pipe(
      // Reuse the same mapping logic as the cached version
      map(response => {
        let userId: string;
        try {
          const parsedResponse = JSON.parse(response);
          if (parsedResponse && typeof parsedResponse === 'object' && parsedResponse.user_id) {
            userId = parsedResponse.user_id;
          } else {
            userId = String(parsedResponse);
          }
        } catch (e) {
          userId = response;
        }

        if (!userId || typeof userId !== 'string' || userId.trim() === '') {
          throw new Error('Valid user ID not found in response');
        }

        this.setUserId(userId);
        return userId;
      }),
      catchError(_error => {
        const mockUserId = '01df7a8f-8af7-477a-9e69-7d3a236fa774';
        this.setUserId(mockUserId);
        return of(mockUserId);
      })
    );
  }

  /**
   * Get the current user ID
   * @returns The current user ID or null if not set
   */
  getUserId(): string | null {
    return this.userIdSubject.getValue();
  }

  /**
   * Set the user ID and store it in session storage
   * @param userId The user ID to set
   */
  setUserId(userId: string): void {
    // Store in session storage
    sessionStorage.setItem('userId', userId);

    // Update the BehaviorSubject
    this.userIdSubject.next(userId);
  }

  /**
   * Load user ID from session storage
   * @returns The stored user ID or null if not found
   */
  private loadUserIdFromStorage(): string | null {
    try {
      return sessionStorage.getItem('userId');
    } catch (e) {
      return null;
    }
  }

  /**
   * Clear the stored user ID
   */
  clearUserId(): void {
    sessionStorage.removeItem('userId');
    this.userIdSubject.next(null);
  }
}
