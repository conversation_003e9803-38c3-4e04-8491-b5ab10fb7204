import { Injectable } from '@angular/core';
import { Observable, of, delay, switchMap } from 'rxjs';
import { NewPollingResponseProcessorService } from './new-polling-response-processor.service';
import {
  MOCK_OVERVIEW_IN_PROGRESS,
  MOCK_OVERVIEW_COMPLETED,
  MOCK_PROJECT_OVERVIEW_FAILED,
  MOCK_LAYOUT_ANALYZED_IN_PROGRESS,
  MOCK_LAYOUT_ANALYZED_COMPLETED,
  MOCK_LAYOUT_ANALYZED_FAILED,
  MOCK_DESIGN_SYSTEM_IN_PROGRESS,
  MOCK_DESIGN_SYSTEM_COMPLETED,
  MOCK_DESIGN_SYSTEM_FAILED,
  MOCK_DEPLOYED_IN_PROGRESS,
  MOCK_DEPLOYED_COMPLETED,
  MOCK_DEPLOYED_FAILED
} from '../data/mock-polling-responses';
import { NewPollingResponse } from '../models/polling-response.interface';
import { createLogger } from '../utils/logger';

/**
 * Test service to demonstrate and validate the new polling response processing
 * This service simulates different polling scenarios using mock data
 */
@Injectable({
  providedIn: 'root'
})
export class PollingTestService {
  private logger = createLogger('PollingTestService');

  constructor(private responseProcessor: NewPollingResponseProcessorService) {}

  /**
   * Test the complete flow from project overview to deployment
   */
  testCompleteFlow(): Observable<string> {
    return this.simulatePollingSequence([
      MOCK_OVERVIEW_IN_PROGRESS,
      MOCK_OVERVIEW_COMPLETED,
      MOCK_LAYOUT_ANALYZED_IN_PROGRESS,
      MOCK_LAYOUT_ANALYZED_COMPLETED,
      MOCK_DESIGN_SYSTEM_IN_PROGRESS,
      MOCK_DESIGN_SYSTEM_COMPLETED,
      MOCK_DEPLOYED_IN_PROGRESS,
      MOCK_DEPLOYED_COMPLETED
    ]);
  }

  /**
   * Test failure scenarios
   */
  testFailureScenarios(): Observable<string> {
    return this.simulatePollingSequence([
      MOCK_OVERVIEW_IN_PROGRESS,
      MOCK_PROJECT_OVERVIEW_FAILED
    ]);
  }

  /**
   * Test layout analysis failure
   */
  testLayoutAnalysisFailure(): Observable<string> {
    return this.simulatePollingSequence([
      MOCK_OVERVIEW_IN_PROGRESS,
      MOCK_OVERVIEW_COMPLETED,
      MOCK_LAYOUT_ANALYZED_IN_PROGRESS,
      MOCK_LAYOUT_ANALYZED_FAILED
    ]);
  }

  /**
   * Test design system analysis failure
   */
  testDesignSystemFailure(): Observable<string> {
    return this.simulatePollingSequence([
      MOCK_OVERVIEW_IN_PROGRESS,
      MOCK_OVERVIEW_COMPLETED,
      MOCK_LAYOUT_ANALYZED_IN_PROGRESS,
      MOCK_LAYOUT_ANALYZED_COMPLETED,
      MOCK_DESIGN_SYSTEM_IN_PROGRESS,
      MOCK_DESIGN_SYSTEM_FAILED
    ]);
  }

  /**
   * Test deployment failure
   */
  testDeploymentFailure(): Observable<string> {
    return this.simulatePollingSequence([
      MOCK_OVERVIEW_IN_PROGRESS,
      MOCK_OVERVIEW_COMPLETED,
      MOCK_LAYOUT_ANALYZED_IN_PROGRESS,
      MOCK_LAYOUT_ANALYZED_COMPLETED,
      MOCK_DESIGN_SYSTEM_IN_PROGRESS,
      MOCK_DESIGN_SYSTEM_COMPLETED,
      MOCK_DEPLOYED_IN_PROGRESS,
      MOCK_DEPLOYED_FAILED
    ]);
  }

  /**
   * Test individual progress state
   */
  testProgressState(response: NewPollingResponse): void {
    this.logger.info('Testing progress state:', response.progress);
    this.responseProcessor.processResponse(response);
  }

  /**
   * Simulate a polling sequence with delays between responses
   */
  private simulatePollingSequence(responses: NewPollingResponse[]): Observable<string> {
    let currentIndex = 0;

    const processNext = (): Observable<string> => {
      if (currentIndex >= responses.length) {
        return of('Polling sequence completed');
      }

      const response = responses[currentIndex];
      this.logger.info(`Processing response ${currentIndex + 1}/${responses.length}:`, response.progress);

      // Process the response
      this.responseProcessor.processResponse(response);

      currentIndex++;

      // Continue with next response after delay
      return of(`Processed: ${response.progress} - ${response.status}`).pipe(
        delay(2000), // 2 second delay between responses
        switchMap(() => processNext())
      );
    };

    return processNext();
  }

  /**
   * Get current processor state for debugging
   */
  getCurrentState(): any {
    return {
      progress: this.responseProcessor.getCurrentProgress(),
      status: this.responseProcessor.getCurrentStatus(),
      isPreviewEnabled: this.responseProcessor.isPreviewEnabled()
    };
  }

  /**
   * Reset the processor state
   */
  resetState(): void {
    this.responseProcessor.reset();
    this.logger.info('Processor state reset');
  }

  /**
   * Test specific UI scenarios
   */
  testUIScenarios(): void {
    this.logger.info('Testing UI scenarios...');

    // Test project overview with README artifact
    this.logger.info('1. Testing project overview with README artifact');
    this.testProgressState(MOCK_OVERVIEW_COMPLETED);

    setTimeout(() => {
      // Test layout analysis with layout code
      this.logger.info('2. Testing layout analysis with layout code');
      this.testProgressState(MOCK_LAYOUT_ANALYZED_COMPLETED);
    }, 3000);

    setTimeout(() => {
      // Test design system with tokens
      this.logger.info('3. Testing design system with tokens');
      this.testProgressState(MOCK_DESIGN_SYSTEM_COMPLETED);
    }, 6000);

    setTimeout(() => {
      // Test deployment with preview URL
      this.logger.info('4. Testing deployment with preview URL');
      this.testProgressState(MOCK_DEPLOYED_COMPLETED);
    }, 9000);
  }
}
