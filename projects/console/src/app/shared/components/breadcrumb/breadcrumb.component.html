<nav
  class="breadcrumb-container"
  *ngIf="(breadcrumbs$ | async)?.length"
  aria-label="Breadcrumbs"
>
  <ul class="breadcrumb-list">
    <li
      *ngFor="
        let breadcrumb of breadcrumbs$ | async;
        let last = last;
        let i = index
      "
      class="breadcrumb-item"
      [class.active]="breadcrumb.active"
    >
      <a
        *ngIf="!breadcrumb.active && breadcrumb.url"
        [routerLink]="breadcrumb.url"
        class="breadcrumb-link"
      >
        {{ breadcrumb.label }}
      </a>
      <span
        *ngIf="breadcrumb.active || !breadcrumb.url"
        class="breadcrumb-label"
        [attr.aria-current]="breadcrumb.active ? 'page' : null"
      >
        {{ breadcrumb.label }}
      </span>
      <span *ngIf="!last" class="separator" aria-hidden="true">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9 18L15 12L9 6"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </span>
    </li>
  </ul>
</nav>
