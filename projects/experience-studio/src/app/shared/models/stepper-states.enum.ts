/**
 * Enum representing the possible states of the stepper component.
 * Each state has a corresponding display title in present tense.
 */
export enum StepperState {
  OVERVIEW = 'OVERVIEW',
  SEED_PROJECT_INITIALIZED = 'SEED_PROJECT_INITIALIZED',
  FILE_QUEUE = 'FILE_QUEUE',
  DESIGN_SYSTEM_MAPPED = 'DESIGN_SYSTEM_MAPPED',
  COMPONENTS_CREATED = 'COMPONENTS_CREATED',
  LAYOUT_ANALYZED = 'LAYOUT_ANALYZED',
  PAGES_GENERATED = 'PAGES_GENERATED',
  BUILD_STARTED = 'BUILD_STARTED',
  BUILD_SUCCEEDED = 'BUILD_SUCCEEDED',
  BUILD_FAILED = 'BUILD_FAILED',
  BUILD = 'BUILD',
  FILES_GENERATED = 'FILES_GENERATED',
  DEPLOY = 'DEPLOY',
  DEPLOYED = 'DEPLOYED',
}

/**
 * Maps stepper states to their display titles in present tense.
 */
export const StepperStateDisplayTitles: Record<StepperState, string> = {
  [StepperState.OVERVIEW]: 'Project Overview',
  [StepperState.SEED_PROJECT_INITIALIZED]: 'Seed Project',
  [StepperState.FILE_QUEUE]: 'Components Identified',
  [StepperState.DESIGN_SYSTEM_MAPPED]: 'Design System',
  [StepperState.COMPONENTS_CREATED]: 'Components Created',
  [StepperState.LAYOUT_ANALYZED]: 'Layout Identified',
  [StepperState.PAGES_GENERATED]: 'Pages Created',
  [StepperState.BUILD_STARTED]: 'Build Started',
  [StepperState.BUILD_SUCCEEDED]: 'Build Completed',
  [StepperState.BUILD_FAILED]: 'Build Failed',
  [StepperState.BUILD]: 'Building Application',
  [StepperState.FILES_GENERATED]: 'Files Generated',
  [StepperState.DEPLOY]: 'Deploying Application',
  [StepperState.DEPLOYED]: 'Deployed',
};
