.search-container {
  display: flex;
  width: 1049px;
  padding: 24px 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  /* Enhanced glass morphism effect */
  background: linear-gradient(109.35deg, rgba(255, 255, 255, 0.15) 3.98%, rgba(255, 255, 255, 0.25) 95.92%);
  border: 1px solid rgba(255, 255, 255, 0.18);
  backdrop-filter: blur(70px);
  -webkit-backdrop-filter: blur(70px);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
  box-sizing: border-box;
  margin: 0 auto;
}

.search-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}



.search-input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border-radius: 16px;
  background-color: #fff;
  color: #666D99;
  font-family: Mulish;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  outline: none;
  border: double 1px transparent;
  background-image: linear-gradient(white, white),
    linear-gradient(90.78deg, rgba(101, 102, 205, 0.5) 8.06%, rgba(227, 10, 109, 0.5) 73.25%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
}

.animated-placeholder-wrapper {
  position: absolute;
  left: 48px;
  /* Align with input's padding-left */
  top: 50%;
  transform: translateY(-50%);
  overflow: hidden;
  /* **Crucial:** Hides the parts of spans not currently visible */
  height: 24px;
  /* **Crucial:** This must be the exact height of one line of text */
  display: flex;
  align-items: center;
  pointer-events: none;
  /* Allows clicks to pass through to the input */
  width: calc(100% - 48px - 16px);
  /* Adjust width to fit within the input, considering padding */
}

.animated-placeholder {
  display: flex;
  flex-direction: column;
  /* Stacks spans vertically */
  height: 100%;
  /* Ensures this container takes the full height of the wrapper */
  animation: slide-text-loop 9s cubic-bezier(0.7, 0, 0.3, 1) infinite;
  /* 3 phrases * 3 seconds each with smoother timing */
  line-height: 1.2;
  /* Adjust based on your font size for proper vertical spacing */
  /* We'll use this to control the vertical position of the group of spans */
}

.animated-placeholder span {
  color: #666D99;
  font-family: 'Mulish', -apple-system, 'Roboto', 'Helvetica', sans-serif;
  font-size: 20px;
  font-weight: 400;
  white-space: nowrap;
  /* Prevent text from wrapping */
  /* Each span needs to be the exact height of the wrapper to facilitate precise sliding */
  height: 100%;
  display: flex;
  align-items: center;
  /* Vertically align text within its own 'slot' */
  flex-shrink: 0;
  /* Prevent spans from shrinking */
  flex-grow: 0;
  /* Prevent spans from growing */
}

@keyframes slide-text-loop {

  0%,
  30% {
    transform: translateY(0%);
  }

  33.33%,
  63.33% {
    transform: translateY(-100%);
  }

  66.66%,
  96.66% {
    transform: translateY(-200%);
  }

  100% {
    transform: translateY(-300%);
    /* Move up by 3 span heights to transition back to the first item */
  }
}

/* Keep your existing media queries */
@media (max-width: 1200px) {
  .search-container {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .search-wrapper {
    max-width: 100%;
  }

  .search-container {
    height: auto;
  }

  .search-input {
    font-size: 18px;
  }

  .animated-placeholder span {
    font-size: 18px;
  }

  .animated-placeholder-wrapper {
    height: 22px;
    /* Adjust height for smaller screens, ensure it matches font size */
  }
}

awe-icons svg {
  margin-top: 10px;
}