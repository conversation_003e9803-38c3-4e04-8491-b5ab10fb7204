export interface ModelDefinition {
  provider: string;
  name: string;
  version: string;
  description?: string;
  modelType?: 'Generative' | 'Embedding';
  contextSize?: number;
  maxTokens?: number;
  parameters?: {
    [key: string]: any;
  };
}

/**
 * Mock model definitions for the model selection UI
 */
export const MODEL_DEFINITIONS: ModelDefinition[] = [
  // Google Models
  {
    provider: 'Google',
    name: 'Gemini',
    version: 'v1.0',
    description: 'Gemini 1.0 is Google\'s first multimodal AI model capable of understanding text, images, and code.',
    modelType: 'Generative',
    contextSize: 8192,
    maxTokens: 2048
  },
  {
    provider: 'Google',
    name: '<PERSON>',
    version: 'v2.0',
    description: 'Gemini 2.0 offers improved reasoning, multimodal capabilities, and higher token limits.',
    modelType: 'Generative',
    contextSize: 32768,
    maxTokens: 8192
  },
  {
    provider: 'Google',
    name: 'Gemini',
    version: 'v3.0',
    description: 'Gemini 3.0 is Google\'s most advanced generative model with state-of-the-art performance across tasks.',
    modelType: 'Generative',
    contextSize: 131072,
    maxTokens: 32768
  },
  {
    provider: 'Google',
    name: '<PERSON><PERSON>',
    version: 'v2',
    description: 'PaLM 2 is optimized for reasoning tasks and long context understanding.',
    modelType: 'Generative',
    contextSize: 16384,
    maxTokens: 4096
  },
  
  // Anthropic Models
  {
    provider: 'Anthropic',
    name: 'Claude',
    version: 'v1.0',
    description: 'Claude 1.0 is Anthropic\'s first generation AI assistant, focused on helpfulness and harmlessness.',
    modelType: 'Generative',
    contextSize: 8192,
    maxTokens: 4096
  },
  {
    provider: 'Anthropic',
    name: 'Claude',
    version: 'v2.0',
    description: 'Claude 2.0 offers improved reasoning capabilities and higher token limits than its predecessor.',
    modelType: 'Generative',
    contextSize: 100000,
    maxTokens: 12000
  },
  {
    provider: 'Anthropic',
    name: 'Claude',
    version: 'v3.0 Opus',
    description: 'Claude 3 Opus is Anthropic\'s most powerful model with exceptional reasoning and instruction following.',
    modelType: 'Generative',
    contextSize: 200000,
    maxTokens: 30000
  },
  {
    provider: 'Anthropic',
    name: 'Claude',
    version: 'v3.0 Sonnet',
    description: 'Claude 3 Sonnet balances high performance with efficiency for general purpose use.',
    modelType: 'Generative',
    contextSize: 150000,
    maxTokens: 25000
  },
  
  // Azure Models
  {
    provider: 'Azure',
    name: 'GPT-4',
    version: 'v1.0',
    description: 'OpenAI\'s GPT-4 available through Azure, optimized for enterprise use and compliance.',
    modelType: 'Generative',
    contextSize: 8192,
    maxTokens: 4096
  },
  {
    provider: 'Azure',
    name: 'GPT-4',
    version: 'Turbo',
    description: 'A faster version of GPT-4 on Azure with slightly reduced capabilities but higher throughput.',
    modelType: 'Generative',
    contextSize: 8192,
    maxTokens: 4096
  },
  {
    provider: 'Azure',
    name: 'GPT-3.5',
    version: 'Turbo',
    description: 'A cost-effective model balancing performance and efficiency for general tasks.',
    modelType: 'Generative',
    contextSize: 4096,
    maxTokens: 2048
  },
  {
    provider: 'Azure',
    name: 'DALL-E',
    version: 'v3',
    description: 'Image generation model available through Azure OpenAI Service.',
    modelType: 'Generative',
  },
  
  // Amazon Models
  {
    provider: 'Amazon',
    name: 'Titan',
    version: 'Text',
    description: 'Amazon\'s foundation text model for general purpose text generation and understanding.',
    modelType: 'Generative',
    contextSize: 8192,
    maxTokens: 4096
  },
  {
    provider: 'Amazon',
    name: 'Titan',
    version: 'Embeddings',
    description: 'Specialized model for generating text embeddings for similarity search and retrieval.',
    modelType: 'Embedding',
    contextSize: 8192,
  },
  {
    provider: 'Amazon',
    name: 'Claude',
    version: 'v3.0 on Bedrock',
    description: 'Anthropic\'s Claude available through Amazon Bedrock with AWS integration.',
    modelType: 'Generative',
    contextSize: 200000,
    maxTokens: 30000
  },
  {
    provider: 'Amazon',
    name: 'Stable Diffusion',
    version: 'XL on Bedrock',
    description: 'Advanced image generation model available through Amazon Bedrock.',
    modelType: 'Generative',
  }
];

/**
 * Helper function to get models by provider
 */
export function getModelsByProvider(provider: string): ModelDefinition[] {
  return MODEL_DEFINITIONS.filter(model => model.provider === provider);
}

/**
 * Helper function to get model by provider and version
 */
export function getModel(provider: string, name: string, version: string): ModelDefinition | undefined {
  return MODEL_DEFINITIONS.find(
    model => model.provider === provider && model.name === name && model.version === version
  );
} 