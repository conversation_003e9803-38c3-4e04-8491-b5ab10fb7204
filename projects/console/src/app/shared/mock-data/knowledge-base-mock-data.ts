import { CardData } from '../models/card.model';

export const MOCK_KNOWLEDGE_BASE: CardData[] = [
  {
    id: '1',
    title: 'Customer Support FAQ Database',
    tags: [
      { label: 'Support' },
      { label: 'FAQs' },
      { label: 'Customer Service' }
    ],
    createdDate: '2/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'business',
    client: 'services',
    department: 'customer-support',
    role: 'support-specialist',
    project: 'customer-experience',
    category: 'documentation'
  },
  {
    id: '2',
    title: 'Product Documentation Library',
    tags: [
      { label: 'Products' },
      { label: 'Documentation' },
      { label: 'Technical Specs' }
    ],
    createdDate: '1/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'manufacturing',
    department: 'product-management',
    role: 'technical-writer',
    project: 'product-documentation',
    category: 'documentation'
  },
  {
    id: '3',
    title: 'Healthcare Regulations Compendium',
    tags: [
      { label: 'Healthcare' },
      { label: 'Regulations' },
      { label: 'Compliance' }
    ],
    createdDate: '3/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'healthcare',
    department: 'compliance',
    role: 'compliance-officer',
    project: 'regulatory-adherence',
    category: 'regulations'
  },
  {
    id: '4',
    title: 'Software Development Best Practices',
    tags: [
      { label: 'Development' },
      { label: 'Best Practices' },
      { label: 'Coding Standards' }
    ],
    createdDate: '2/18/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'technology',
    department: 'engineering',
    role: 'software-developer',
    project: 'code-quality',
    category: 'best-practices'
  },
  {
    id: '5',
    title: 'Market Research Repository',
    tags: [
      { label: 'Marketing' },
      { label: 'Research' },
      { label: 'Market Analysis' }
    ],
    createdDate: '1/25/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'business',
    client: 'marketing',
    department: 'market-research',
    role: 'marketing-analyst',
    project: 'market-intelligence',
    category: 'research'
  },
  {
    id: '6',
    title: 'Employee Training Resources',
    tags: [
      { label: 'HR' },
      { label: 'Training' },
      { label: 'Professional Development' }
    ],
    createdDate: '2/28/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'business',
    client: 'hr',
    department: 'training',
    role: 'training-specialist',
    project: 'employee-development',
    category: 'training'
  },
  {
    id: '7',
    title: 'Legal Precedents Database',
    tags: [
      { label: 'Legal' },
      { label: 'Precedents' },
      { label: 'Case Studies' }
    ],
    createdDate: '3/12/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'legal',
    department: 'legal-affairs',
    role: 'legal-counsel',
    project: 'legal-research',
    category: 'case-law'
  },
  {
    id: '8',
    title: 'Financial Regulations Handbook',
    tags: [
      { label: 'Finance' },
      { label: 'Regulations' },
      { label: 'Compliance' }
    ],
    createdDate: '1/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'financial',
    department: 'compliance',
    role: 'compliance-officer',
    project: 'regulatory-compliance',
    category: 'regulations'
  },
  {
    id: '9',
    title: 'Supply Chain Best Practices',
    tags: [
      { label: 'Supply Chain' },
      { label: 'Logistics' },
      { label: 'Operations' }
    ],
    createdDate: '2/14/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'manufacturing',
    department: 'operations',
    role: 'supply-chain-manager',
    project: 'supply-chain-optimization',
    category: 'best-practices'
  },
  {
    id: '10',
    title: 'Project Management Guidelines',
    tags: [
      { label: 'Project Management' },
      { label: 'Guidelines' },
      { label: 'Methodologies' }
    ],
    createdDate: '3/10/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'business',
    client: 'technology',
    department: 'project-management',
    role: 'project-manager',
    project: 'methodology-standardization',
    category: 'guidelines'
  },
  {
    id: '11',
    title: 'Customer Experience Research',
    tags: [
      { label: 'CX' },
      { label: 'Research' },
      { label: 'Customer Insights' }
    ],
    createdDate: '1/30/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'business',
    client: 'services',
    department: 'customer-experience',
    role: 'cx-analyst',
    project: 'customer-journey-mapping',
    category: 'research'
  },
  {
    id: '12',
    title: 'Educational Curriculum Library',
    tags: [
      { label: 'Education' },
      { label: 'Curriculum' },
      { label: 'Learning Resources' }
    ],
    createdDate: '2/5/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'education',
    client: 'educational-institutions',
    department: 'curriculum-development',
    role: 'educator',
    project: 'educational-content',
    category: 'education'
  },
  {
    id: '13',
    title: 'Industry Standards Repository',
    tags: [
      { label: 'Standards' },
      { label: 'Industry Specifications' },
      { label: 'Quality Assurance' }
    ],
    createdDate: '3/18/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'manufacturing',
    department: 'quality-assurance',
    role: 'quality-manager',
    project: 'standards-compliance',
    category: 'standards'
  },
  {
    id: '14',
    title: 'Cybersecurity Threat Intelligence',
    tags: [
      { label: 'Security' },
      { label: 'Cyber Threats' },
      { label: 'Risk Management' }
    ],
    createdDate: '1/12/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'technology',
    department: 'information-security',
    role: 'security-analyst',
    project: 'threat-intelligence',
    category: 'security'
  },
  {
    id: '15',
    title: 'Brand Guidelines and Assets',
    tags: [
      { label: 'Brand' },
      { label: 'Design' },
      { label: 'Marketing Assets' }
    ],
    createdDate: '2/20/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'business',
    client: 'marketing',
    department: 'brand-management',
    role: 'brand-manager',
    project: 'brand-consistency',
    category: 'design'
  },
  {
    id: '16',
    title: 'Scientific Research Database',
    tags: [
      { label: 'Research' },
      { label: 'Scientific' },
      { label: 'R&D' }
    ],
    createdDate: '3/2/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'pharmaceutical',
    department: 'research-development',
    role: 'researcher',
    project: 'scientific-discovery',
    category: 'research'
  },
  {
    id: '17',
    title: 'Sales Training Manual',
    tags: [
      { label: 'Sales' },
      { label: 'Training' },
      { label: 'Business Development' }
    ],
    createdDate: '1/22/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'business',
    client: 'retail',
    department: 'sales',
    role: 'sales-manager',
    project: 'sales-enablement',
    category: 'training'
  },
  {
    id: '18',
    title: 'Engineering Design Patterns',
    tags: [
      { label: 'Engineering' },
      { label: 'Design Patterns' },
      { label: 'Development' }
    ],
    createdDate: '2/27/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'technology',
    department: 'engineering',
    role: 'software-architect',
    project: 'architectural-standards',
    category: 'patterns'
  },
  {
    id: '19',
    title: 'Change Management Toolkit',
    tags: [
      { label: 'Change Management' },
      { label: 'Organizational Change' },
      { label: 'Transformation' }
    ],
    createdDate: '3/15/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'business',
    client: 'consulting',
    department: 'organizational-development',
    role: 'change-manager',
    project: 'business-transformation',
    category: 'management'
  },
  {
    id: '20',
    title: 'Data Governance Framework',
    tags: [
      { label: 'Data Governance' },
      { label: 'Data Management' },
      { label: 'Compliance' }
    ],
    createdDate: '1/8/2025',
    actions: [
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
     
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ],
    // Filter properties
    userType: 'enterprise',
    client: 'financial',
    department: 'data-management',
    role: 'data-governance-officer',
    project: 'regulatory-compliance',
    category: 'governance'
  }
]; 