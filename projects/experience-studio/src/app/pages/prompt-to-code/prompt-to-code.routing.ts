import { Routes } from '@angular/router';
import { PromptSubmissionGuard } from '../../shared/guards/prompt-submission.guard';

export const PROMPT_TO_CODE_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./prompt-to-code.component').then(m => m.PromptToCodeComponent),
    children: [
      {
        path: 'prompt',
        loadComponent: () =>
          import('../image-to-code/components/prompt-content/prompt-content.component').then(
            m => m.PromptContentComponent
          ),
        outlet: 'primary',
      },
      {
        path: 'code-preview',
        loadComponent: () =>
          import('../../shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
        canActivate: [PromptSubmissionGuard],
        outlet: 'primary',
      },
      { path: '', redirectTo: 'prompt', pathMatch: 'full' },
    ],
  },
];
