import { Component, OnInit, HostListener, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '@awe/play-comp-library';
import { NavItemComponent } from '../nav-item/nav-item.component';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { ThemeService } from '../../services/theme/theme.service';
import { ThemeToggleComponent } from '../theme-toggle/theme-toggle.component';

interface DropdownItem {
  label: string;
  description: string;
  route: string;
  icon: string;
}

interface NavItem {
  label: string;
  route: string;
  selected: boolean;
  hasDropdown: boolean;
  dropdownOpen?: boolean;
  icon: string;
  dropdownItems?: DropdownItem[];
}

@Component({
  selector: 'app-nav-header',
  standalone: true,
  imports: [HeaderComponent, CommonModule, NavItemComponent, ThemeToggleComponent],
  templateUrl: './nav-header.component.html',
  styleUrl: './nav-header.component.scss',
})
export class NavHeaderComponent implements OnInit {
  logoSrc: string = '';
  themeMenuIcon: string = '';
  userAvatar: string = '';
  currentTheme: 'light' | 'dark' = 'light';

  // Navigation menu items
  navItems: NavItem[] = [
    { 
      label: 'Dashboard', 
      route: '/dashboard', 
      selected: true,
      hasDropdown: false,
      icon: `svgs/icons/awe_dashboard.svg`
    },
    { 
      label: 'Launch', 
      route: '/launch', 
      selected: false, 
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_launch.svg`,
      dropdownItems: [
        { 
          label: 'Agents', 
          description: 'Create, Manage and Edit Agents',
          route: '/launch/agents',
          icon: `svgs/icons/awe_agents.svg`
        },
        { 
          label: 'Workflows', 
          description: 'Create, Manage and Edit Workflows',
          route: '/launch/workflows',
          icon: `svgs/icons/awe_workflows.svg`
        }
      ]
    },
    { 
      label: 'Libraries', 
      route: '/libraries', 
      selected: false, 
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_libraries.svg`,
      dropdownItems: [
        { 
          label: 'Prompts', 
          description: 'Create, Manage and Edit Prompts', 
          route: '/libraries/prompts',
          icon: `svgs/icons/awe_prompts.svg`
        },
        { 
          label: 'Models', 
          description: 'Add, Manage and Edit Models', 
          route: '/libraries/models',
          icon: `svgs/icons/awe_models.svg`
        },
        { 
          label: 'Knowledge-base', 
          description: 'Add, Manage and Edit Knowledge-Base', 
          route: '/libraries/knowledge-base',
          icon: `svgs/icons/awe_knowledgebase.svg`
        },
        { 
          label: 'Tools', 
          description: 'Add, Manage and Edit Tools', 
          route: '/libraries/tools',
          icon: `svgs/icons/awe_tools.svg`
        },
        { 
          label: 'Guardrails', 
          description: 'Add, Manage and Edit Guardrails', 
          route: '/libraries/guardrails',
          icon: `svgs/icons/awe_guardrails.svg`
        }
      ]
    },
    { 
      label: 'Manage', 
      route: '/manage', 
      selected: false, 
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_manage.svg`,
      dropdownItems: [
        { 
          label: 'Management 1', 
          description: 'Management description', 
          route: '/manage1',
          icon: `svgs/icons/awe_userlogs.svg`
        },
        { 
          label: 'Management 2', 
          description: 'Management description', 
          route: '/manage2',
          icon: `svgs/icons/awe_userlogs.svg`
        }
      ]
    },
    { 
      label: 'Analytics', 
      route: '/analytics', 
      selected: false, 
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_analytics.svg`,
      dropdownItems: [
        { 
          label: 'Analytics 1', 
          description: 'Analytics description', 
          route: '/analytics1',
          icon: `svgs/icons/awe_analytics.svg`
        },
        { 
          label: 'Analytics 2', 
          description: 'Analytics description', 
          route: '/analytics2',
          icon: `svgs/icons/awe_analytics.svg`
        }
      ]
    }
  ];
 
  constructor(
    private elementRef: ElementRef, 
    private router: Router,
    private themeService: ThemeService
  ) {
    // Subscribe to router events to update the active menu item
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: any) => {
        this.updateActiveMenuItemByRoute(event.url);
      });
  }

  ngOnInit(): void {
    // Get the initial theme
    this.currentTheme = this.themeService.getCurrentTheme();
    
    // Update assets based on theme
    this.updateThemeAssets();
    
    // Subscribe to theme changes
    this.themeService.themeObservable.subscribe(theme => {
      this.currentTheme = theme;
      this.updateThemeAssets();
    });
    
    // Initialize the active menu item based on the current route
    this.updateActiveMenuItemByRoute(this.router.url);
  }

  // Update assets based on the current theme
  private updateThemeAssets(): void {
    // Update theme-specific assets
    this.logoSrc = `svgs/ascendion-logo/ascendion-logo-${this.currentTheme}.svg`;   
    this.themeMenuIcon = `svgs/header/menu-${this.currentTheme}.svg`;
    this.userAvatar = `svgs/header/user-avatar.svg`;
  }

  // Update the active menu item based on the current route
  updateActiveMenuItemByRoute(url: string): void {
    // Reset all selections
    this.navItems.forEach(item => {
      item.selected = false;
    });

    // Find the matching parent route or parent of a child route
    const parentItem = this.navItems.find(item => {
      // Check if this is a direct match for the parent route
      if (url === item.route) {
        return true;
      }
      
      // Check if this is a dropdown parent with a matching child
      if (item.hasDropdown && item.dropdownItems) {
        // Check if the URL starts with the parent route path (for nested routes)
        // OR if any child route exactly matches the URL
        return url.startsWith(item.route + '/') || 
               item.dropdownItems.some(child => url === child.route);
      }
      
      return false;
    });

    if (parentItem) {
      parentItem.selected = true;
      console.log(`Selected nav item: ${parentItem.label}`);
    } else {
      // Default to Dashboard if no match found
      const dashboardItem = this.navItems.find(item => item.route === '/dashboard');
      if (dashboardItem) {
        dashboardItem.selected = true;
        console.log('No matching route found, defaulting to Dashboard');
      }
    }
  }

  // Listen for clicks on the document to close dropdowns when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Check if click target is inside a dropdown menu
    const target = event.target as HTMLElement;
    const clickedInsideDropdown = !!target.closest('.dropdown-menu') || 
                                  !!target.closest('.dropdown-item');
                                  
    // If clicked inside dropdown, don't close menus
    if (clickedInsideDropdown) {
      console.log('Clicked inside dropdown, keeping open');
      return;
    }
    
    // Check if the click was outside the nav header
    const clickedOutsideNavHeader = !this.elementRef.nativeElement.contains(event.target);
    if (clickedOutsideNavHeader) {
      console.log('Clicked outside header, closing all dropdowns');
      this.closeAllDropdowns();
    }
  }

  // Toggle dropdown menu
  toggleDropdown(index: number): void {
    console.log(`Toggling dropdown for index: ${index}`);
    
    // Close all other dropdowns
    this.navItems.forEach((navItem, i) => {
      if (i !== index && navItem.dropdownOpen) {
        navItem.dropdownOpen = false;
      }
    });
    
    // Toggle the current dropdown
    this.navItems[index].dropdownOpen = !this.navItems[index].dropdownOpen;
  }

  // Close all dropdowns
  closeAllDropdowns(): void {
    this.navItems.forEach(navItem => {
      if (navItem.dropdownOpen) {
        navItem.dropdownOpen = false;
      }
    });
  }

  // Select a menu item
  selectMenuItem(index: number): void {
    this.navItems.forEach((navItem, i) => {
      navItem.selected = i === index;
    });
  }

  // Navigate to route
  navigateTo(route: string): void {
    console.log(`Navigating to ${route}`);
    this.router.navigate([route]);
  }

  // Handle dropdown item selection
  onDropdownItemSelected(event: {route: string, label: string}, parentIndex: number): void {
    console.log(`Dropdown item selected: ${event.label}, navigating to ${event.route}`);
    this.selectMenuItem(parentIndex);
    this.navigateTo(event.route);
    
    // Close all dropdowns after selection
    this.closeAllDropdowns();
  }
}
 
 