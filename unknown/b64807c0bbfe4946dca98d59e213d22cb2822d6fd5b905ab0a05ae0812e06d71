import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface ActivityLog {
  timestamp: string;
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error';
}

export interface ExecutionDetails {
  agentName: string;
  executionId: string;
  startTime: string;
  endTime?: string;
  status: 'running' | 'completed' | 'failed' | 'canceled';
  steps?: string[];
}

@Component({
  selector: 'app-agent-activity',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './agent-activity.component.html',
  styleUrls: ['./agent-activity.component.scss']
})
export class AgentActivityComponent {
  @Input() activityLogs: ActivityLog[] = [];
  @Input() executionDetails?: ExecutionDetails;
  @Input() progress: number = 0;
  @Input() isRunning: boolean = false;
  
  @Output() saveLogs = new EventEmitter<void>();
  @Output() controlAction = new EventEmitter<'play' | 'pause' | 'stop'>();
  
  showDetails: boolean = true;
  
  constructor() {}
  
  toggleDetails(): void {
    this.showDetails = !this.showDetails;
  }
  
  onSaveLogs(): void {
    this.saveLogs.emit();
  }
  
  onControlAction(action: 'play' | 'pause' | 'stop'): void {
    this.controlAction.emit(action);
  }
  
  get statusClass(): string {
    if (!this.executionDetails) return '';
    
    switch (this.executionDetails.status) {
      case 'running': return 'status-running';
      case 'completed': return 'status-completed';
      case 'failed': return 'status-failed';
      case 'canceled': return 'status-canceled';
      default: return '';
    }
  }
}