.workflow-execution-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
  Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  
  
  // Header styling
  .execution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color, #e0e0e0);
    
    .execution-title {
      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--text-color, #333);
      }
      
      .header-buttons {
        display: flex;
        gap: 12px;
      }
    }
    
    .execution-actions {
      display: flex;
      gap: 12px;
      
      .back-button, .edit-button {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 5px;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        
        svg {
          margin-right: 6px;
        }
      }
      
      .back-button {
        background-color: var(--bg-muted, #f5f5f5);
        border: 1px solid var(--border-color, #e0e0e0);
        color: var(--text-color, #333);
        
        &:hover {
          background-color: var(--bg-muted-hover, #e9e9e9);
        }
      }
      
      .edit-button {
        background-color: var(--card-bg, #fff);
        border: 1px solid var(--border-color, #e0e0e0);
        color: var(--text-color, #333);
        
        &:hover {
          background-color: var(--card-bg-hover, #f9f9f9);
          border-color: var(--border-color-dark, #d0d0d0);
        }
      }
    }
  }
  
  // Main content with 3 columns
  .execution-content {
    display: flex;
    flex-grow: 1;
    height: calc(100vh - 80px);
    overflow: hidden;
    padding: 20px 0;
    gap: 20px;
    
    // Common column styling
    .column {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      
      .column-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        
        h2 {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: var(--text-color, #333);
        }
        
        .icon-button {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 8px 16px;
          background-color: var(--card-bg, white);
          position: relative;
          border: 1px solid transparent;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          color: transparent;
          background-image: linear-gradient(90deg, #6566CD 0%, #F96CAB 100%);
          -webkit-background-clip: text;
          background-clip: text;
          cursor: pointer;
          transition: all 0.2s ease;
          
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 8px;
            padding: 1px;
            background: linear-gradient(90deg, #6566CD 0%, #F96CAB 100%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
          }
          
          &:hover {
            background-color: var(--bg-muted-hover, #f9f5ff);
          }
          
          svg {
            width: 18px;
            height: 18px;
            fill: none;
            
            path {
              stroke-width: 2;
              stroke: url(#gradient);
            }
          }
        }
      }
      
      .column-content {
        flex-grow: 1;
        overflow-y: auto;
        padding: 0;
        display: flex;
        flex-direction: column;
        height: 100%;
        
        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        
        &::-webkit-scrollbar-track {
          background: var(--scrollbar-track, #f1f1f1);
          border-radius: 4px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: var(--scrollbar-thumb, #d1d1d1);
          border-radius: 4px;
        }
        
        &::-webkit-scrollbar-thumb:hover {
          background: var(--scrollbar-thumb-hover, #b1b1b1);
        }
      }
    }
    
    // Specific column styles for child components
    .activity-column {
      background: transparent;
      box-shadow: none;
      
      app-agent-activity {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }
    
    .playground-column {
      background: var(--card-bg);
      border-radius: 20px;
      box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));
      
      .column-header {
        .header-buttons {
          display: flex;
          flex-direction: row;
          gap: 12px;
          align-items: center;
        }
      }
      
      .playground-content {
        padding: 0;
        height: 100%;
        
        .chat-container {
          padding:20px;
          flex: 1;
          display: flex;
          flex-direction: column;
          height: 100%;
          min-height: 400px;
          border-radius: 12px;
          overflow: hidden;
          position: relative;

          ::ng-deep .prompt-container {
           margin-top: 30px;
          }
          
          .playground-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--dashboard-primary, #6566CD);
            margin: 16px 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          app-chat-interface {
            height: 100%;
            display: flex;
            flex-direction: column;
            flex: 1;
          }
        }
      }
    }
    
    .output-column {
      background-color: var(--card-bg, white);
      border-radius: 20px;
      box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));
      
      app-agent-output {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }
  }
}