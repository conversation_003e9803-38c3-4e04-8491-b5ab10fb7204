import { Injectable, inject } from '@angular/core';
import { Observable, from, throwError, of } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { createLogger } from '../utils/logger';
import { ToastService } from './toast.service';

export interface ScreenshotOptions {
  width?: number;
  height?: number;
  scale?: number;
  backgroundColor?: string;
  allowTaint?: boolean;
  useCORS?: boolean;
  scrollX?: number;
  scrollY?: number;
  windowWidth?: number;
  windowHeight?: number;
}

export interface ScreenshotResult {
  success: boolean;
  canvas?: HTMLCanvasElement;
  dataUrl?: string;
  blob?: Blob;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ScreenshotService {
  private readonly logger = createLogger('ScreenshotService');
  private readonly toastService = inject(ToastService);

  /**
   * Fallback method to capture iframe when content is not accessible
   */
  private captureIframeFallback(
    iframe: HTMLIFrameElement,
    html2canvas: any,
    options: ScreenshotOptions
  ): Observable<ScreenshotResult> {
    this.logger.info('📸 Using iframe fallback capture method');

    // Wait a bit for iframe to load if it hasn't already
    return new Observable<ScreenshotResult>((observer) => {
      const attemptCapture = () => {
        // Try to access iframe content one more time
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

        if (iframeDoc) {
          this.logger.info('📸 Iframe content became accessible, using direct capture');
          // If content is now accessible, use the direct method
          const targetElement = iframeDoc.body || iframeDoc.documentElement;
          if (targetElement) {
            this.captureElementDirectly(targetElement, html2canvas, options).subscribe(observer);
            return;
          }
        }

        // If still no access, capture the iframe container
        this.logger.info('📸 Capturing iframe container as fallback');
        const iframeContainer = iframe.parentElement || iframe;
        this.captureElementDirectly(iframeContainer, html2canvas, options).subscribe(observer);
      };

      // Try immediately first
      attemptCapture();
    });
  }

  /**
   * Direct element capture helper
   */
  private captureElementDirectly(
    element: HTMLElement,
    html2canvas: any,
    options: ScreenshotOptions
  ): Observable<ScreenshotResult> {
    const defaultOptions: any = {
      allowTaint: true,
      useCORS: true,
      scale: options.scale || 1,
      backgroundColor: options.backgroundColor || '#ffffff',
      scrollX: options.scrollX || 0,
      scrollY: options.scrollY || 0,
      width: options.width,
      height: options.height,
      ...options
    };

    this.logger.info('📸 Capturing element directly', {
      elementTagName: element.tagName,
      elementId: element.id,
      elementClass: element.className
    });

    return from(html2canvas(element, defaultOptions) as Promise<HTMLCanvasElement>).pipe(
      switchMap((canvas: HTMLCanvasElement) => {
        this.logger.info('📸 Direct element capture successful', {
          canvasWidth: canvas.width,
          canvasHeight: canvas.height
        });

        if (!canvas || canvas.width === 0 || canvas.height === 0) {
          return of({
            success: false,
            error: 'Generated canvas is invalid or empty'
          });
        }

        const dataUrl = canvas.toDataURL('image/png');

        return new Observable<ScreenshotResult>((observer) => {
          canvas.toBlob((blob) => {
            if (!blob) {
              observer.next({
                success: false,
                error: 'Failed to convert canvas to blob'
              });
            } else {
              observer.next({
                success: true,
                canvas,
                dataUrl,
                blob
              });
            }
            observer.complete();
          }, 'image/png');
        });
      }),
      catchError((error) => {
        this.logger.error('📸 Direct element capture failed', error);
        return of({
          success: false,
          error: error.message || 'Element capture failed'
        });
      })
    );
  }

  /**
   * Dynamically load html2canvas library
   */
  private loadHtml2Canvas(): Observable<any> {
    this.logger.info('📸 Loading html2canvas library...');

    return from(import('html2canvas')).pipe(
      map((module) => {
        const html2canvas = module.default || module;
        this.logger.info('📸 html2canvas library loaded successfully');
        return html2canvas;
      }),
      catchError((error) => {
        this.logger.error('📸 Failed to load html2canvas library', error);
        this.toastService.error('Screenshot feature requires html2canvas library. Please install it to enable screenshots.');
        return throwError(() => new Error('html2canvas library not available'));
      })
    );
  }

  /**
   * Capture screenshot of iframe content
   * This method captures the actual rendered HTML content inside the iframe
   */
  captureIframeContent(
    iframe: HTMLIFrameElement,
    options: ScreenshotOptions = {}
  ): Observable<ScreenshotResult> {
    this.logger.info('📸 Starting iframe content capture', {
      options,
      iframeExists: !!iframe,
      iframeSrc: iframe?.src,
      iframeWidth: iframe?.offsetWidth,
      iframeHeight: iframe?.offsetHeight
    });

    return this.loadHtml2Canvas().pipe(
      switchMap((html2canvas) => {
        try {
          this.logger.info('📸 html2canvas loaded, accessing iframe document...');

          // Get iframe document
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

          this.logger.info('📸 Iframe document access result', {
            hasContentDocument: !!iframe.contentDocument,
            hasContentWindow: !!iframe.contentWindow,
            hasDocument: !!iframeDoc,
            documentReadyState: iframeDoc?.readyState,
            documentURL: iframeDoc?.URL
          });

          if (!iframeDoc) {
            this.logger.warn('📸 Cannot access iframe content directly, trying fallback approach...');
            // Fallback: try to capture the iframe element itself
            return this.captureIframeFallback(iframe, html2canvas, options);
          }

          // Get the body element to capture
          const targetElement = iframeDoc.body || iframeDoc.documentElement;

          this.logger.info('📸 Target element for capture', {
            hasBody: !!iframeDoc.body,
            hasDocumentElement: !!iframeDoc.documentElement,
            targetElementTagName: targetElement?.tagName,
            targetElementChildren: targetElement?.children?.length
          });

          if (!targetElement) {
            const error = 'No content found in iframe';
            this.logger.error('📸 ' + error);
            return of({
              success: false,
              error
            });
          }

          // Default options optimized for iframe content
          const defaultOptions: any = {
            allowTaint: true,
            useCORS: true,
            scale: options.scale || 1,
            backgroundColor: options.backgroundColor || '#ffffff',
            scrollX: options.scrollX || 0,
            scrollY: options.scrollY || 0,
            width: options.width,
            height: options.height,
            ...options
          };

          this.logger.info('📸 Starting html2canvas capture with options', defaultOptions);

          // Capture the iframe content
          return from(html2canvas(targetElement, defaultOptions) as Promise<HTMLCanvasElement>).pipe(
            switchMap((canvas: HTMLCanvasElement) => {
              this.logger.info('📸 Screenshot captured successfully', {
                canvasWidth: canvas.width,
                canvasHeight: canvas.height,
                hasCanvas: !!canvas
              });

              if (!canvas || canvas.width === 0 || canvas.height === 0) {
                const error = 'Generated canvas is invalid or empty';
                this.logger.error('📸 ' + error);
                return of({
                  success: false,
                  error
                });
              }

              // Convert to data URL
              const dataUrl = canvas.toDataURL('image/png');
              this.logger.info('📸 Canvas converted to data URL', {
                dataUrlLength: dataUrl.length,
                dataUrlPrefix: dataUrl.substring(0, 50)
              });

              // Convert to blob for download
              return new Observable<ScreenshotResult>((observer) => {
                canvas.toBlob((blob) => {
                  this.logger.info('📸 Canvas converted to blob', {
                    hasBlobData: !!blob,
                    blobSize: blob?.size,
                    blobType: blob?.type
                  });

                  if (!blob) {
                    observer.next({
                      success: false,
                      error: 'Failed to convert canvas to blob'
                    });
                  } else {
                    observer.next({
                      success: true,
                      canvas,
                      dataUrl,
                      blob
                    });
                  }
                  observer.complete();
                }, 'image/png');
              });
            }),
            catchError((error) => {
              this.logger.error('📸 Screenshot capture failed', error);
              return of({
                success: false,
                error: error.message || 'Screenshot capture failed'
              });
            })
          );
        } catch (error: any) {
          this.logger.error('📸 Screenshot setup failed', error);
          return of({
            success: false,
            error: error.message || 'Screenshot setup failed'
          });
        }
      }),
      catchError((error) => {
        this.logger.error('📸 Failed to load html2canvas library', error);
        return of({
          success: false,
          error: 'Failed to load screenshot library'
        });
      })
    );
  }

  /**
   * Capture screenshot of any DOM element
   */
  captureElement(
    element: HTMLElement,
    options: ScreenshotOptions = {}
  ): Observable<ScreenshotResult> {
    this.logger.info('📸 Starting element capture', { options });

    return this.loadHtml2Canvas().pipe(
      switchMap((html2canvas) => {
        const defaultOptions: any = {
          allowTaint: true,
          useCORS: true,
          scale: 1,
          backgroundColor: '#ffffff',
          ...options
        };

        return from(html2canvas(element, defaultOptions) as Promise<HTMLCanvasElement>).pipe(
          switchMap((canvas: HTMLCanvasElement) => {
            this.logger.info('📸 Element screenshot captured successfully', {
              width: canvas.width,
              height: canvas.height
            });

            const dataUrl = canvas.toDataURL('image/png');

            return new Observable<ScreenshotResult>((observer) => {
              canvas.toBlob((blob) => {
                observer.next({
                  success: true,
                  canvas,
                  dataUrl,
                  blob: blob || undefined
                });
                observer.complete();
              }, 'image/png');
            });
          }),
          catchError((error) => {
            this.logger.error('📸 Element screenshot failed', error);
            return of({
              success: false,
              error: error.message || 'Element screenshot failed'
            });
          })
        );
      }),
      catchError((error) => {
        this.logger.error('📸 Failed to load html2canvas library', error);
        return of({
          success: false,
          error: 'Failed to load screenshot library'
        });
      })
    );
  }

  /**
   * Download screenshot as PNG file
   */
  downloadScreenshot(
    screenshotResult: ScreenshotResult,
    filename: string = 'screenshot.png'
  ): void {
    this.logger.info('📸 Download screenshot called', {
      resultSuccess: screenshotResult.success,
      hasBlob: !!screenshotResult.blob,
      blobSize: screenshotResult.blob?.size,
      filename,
      error: screenshotResult.error
    });

    if (!screenshotResult.success) {
      const errorMsg = `Cannot download screenshot - capture failed: ${screenshotResult.error || 'Unknown error'}`;
      this.logger.error('📸 ' + errorMsg);
      this.toastService.error(errorMsg);
      return;
    }

    if (!screenshotResult.blob) {
      const errorMsg = 'Cannot download screenshot - no valid image data (blob is missing)';
      this.logger.error('📸 ' + errorMsg);
      this.toastService.error(errorMsg);
      return;
    }

    try {
      // Create download link
      const url = URL.createObjectURL(screenshotResult.blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      this.logger.info('📸 Screenshot downloaded successfully', { filename });
      this.toastService.success(`Screenshot saved as ${filename}`);
    } catch (error: any) {
      this.logger.error('📸 Screenshot download failed', error);
      this.toastService.error('Failed to download screenshot');
    }
  }

  /**
   * Wait for iframe to be ready for capture
   */
  private waitForIframeReady(iframe: HTMLIFrameElement): Observable<boolean> {
    return new Observable<boolean>((observer) => {
      const checkIframe = () => {
        try {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

          if (iframeDoc && iframeDoc.readyState === 'complete') {
            this.logger.info('📸 Iframe is ready for capture');
            observer.next(true);
            observer.complete();
            return;
          }
        } catch (error) {
          // Cross-origin or other access issues
          this.logger.warn('📸 Cannot access iframe document, proceeding anyway');
          observer.next(true);
          observer.complete();
          return;
        }

        // If not ready, wait a bit and try again
        setTimeout(checkIframe, 100);
      };

      // Start checking
      checkIframe();

      // Timeout after 5 seconds
      setTimeout(() => {
        this.logger.warn('📸 Iframe ready check timed out, proceeding anyway');
        observer.next(true);
        observer.complete();
      }, 5000);
    });
  }

  /**
   * Capture and download iframe content in one step
   */
  captureAndDownloadIframe(
    iframe: HTMLIFrameElement,
    filename: string = 'page-screenshot.png',
    options: ScreenshotOptions = {}
  ): Observable<void> {
    this.logger.info('📸 Starting capture and download process');

    return this.waitForIframeReady(iframe).pipe(
      switchMap(() => this.captureIframeContent(iframe, options)),
      map((result) => {
        this.downloadScreenshot(result, filename);
      }),
      catchError((error) => {
        this.logger.error('📸 Capture and download failed', error);
        this.toastService.error('Failed to capture screenshot');
        return throwError(() => error);
      })
    );
  }

  /**
   * Generate filename based on page title and timestamp
   */
  generateFilename(pageTitle: string = 'page'): string {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
    const sanitizedTitle = pageTitle.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
    return `${sanitizedTitle}-${timestamp}.png`;
  }

  /**
   * Get optimal screenshot options for mobile frames
   */
  getMobileFrameOptions(): ScreenshotOptions {
    return {
      width: 420,
      height: 720,
      scale: 2, // Higher resolution for better quality
      backgroundColor: '#ffffff',
      allowTaint: true,
      useCORS: true
    };
  }

  /**
   * Get optimal screenshot options for canvas nodes
   */
  getCanvasNodeOptions(): ScreenshotOptions {
    return {
      width: 420,
      height: 720,
      scale: 2, // Higher resolution for better quality
      backgroundColor: '#ffffff',
      allowTaint: true,
      useCORS: true
    };
  }

  /**
   * Test method to verify html2canvas is working
   */
  testScreenshotCapability(): Observable<boolean> {
    this.logger.info('📸 Testing screenshot capability...');

    return this.loadHtml2Canvas().pipe(
      switchMap((html2canvas) => {
        // Create a simple test element
        const testDiv = document.createElement('div');
        testDiv.style.width = '100px';
        testDiv.style.height = '100px';
        testDiv.style.backgroundColor = '#ff0000';
        testDiv.style.position = 'absolute';
        testDiv.style.top = '-1000px';
        testDiv.innerHTML = 'Test';

        document.body.appendChild(testDiv);

        return from(html2canvas(testDiv) as Promise<HTMLCanvasElement>).pipe(
          map((canvas) => {
            document.body.removeChild(testDiv);
            const success = canvas && canvas.width > 0 && canvas.height > 0;
            this.logger.info('📸 Screenshot test result', {
              success,
              canvasWidth: canvas?.width,
              canvasHeight: canvas?.height
            });
            return success;
          }),
          catchError((error) => {
            document.body.removeChild(testDiv);
            this.logger.error('📸 Screenshot test failed', error);
            return of(false);
          })
        );
      }),
      catchError((error) => {
        this.logger.error('📸 Screenshot capability test failed', error);
        return of(false);
      })
    );
  }
}
