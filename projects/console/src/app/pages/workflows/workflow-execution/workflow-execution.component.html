<div class="workflow-execution-container">
  <!-- SVG Gradient Definitions for Icons -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#6566CD" />
        <stop offset="100%" stop-color="#F96CAB" />
      </linearGradient>
    </defs>
  </svg>

  <!-- Main content section with 3 columns -->
  <div class="execution-content" role="main">
    <!-- Left Column: Agent Activity -->
    <div
      class="column activity-column"
      role="region"
      aria-label="Agent Activity"
    >
      <!-- Agent Activity Component -->
      <app-agent-activity
        [activityLogs]="activityLogs"
        [executionDetails]="executionDetails"
        [progress]="activityProgress"
        [isRunning]="isRunning"
        (saveLogs)="saveLogs()"
        (controlAction)="handleControlAction($event)"
      ></app-agent-activity>
    </div>

    <!-- Middle Column: Workflow Playground -->
    <div
      class="column playground-column"
      role="region"
      aria-label="Workflow Playground"
    >
      <div class="column-content playground-content">
        <div class="column-header">
          <h2 id="playground-title">Workflow Playground</h2>
          <div class="header-buttons">
            <button
              class="icon-button"
              (click)="editWorkflow()"
              aria-label="Edit workflow"
            >
              Edit
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="icon-svg"
                aria-hidden="true"
              >
                <path
                  d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"
                  stroke="url(#gradient)"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"
                  stroke="url(#gradient)"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
            <button
              class="icon-button"
              (click)="exportResults('output')"
              aria-label="Export results"
            >
              Export
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="icon-svg"
                aria-hidden="true"
              >
                <path
                  d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"
                  stroke="url(#gradient)"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M7 10l5 5 5-5M12 15V3"
                  stroke="url(#gradient)"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
        <div class="chat-container" aria-labelledby="playground-title">
          <!-- Chat Interface Component -->
          <app-chat-interface
            [messages]="chatMessages"
            [isLoading]="isProcessingChat"
            (messageSent)="handleChatMessage($event)"
          ></app-chat-interface>
        </div>
      </div>
    </div>

    <!-- Right Column: Agent Output -->
    <div class="column output-column" role="region" aria-label="Agent Output">
      <div class="column-content">
        <!-- Agent Output Component -->
        <app-agent-output
          [outputs]="agentOutputs"
          (export)="exportResults('output')"
        ></app-agent-output>
      </div>
    </div>
  </div>
</div>
