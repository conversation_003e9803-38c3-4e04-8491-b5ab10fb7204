import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dropdown-item',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dropdown-item.component.html',
  styleUrl: './dropdown-item.component.scss'
})
export class DropdownItemComponent {
  @Input() icon: string = '';
  @Input() label: string = '';
  @Input() description: string = '';
  @Input() route: string = '';
  @Output() itemClick = new EventEmitter<{route: string, label: string}>();
  
  constructor(private router: Router) {}
  
  // Check if this item is active based on the current route
  get isActive(): boolean {
    return this.router.url === this.route;
  }

  onClick(event: MouseEvent): void {
    // Stop the event from bubbling up to parent elements
    event.stopPropagation();
    event.preventDefault();
    
    console.log(`Dropdown item clicked: ${this.label}`);
    
    this.itemClick.emit({
      route: this.route,
      label: this.label
    });
  }
} 